{"version": 3, "sources": ["src/app/projects/projects.component.ts"], "sourcesContent": ["import { Component, inject } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { AuthService } from '../services/auth.service';\n\n// Carbon Design System imports\nimport { ButtonModule } from 'carbon-components-angular/button';\n\n@Component({\n  selector: 'app-projects',\n  standalone: true,\n  imports: [\n    CommonModule,\n    ButtonModule\n  ],\n  template: `\n    <div class=\"projects-container\">\n      <header class=\"projects-header\">\n        <h1>Projects Dashboard</h1>\n        <button \n          ibmButton=\"primary\" \n          size=\"md\"\n          (click)=\"signOut()\">\n          Sign Out\n        </button>\n      </header>\n      \n      <main class=\"projects-content\">\n        <p>Welcome to your projects dashboard!</p>\n        <p *ngIf=\"auth.user$ | async as user\">\n          Logged in as: {{ user.email }}\n        </p>\n      </main>\n    </div>\n  `,\n  styles: [`\n    .projects-container {\n      padding: 2rem;\n      max-width: 1200px;\n      margin: 0 auto;\n    }\n    \n    .projects-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: 2rem;\n      padding-bottom: 1rem;\n      border-bottom: 1px solid #e0e0e0;\n    }\n    \n    .projects-header h1 {\n      margin: 0;\n      font-size: 2rem;\n      font-weight: 400;\n    }\n    \n    .projects-content {\n      padding: 1rem 0;\n    }\n  `]\n})\nexport class ProjectsComponent {\n  protected auth = inject(AuthService);\n\n  async signOut() {\n    try {\n      await this.auth.signOut();\n    } catch (error) {\n      console.error('Sign out error:', error);\n    }\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BQ,IAAA,yBAAA,GAAA,GAAA;AACE,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;;;;AADE,IAAA,oBAAA;AAAA,IAAA,6BAAA,mBAAA,QAAA,OAAA,GAAA;;;AAgCJ,IAAO,oBAAP,MAAO,mBAAiB;EAClB,OAAO,OAAO,WAAW;EAEnC,MAAM,UAAO;AACX,QAAI;AACF,YAAM,KAAK,KAAK,QAAO;IACzB,SAAS,OAAO;AACd,cAAQ,MAAM,mBAAmB,KAAK;IACxC;EACF;;qCATW,oBAAiB;EAAA;yEAAjB,oBAAiB,WAAA,CAAA,CAAA,cAAA,CAAA,GAAA,OAAA,IAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,oBAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,aAAA,WAAA,QAAA,MAAA,GAAA,OAAA,GAAA,CAAA,GAAA,kBAAA,GAAA,CAAA,GAAA,MAAA,CAAA,GAAA,UAAA,SAAA,2BAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;AA9C1B,MAAA,yBAAA,GAAA,OAAA,CAAA,EAAgC,GAAA,UAAA,CAAA,EACE,GAAA,IAAA;AAC1B,MAAA,iBAAA,GAAA,oBAAA;AAAkB,MAAA,uBAAA;AACtB,MAAA,yBAAA,GAAA,UAAA,CAAA;AAGE,MAAA,qBAAA,SAAA,SAAA,qDAAA;AAAA,eAAS,IAAA,QAAA;MAAS,CAAA;AAClB,MAAA,iBAAA,GAAA,YAAA;AACF,MAAA,uBAAA,EAAS;AAGX,MAAA,yBAAA,GAAA,QAAA,CAAA,EAA+B,GAAA,GAAA;AAC1B,MAAA,iBAAA,GAAA,qCAAA;AAAmC,MAAA,uBAAA;AACtC,MAAA,qBAAA,GAAA,gCAAA,GAAA,GAAA,KAAA,CAAA;;AAGF,MAAA,uBAAA,EAAO;;;AAHD,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,QAAA,sBAAA,IAAA,GAAA,IAAA,KAAA,KAAA,CAAA;;oBAjBR,cAAY,MACZ,cAAY,QAAA,SAAA,GAAA,QAAA,CAAA,qiBAAA,EAAA,CAAA;;;sEAiDH,mBAAiB,CAAA;UAtD7B;uBACW,gBAAc,YACZ,MAAI,SACP;MACP;MACA;OACD,UACS;;;;;;;;;;;;;;;;;;;KAmBT,QAAA,CAAA,woBAAA,EAAA,CAAA;;;;6EA4BU,mBAAiB,EAAA,WAAA,qBAAA,UAAA,0CAAA,YAAA,GAAA,CAAA;AAAA,GAAA;", "names": []}