import { TestBed } from '@angular/core/testing';
import { AuthService } from './auth.service';
import { provideFirebaseApp, initializeApp } from '@angular/fire/app';
import { provideAuth, getAuth } from '@angular/fire/auth';

describe('AuthService', () => {
  let service: AuthService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        provideFirebaseApp(() => initializeApp({
          apiKey: "test-api-key",
          authDomain: "test.firebaseapp.com",
          projectId: "test-project",
          storageBucket: "test.appspot.com",
          messagingSenderId: "123456789",
          appId: "1:123456789:web:test"
        })),
        provideAuth(() => getAuth()),
      ]
    });
    service = TestBed.inject(AuthService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should have user$ observable', () => {
    expect(service.user$).toBeDefined();
  });

  it('should have uid$ observable', () => {
    expect(service.uid$).toBeDefined();
  });

  it('should have signIn method', () => {
    expect(service.signIn).toBeDefined();
    expect(typeof service.signIn).toBe('function');
  });

  it('should have signOut method', () => {
    expect(service.signOut).toBeDefined();
    expect(typeof service.signOut).toBe('function');
  });
});
