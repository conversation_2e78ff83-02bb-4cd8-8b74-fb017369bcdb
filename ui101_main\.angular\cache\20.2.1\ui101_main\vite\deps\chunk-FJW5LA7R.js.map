{"version": 3, "sources": ["../../../../../../node_modules/@floating-ui/utils/dist/floating-ui.utils.mjs", "../../../../../../node_modules/@floating-ui/core/dist/floating-ui.core.mjs", "../../../../../../node_modules/@floating-ui/utils/dist/floating-ui.utils.dom.mjs", "../../../../../../node_modules/@floating-ui/dom/dist/floating-ui.dom.mjs", "../../../../../../node_modules/carbon-components-angular/fesm2020/carbon-components-angular-popover.mjs", "../../../../../../node_modules/carbon-components-angular/fesm2020/carbon-components-angular-tooltip.mjs", "../../../../../../node_modules/carbon-components-angular/fesm2020/carbon-components-angular-button.mjs"], "sourcesContent": ["/**\n * Custom positioning reference element.\n * @see https://floating-ui.com/docs/virtual-elements\n */\n\nconst sides = ['top', 'right', 'bottom', 'left'];\nconst alignments = ['start', 'end'];\nconst placements = /*#__PURE__*/sides.reduce((acc, side) => acc.concat(side, side + \"-\" + alignments[0], side + \"-\" + alignments[1]), []);\nconst min = Math.min;\nconst max = Math.max;\nconst round = Math.round;\nconst floor = Math.floor;\nconst createCoords = v => ({\n  x: v,\n  y: v\n});\nconst oppositeSideMap = {\n  left: 'right',\n  right: 'left',\n  bottom: 'top',\n  top: 'bottom'\n};\nconst oppositeAlignmentMap = {\n  start: 'end',\n  end: 'start'\n};\nfunction clamp(start, value, end) {\n  return max(start, min(value, end));\n}\nfunction evaluate(value, param) {\n  return typeof value === 'function' ? value(param) : value;\n}\nfunction getSide(placement) {\n  return placement.split('-')[0];\n}\nfunction getAlignment(placement) {\n  return placement.split('-')[1];\n}\nfunction getOppositeAxis(axis) {\n  return axis === 'x' ? 'y' : 'x';\n}\nfunction getAxisLength(axis) {\n  return axis === 'y' ? 'height' : 'width';\n}\nconst yAxisSides = /*#__PURE__*/new Set(['top', 'bottom']);\nfunction getSideAxis(placement) {\n  return yAxisSides.has(getSide(placement)) ? 'y' : 'x';\n}\nfunction getAlignmentAxis(placement) {\n  return getOppositeAxis(getSideAxis(placement));\n}\nfunction getAlignmentSides(placement, rects, rtl) {\n  if (rtl === void 0) {\n    rtl = false;\n  }\n  const alignment = getAlignment(placement);\n  const alignmentAxis = getAlignmentAxis(placement);\n  const length = getAxisLength(alignmentAxis);\n  let mainAlignmentSide = alignmentAxis === 'x' ? alignment === (rtl ? 'end' : 'start') ? 'right' : 'left' : alignment === 'start' ? 'bottom' : 'top';\n  if (rects.reference[length] > rects.floating[length]) {\n    mainAlignmentSide = getOppositePlacement(mainAlignmentSide);\n  }\n  return [mainAlignmentSide, getOppositePlacement(mainAlignmentSide)];\n}\nfunction getExpandedPlacements(placement) {\n  const oppositePlacement = getOppositePlacement(placement);\n  return [getOppositeAlignmentPlacement(placement), oppositePlacement, getOppositeAlignmentPlacement(oppositePlacement)];\n}\nfunction getOppositeAlignmentPlacement(placement) {\n  return placement.replace(/start|end/g, alignment => oppositeAlignmentMap[alignment]);\n}\nconst lrPlacement = ['left', 'right'];\nconst rlPlacement = ['right', 'left'];\nconst tbPlacement = ['top', 'bottom'];\nconst btPlacement = ['bottom', 'top'];\nfunction getSideList(side, isStart, rtl) {\n  switch (side) {\n    case 'top':\n    case 'bottom':\n      if (rtl) return isStart ? rlPlacement : lrPlacement;\n      return isStart ? lrPlacement : rlPlacement;\n    case 'left':\n    case 'right':\n      return isStart ? tbPlacement : btPlacement;\n    default:\n      return [];\n  }\n}\nfunction getOppositeAxisPlacements(placement, flipAlignment, direction, rtl) {\n  const alignment = getAlignment(placement);\n  let list = getSideList(getSide(placement), direction === 'start', rtl);\n  if (alignment) {\n    list = list.map(side => side + \"-\" + alignment);\n    if (flipAlignment) {\n      list = list.concat(list.map(getOppositeAlignmentPlacement));\n    }\n  }\n  return list;\n}\nfunction getOppositePlacement(placement) {\n  return placement.replace(/left|right|bottom|top/g, side => oppositeSideMap[side]);\n}\nfunction expandPaddingObject(padding) {\n  return {\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0,\n    ...padding\n  };\n}\nfunction getPaddingObject(padding) {\n  return typeof padding !== 'number' ? expandPaddingObject(padding) : {\n    top: padding,\n    right: padding,\n    bottom: padding,\n    left: padding\n  };\n}\nfunction rectToClientRect(rect) {\n  const {\n    x,\n    y,\n    width,\n    height\n  } = rect;\n  return {\n    width,\n    height,\n    top: y,\n    left: x,\n    right: x + width,\n    bottom: y + height,\n    x,\n    y\n  };\n}\n\nexport { alignments, clamp, createCoords, evaluate, expandPaddingObject, floor, getAlignment, getAlignmentAxis, getAlignmentSides, getAxisLength, getExpandedPlacements, getOppositeAlignmentPlacement, getOppositeAxis, getOppositeAxisPlacements, getOppositePlacement, getPaddingObject, getSide, getSideAxis, max, min, placements, rectToClientRect, round, sides };\n", "import { getSideAxis, getAlignmentAxis, getAxisLength, getSide, getAlignment, evaluate, getPaddingObject, rectToClientRect, min, clamp, placements, getAlignmentSides, getOppositeAlignmentPlacement, getOppositePlacement, getExpandedPlacements, getOppositeAxisPlacements, sides, max, getOppositeAxis } from '@floating-ui/utils';\nexport { rectToClientRect } from '@floating-ui/utils';\n\nfunction computeCoordsFromPlacement(_ref, placement, rtl) {\n  let {\n    reference,\n    floating\n  } = _ref;\n  const sideAxis = getSideAxis(placement);\n  const alignmentAxis = getAlignmentAxis(placement);\n  const alignLength = getAxisLength(alignmentAxis);\n  const side = getSide(placement);\n  const isVertical = sideAxis === 'y';\n  const commonX = reference.x + reference.width / 2 - floating.width / 2;\n  const commonY = reference.y + reference.height / 2 - floating.height / 2;\n  const commonAlign = reference[alignLength] / 2 - floating[alignLength] / 2;\n  let coords;\n  switch (side) {\n    case 'top':\n      coords = {\n        x: commonX,\n        y: reference.y - floating.height\n      };\n      break;\n    case 'bottom':\n      coords = {\n        x: commonX,\n        y: reference.y + reference.height\n      };\n      break;\n    case 'right':\n      coords = {\n        x: reference.x + reference.width,\n        y: commonY\n      };\n      break;\n    case 'left':\n      coords = {\n        x: reference.x - floating.width,\n        y: commonY\n      };\n      break;\n    default:\n      coords = {\n        x: reference.x,\n        y: reference.y\n      };\n  }\n  switch (getAlignment(placement)) {\n    case 'start':\n      coords[alignmentAxis] -= commonAlign * (rtl && isVertical ? -1 : 1);\n      break;\n    case 'end':\n      coords[alignmentAxis] += commonAlign * (rtl && isVertical ? -1 : 1);\n      break;\n  }\n  return coords;\n}\n\n/**\n * Computes the `x` and `y` coordinates that will place the floating element\n * next to a given reference element.\n *\n * This export does not have any `platform` interface logic. You will need to\n * write one for the platform you are using Floating UI with.\n */\nconst computePosition = async (reference, floating, config) => {\n  const {\n    placement = 'bottom',\n    strategy = 'absolute',\n    middleware = [],\n    platform\n  } = config;\n  const validMiddleware = middleware.filter(Boolean);\n  const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(floating));\n  let rects = await platform.getElementRects({\n    reference,\n    floating,\n    strategy\n  });\n  let {\n    x,\n    y\n  } = computeCoordsFromPlacement(rects, placement, rtl);\n  let statefulPlacement = placement;\n  let middlewareData = {};\n  let resetCount = 0;\n  for (let i = 0; i < validMiddleware.length; i++) {\n    const {\n      name,\n      fn\n    } = validMiddleware[i];\n    const {\n      x: nextX,\n      y: nextY,\n      data,\n      reset\n    } = await fn({\n      x,\n      y,\n      initialPlacement: placement,\n      placement: statefulPlacement,\n      strategy,\n      middlewareData,\n      rects,\n      platform,\n      elements: {\n        reference,\n        floating\n      }\n    });\n    x = nextX != null ? nextX : x;\n    y = nextY != null ? nextY : y;\n    middlewareData = {\n      ...middlewareData,\n      [name]: {\n        ...middlewareData[name],\n        ...data\n      }\n    };\n    if (reset && resetCount <= 50) {\n      resetCount++;\n      if (typeof reset === 'object') {\n        if (reset.placement) {\n          statefulPlacement = reset.placement;\n        }\n        if (reset.rects) {\n          rects = reset.rects === true ? await platform.getElementRects({\n            reference,\n            floating,\n            strategy\n          }) : reset.rects;\n        }\n        ({\n          x,\n          y\n        } = computeCoordsFromPlacement(rects, statefulPlacement, rtl));\n      }\n      i = -1;\n    }\n  }\n  return {\n    x,\n    y,\n    placement: statefulPlacement,\n    strategy,\n    middlewareData\n  };\n};\n\n/**\n * Resolves with an object of overflow side offsets that determine how much the\n * element is overflowing a given clipping boundary on each side.\n * - positive = overflowing the boundary by that number of pixels\n * - negative = how many pixels left before it will overflow\n * - 0 = lies flush with the boundary\n * @see https://floating-ui.com/docs/detectOverflow\n */\nasync function detectOverflow(state, options) {\n  var _await$platform$isEle;\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    x,\n    y,\n    platform,\n    rects,\n    elements,\n    strategy\n  } = state;\n  const {\n    boundary = 'clippingAncestors',\n    rootBoundary = 'viewport',\n    elementContext = 'floating',\n    altBoundary = false,\n    padding = 0\n  } = evaluate(options, state);\n  const paddingObject = getPaddingObject(padding);\n  const altContext = elementContext === 'floating' ? 'reference' : 'floating';\n  const element = elements[altBoundary ? altContext : elementContext];\n  const clippingClientRect = rectToClientRect(await platform.getClippingRect({\n    element: ((_await$platform$isEle = await (platform.isElement == null ? void 0 : platform.isElement(element))) != null ? _await$platform$isEle : true) ? element : element.contextElement || (await (platform.getDocumentElement == null ? void 0 : platform.getDocumentElement(elements.floating))),\n    boundary,\n    rootBoundary,\n    strategy\n  }));\n  const rect = elementContext === 'floating' ? {\n    x,\n    y,\n    width: rects.floating.width,\n    height: rects.floating.height\n  } : rects.reference;\n  const offsetParent = await (platform.getOffsetParent == null ? void 0 : platform.getOffsetParent(elements.floating));\n  const offsetScale = (await (platform.isElement == null ? void 0 : platform.isElement(offsetParent))) ? (await (platform.getScale == null ? void 0 : platform.getScale(offsetParent))) || {\n    x: 1,\n    y: 1\n  } : {\n    x: 1,\n    y: 1\n  };\n  const elementClientRect = rectToClientRect(platform.convertOffsetParentRelativeRectToViewportRelativeRect ? await platform.convertOffsetParentRelativeRectToViewportRelativeRect({\n    elements,\n    rect,\n    offsetParent,\n    strategy\n  }) : rect);\n  return {\n    top: (clippingClientRect.top - elementClientRect.top + paddingObject.top) / offsetScale.y,\n    bottom: (elementClientRect.bottom - clippingClientRect.bottom + paddingObject.bottom) / offsetScale.y,\n    left: (clippingClientRect.left - elementClientRect.left + paddingObject.left) / offsetScale.x,\n    right: (elementClientRect.right - clippingClientRect.right + paddingObject.right) / offsetScale.x\n  };\n}\n\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * @see https://floating-ui.com/docs/arrow\n */\nconst arrow = options => ({\n  name: 'arrow',\n  options,\n  async fn(state) {\n    const {\n      x,\n      y,\n      placement,\n      rects,\n      platform,\n      elements,\n      middlewareData\n    } = state;\n    // Since `element` is required, we don't Partial<> the type.\n    const {\n      element,\n      padding = 0\n    } = evaluate(options, state) || {};\n    if (element == null) {\n      return {};\n    }\n    const paddingObject = getPaddingObject(padding);\n    const coords = {\n      x,\n      y\n    };\n    const axis = getAlignmentAxis(placement);\n    const length = getAxisLength(axis);\n    const arrowDimensions = await platform.getDimensions(element);\n    const isYAxis = axis === 'y';\n    const minProp = isYAxis ? 'top' : 'left';\n    const maxProp = isYAxis ? 'bottom' : 'right';\n    const clientProp = isYAxis ? 'clientHeight' : 'clientWidth';\n    const endDiff = rects.reference[length] + rects.reference[axis] - coords[axis] - rects.floating[length];\n    const startDiff = coords[axis] - rects.reference[axis];\n    const arrowOffsetParent = await (platform.getOffsetParent == null ? void 0 : platform.getOffsetParent(element));\n    let clientSize = arrowOffsetParent ? arrowOffsetParent[clientProp] : 0;\n\n    // DOM platform can return `window` as the `offsetParent`.\n    if (!clientSize || !(await (platform.isElement == null ? void 0 : platform.isElement(arrowOffsetParent)))) {\n      clientSize = elements.floating[clientProp] || rects.floating[length];\n    }\n    const centerToReference = endDiff / 2 - startDiff / 2;\n\n    // If the padding is large enough that it causes the arrow to no longer be\n    // centered, modify the padding so that it is centered.\n    const largestPossiblePadding = clientSize / 2 - arrowDimensions[length] / 2 - 1;\n    const minPadding = min(paddingObject[minProp], largestPossiblePadding);\n    const maxPadding = min(paddingObject[maxProp], largestPossiblePadding);\n\n    // Make sure the arrow doesn't overflow the floating element if the center\n    // point is outside the floating element's bounds.\n    const min$1 = minPadding;\n    const max = clientSize - arrowDimensions[length] - maxPadding;\n    const center = clientSize / 2 - arrowDimensions[length] / 2 + centerToReference;\n    const offset = clamp(min$1, center, max);\n\n    // If the reference is small enough that the arrow's padding causes it to\n    // to point to nothing for an aligned placement, adjust the offset of the\n    // floating element itself. To ensure `shift()` continues to take action,\n    // a single reset is performed when this is true.\n    const shouldAddOffset = !middlewareData.arrow && getAlignment(placement) != null && center !== offset && rects.reference[length] / 2 - (center < min$1 ? minPadding : maxPadding) - arrowDimensions[length] / 2 < 0;\n    const alignmentOffset = shouldAddOffset ? center < min$1 ? center - min$1 : center - max : 0;\n    return {\n      [axis]: coords[axis] + alignmentOffset,\n      data: {\n        [axis]: offset,\n        centerOffset: center - offset - alignmentOffset,\n        ...(shouldAddOffset && {\n          alignmentOffset\n        })\n      },\n      reset: shouldAddOffset\n    };\n  }\n});\n\nfunction getPlacementList(alignment, autoAlignment, allowedPlacements) {\n  const allowedPlacementsSortedByAlignment = alignment ? [...allowedPlacements.filter(placement => getAlignment(placement) === alignment), ...allowedPlacements.filter(placement => getAlignment(placement) !== alignment)] : allowedPlacements.filter(placement => getSide(placement) === placement);\n  return allowedPlacementsSortedByAlignment.filter(placement => {\n    if (alignment) {\n      return getAlignment(placement) === alignment || (autoAlignment ? getOppositeAlignmentPlacement(placement) !== placement : false);\n    }\n    return true;\n  });\n}\n/**\n * Optimizes the visibility of the floating element by choosing the placement\n * that has the most space available automatically, without needing to specify a\n * preferred placement. Alternative to `flip`.\n * @see https://floating-ui.com/docs/autoPlacement\n */\nconst autoPlacement = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'autoPlacement',\n    options,\n    async fn(state) {\n      var _middlewareData$autoP, _middlewareData$autoP2, _placementsThatFitOnE;\n      const {\n        rects,\n        middlewareData,\n        placement,\n        platform,\n        elements\n      } = state;\n      const {\n        crossAxis = false,\n        alignment,\n        allowedPlacements = placements,\n        autoAlignment = true,\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      const placements$1 = alignment !== undefined || allowedPlacements === placements ? getPlacementList(alignment || null, autoAlignment, allowedPlacements) : allowedPlacements;\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const currentIndex = ((_middlewareData$autoP = middlewareData.autoPlacement) == null ? void 0 : _middlewareData$autoP.index) || 0;\n      const currentPlacement = placements$1[currentIndex];\n      if (currentPlacement == null) {\n        return {};\n      }\n      const alignmentSides = getAlignmentSides(currentPlacement, rects, await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating)));\n\n      // Make `computeCoords` start from the right place.\n      if (placement !== currentPlacement) {\n        return {\n          reset: {\n            placement: placements$1[0]\n          }\n        };\n      }\n      const currentOverflows = [overflow[getSide(currentPlacement)], overflow[alignmentSides[0]], overflow[alignmentSides[1]]];\n      const allOverflows = [...(((_middlewareData$autoP2 = middlewareData.autoPlacement) == null ? void 0 : _middlewareData$autoP2.overflows) || []), {\n        placement: currentPlacement,\n        overflows: currentOverflows\n      }];\n      const nextPlacement = placements$1[currentIndex + 1];\n\n      // There are more placements to check.\n      if (nextPlacement) {\n        return {\n          data: {\n            index: currentIndex + 1,\n            overflows: allOverflows\n          },\n          reset: {\n            placement: nextPlacement\n          }\n        };\n      }\n      const placementsSortedByMostSpace = allOverflows.map(d => {\n        const alignment = getAlignment(d.placement);\n        return [d.placement, alignment && crossAxis ?\n        // Check along the mainAxis and main crossAxis side.\n        d.overflows.slice(0, 2).reduce((acc, v) => acc + v, 0) :\n        // Check only the mainAxis.\n        d.overflows[0], d.overflows];\n      }).sort((a, b) => a[1] - b[1]);\n      const placementsThatFitOnEachSide = placementsSortedByMostSpace.filter(d => d[2].slice(0,\n      // Aligned placements should not check their opposite crossAxis\n      // side.\n      getAlignment(d[0]) ? 2 : 3).every(v => v <= 0));\n      const resetPlacement = ((_placementsThatFitOnE = placementsThatFitOnEachSide[0]) == null ? void 0 : _placementsThatFitOnE[0]) || placementsSortedByMostSpace[0][0];\n      if (resetPlacement !== placement) {\n        return {\n          data: {\n            index: currentIndex + 1,\n            overflows: allOverflows\n          },\n          reset: {\n            placement: resetPlacement\n          }\n        };\n      }\n      return {};\n    }\n  };\n};\n\n/**\n * Optimizes the visibility of the floating element by flipping the `placement`\n * in order to keep it in view when the preferred placement(s) will overflow the\n * clipping boundary. Alternative to `autoPlacement`.\n * @see https://floating-ui.com/docs/flip\n */\nconst flip = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'flip',\n    options,\n    async fn(state) {\n      var _middlewareData$arrow, _middlewareData$flip;\n      const {\n        placement,\n        middlewareData,\n        rects,\n        initialPlacement,\n        platform,\n        elements\n      } = state;\n      const {\n        mainAxis: checkMainAxis = true,\n        crossAxis: checkCrossAxis = true,\n        fallbackPlacements: specifiedFallbackPlacements,\n        fallbackStrategy = 'bestFit',\n        fallbackAxisSideDirection = 'none',\n        flipAlignment = true,\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n\n      // If a reset by the arrow was caused due to an alignment offset being\n      // added, we should skip any logic now since `flip()` has already done its\n      // work.\n      // https://github.com/floating-ui/floating-ui/issues/2549#issuecomment-1719601643\n      if ((_middlewareData$arrow = middlewareData.arrow) != null && _middlewareData$arrow.alignmentOffset) {\n        return {};\n      }\n      const side = getSide(placement);\n      const initialSideAxis = getSideAxis(initialPlacement);\n      const isBasePlacement = getSide(initialPlacement) === initialPlacement;\n      const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating));\n      const fallbackPlacements = specifiedFallbackPlacements || (isBasePlacement || !flipAlignment ? [getOppositePlacement(initialPlacement)] : getExpandedPlacements(initialPlacement));\n      const hasFallbackAxisSideDirection = fallbackAxisSideDirection !== 'none';\n      if (!specifiedFallbackPlacements && hasFallbackAxisSideDirection) {\n        fallbackPlacements.push(...getOppositeAxisPlacements(initialPlacement, flipAlignment, fallbackAxisSideDirection, rtl));\n      }\n      const placements = [initialPlacement, ...fallbackPlacements];\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const overflows = [];\n      let overflowsData = ((_middlewareData$flip = middlewareData.flip) == null ? void 0 : _middlewareData$flip.overflows) || [];\n      if (checkMainAxis) {\n        overflows.push(overflow[side]);\n      }\n      if (checkCrossAxis) {\n        const sides = getAlignmentSides(placement, rects, rtl);\n        overflows.push(overflow[sides[0]], overflow[sides[1]]);\n      }\n      overflowsData = [...overflowsData, {\n        placement,\n        overflows\n      }];\n\n      // One or more sides is overflowing.\n      if (!overflows.every(side => side <= 0)) {\n        var _middlewareData$flip2, _overflowsData$filter;\n        const nextIndex = (((_middlewareData$flip2 = middlewareData.flip) == null ? void 0 : _middlewareData$flip2.index) || 0) + 1;\n        const nextPlacement = placements[nextIndex];\n        if (nextPlacement) {\n          const ignoreCrossAxisOverflow = checkCrossAxis === 'alignment' ? initialSideAxis !== getSideAxis(nextPlacement) : false;\n          if (!ignoreCrossAxisOverflow ||\n          // We leave the current main axis only if every placement on that axis\n          // overflows the main axis.\n          overflowsData.every(d => getSideAxis(d.placement) === initialSideAxis ? d.overflows[0] > 0 : true)) {\n            // Try next placement and re-run the lifecycle.\n            return {\n              data: {\n                index: nextIndex,\n                overflows: overflowsData\n              },\n              reset: {\n                placement: nextPlacement\n              }\n            };\n          }\n        }\n\n        // First, find the candidates that fit on the mainAxis side of overflow,\n        // then find the placement that fits the best on the main crossAxis side.\n        let resetPlacement = (_overflowsData$filter = overflowsData.filter(d => d.overflows[0] <= 0).sort((a, b) => a.overflows[1] - b.overflows[1])[0]) == null ? void 0 : _overflowsData$filter.placement;\n\n        // Otherwise fallback.\n        if (!resetPlacement) {\n          switch (fallbackStrategy) {\n            case 'bestFit':\n              {\n                var _overflowsData$filter2;\n                const placement = (_overflowsData$filter2 = overflowsData.filter(d => {\n                  if (hasFallbackAxisSideDirection) {\n                    const currentSideAxis = getSideAxis(d.placement);\n                    return currentSideAxis === initialSideAxis ||\n                    // Create a bias to the `y` side axis due to horizontal\n                    // reading directions favoring greater width.\n                    currentSideAxis === 'y';\n                  }\n                  return true;\n                }).map(d => [d.placement, d.overflows.filter(overflow => overflow > 0).reduce((acc, overflow) => acc + overflow, 0)]).sort((a, b) => a[1] - b[1])[0]) == null ? void 0 : _overflowsData$filter2[0];\n                if (placement) {\n                  resetPlacement = placement;\n                }\n                break;\n              }\n            case 'initialPlacement':\n              resetPlacement = initialPlacement;\n              break;\n          }\n        }\n        if (placement !== resetPlacement) {\n          return {\n            reset: {\n              placement: resetPlacement\n            }\n          };\n        }\n      }\n      return {};\n    }\n  };\n};\n\nfunction getSideOffsets(overflow, rect) {\n  return {\n    top: overflow.top - rect.height,\n    right: overflow.right - rect.width,\n    bottom: overflow.bottom - rect.height,\n    left: overflow.left - rect.width\n  };\n}\nfunction isAnySideFullyClipped(overflow) {\n  return sides.some(side => overflow[side] >= 0);\n}\n/**\n * Provides data to hide the floating element in applicable situations, such as\n * when it is not in the same clipping context as the reference element.\n * @see https://floating-ui.com/docs/hide\n */\nconst hide = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'hide',\n    options,\n    async fn(state) {\n      const {\n        rects\n      } = state;\n      const {\n        strategy = 'referenceHidden',\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      switch (strategy) {\n        case 'referenceHidden':\n          {\n            const overflow = await detectOverflow(state, {\n              ...detectOverflowOptions,\n              elementContext: 'reference'\n            });\n            const offsets = getSideOffsets(overflow, rects.reference);\n            return {\n              data: {\n                referenceHiddenOffsets: offsets,\n                referenceHidden: isAnySideFullyClipped(offsets)\n              }\n            };\n          }\n        case 'escaped':\n          {\n            const overflow = await detectOverflow(state, {\n              ...detectOverflowOptions,\n              altBoundary: true\n            });\n            const offsets = getSideOffsets(overflow, rects.floating);\n            return {\n              data: {\n                escapedOffsets: offsets,\n                escaped: isAnySideFullyClipped(offsets)\n              }\n            };\n          }\n        default:\n          {\n            return {};\n          }\n      }\n    }\n  };\n};\n\nfunction getBoundingRect(rects) {\n  const minX = min(...rects.map(rect => rect.left));\n  const minY = min(...rects.map(rect => rect.top));\n  const maxX = max(...rects.map(rect => rect.right));\n  const maxY = max(...rects.map(rect => rect.bottom));\n  return {\n    x: minX,\n    y: minY,\n    width: maxX - minX,\n    height: maxY - minY\n  };\n}\nfunction getRectsByLine(rects) {\n  const sortedRects = rects.slice().sort((a, b) => a.y - b.y);\n  const groups = [];\n  let prevRect = null;\n  for (let i = 0; i < sortedRects.length; i++) {\n    const rect = sortedRects[i];\n    if (!prevRect || rect.y - prevRect.y > prevRect.height / 2) {\n      groups.push([rect]);\n    } else {\n      groups[groups.length - 1].push(rect);\n    }\n    prevRect = rect;\n  }\n  return groups.map(rect => rectToClientRect(getBoundingRect(rect)));\n}\n/**\n * Provides improved positioning for inline reference elements that can span\n * over multiple lines, such as hyperlinks or range selections.\n * @see https://floating-ui.com/docs/inline\n */\nconst inline = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'inline',\n    options,\n    async fn(state) {\n      const {\n        placement,\n        elements,\n        rects,\n        platform,\n        strategy\n      } = state;\n      // A MouseEvent's client{X,Y} coords can be up to 2 pixels off a\n      // ClientRect's bounds, despite the event listener being triggered. A\n      // padding of 2 seems to handle this issue.\n      const {\n        padding = 2,\n        x,\n        y\n      } = evaluate(options, state);\n      const nativeClientRects = Array.from((await (platform.getClientRects == null ? void 0 : platform.getClientRects(elements.reference))) || []);\n      const clientRects = getRectsByLine(nativeClientRects);\n      const fallback = rectToClientRect(getBoundingRect(nativeClientRects));\n      const paddingObject = getPaddingObject(padding);\n      function getBoundingClientRect() {\n        // There are two rects and they are disjoined.\n        if (clientRects.length === 2 && clientRects[0].left > clientRects[1].right && x != null && y != null) {\n          // Find the first rect in which the point is fully inside.\n          return clientRects.find(rect => x > rect.left - paddingObject.left && x < rect.right + paddingObject.right && y > rect.top - paddingObject.top && y < rect.bottom + paddingObject.bottom) || fallback;\n        }\n\n        // There are 2 or more connected rects.\n        if (clientRects.length >= 2) {\n          if (getSideAxis(placement) === 'y') {\n            const firstRect = clientRects[0];\n            const lastRect = clientRects[clientRects.length - 1];\n            const isTop = getSide(placement) === 'top';\n            const top = firstRect.top;\n            const bottom = lastRect.bottom;\n            const left = isTop ? firstRect.left : lastRect.left;\n            const right = isTop ? firstRect.right : lastRect.right;\n            const width = right - left;\n            const height = bottom - top;\n            return {\n              top,\n              bottom,\n              left,\n              right,\n              width,\n              height,\n              x: left,\n              y: top\n            };\n          }\n          const isLeftSide = getSide(placement) === 'left';\n          const maxRight = max(...clientRects.map(rect => rect.right));\n          const minLeft = min(...clientRects.map(rect => rect.left));\n          const measureRects = clientRects.filter(rect => isLeftSide ? rect.left === minLeft : rect.right === maxRight);\n          const top = measureRects[0].top;\n          const bottom = measureRects[measureRects.length - 1].bottom;\n          const left = minLeft;\n          const right = maxRight;\n          const width = right - left;\n          const height = bottom - top;\n          return {\n            top,\n            bottom,\n            left,\n            right,\n            width,\n            height,\n            x: left,\n            y: top\n          };\n        }\n        return fallback;\n      }\n      const resetRects = await platform.getElementRects({\n        reference: {\n          getBoundingClientRect\n        },\n        floating: elements.floating,\n        strategy\n      });\n      if (rects.reference.x !== resetRects.reference.x || rects.reference.y !== resetRects.reference.y || rects.reference.width !== resetRects.reference.width || rects.reference.height !== resetRects.reference.height) {\n        return {\n          reset: {\n            rects: resetRects\n          }\n        };\n      }\n      return {};\n    }\n  };\n};\n\nconst originSides = /*#__PURE__*/new Set(['left', 'top']);\n\n// For type backwards-compatibility, the `OffsetOptions` type was also\n// Derivable.\n\nasync function convertValueToCoords(state, options) {\n  const {\n    placement,\n    platform,\n    elements\n  } = state;\n  const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating));\n  const side = getSide(placement);\n  const alignment = getAlignment(placement);\n  const isVertical = getSideAxis(placement) === 'y';\n  const mainAxisMulti = originSides.has(side) ? -1 : 1;\n  const crossAxisMulti = rtl && isVertical ? -1 : 1;\n  const rawValue = evaluate(options, state);\n\n  // eslint-disable-next-line prefer-const\n  let {\n    mainAxis,\n    crossAxis,\n    alignmentAxis\n  } = typeof rawValue === 'number' ? {\n    mainAxis: rawValue,\n    crossAxis: 0,\n    alignmentAxis: null\n  } : {\n    mainAxis: rawValue.mainAxis || 0,\n    crossAxis: rawValue.crossAxis || 0,\n    alignmentAxis: rawValue.alignmentAxis\n  };\n  if (alignment && typeof alignmentAxis === 'number') {\n    crossAxis = alignment === 'end' ? alignmentAxis * -1 : alignmentAxis;\n  }\n  return isVertical ? {\n    x: crossAxis * crossAxisMulti,\n    y: mainAxis * mainAxisMulti\n  } : {\n    x: mainAxis * mainAxisMulti,\n    y: crossAxis * crossAxisMulti\n  };\n}\n\n/**\n * Modifies the placement by translating the floating element along the\n * specified axes.\n * A number (shorthand for `mainAxis` or distance), or an axes configuration\n * object may be passed.\n * @see https://floating-ui.com/docs/offset\n */\nconst offset = function (options) {\n  if (options === void 0) {\n    options = 0;\n  }\n  return {\n    name: 'offset',\n    options,\n    async fn(state) {\n      var _middlewareData$offse, _middlewareData$arrow;\n      const {\n        x,\n        y,\n        placement,\n        middlewareData\n      } = state;\n      const diffCoords = await convertValueToCoords(state, options);\n\n      // If the placement is the same and the arrow caused an alignment offset\n      // then we don't need to change the positioning coordinates.\n      if (placement === ((_middlewareData$offse = middlewareData.offset) == null ? void 0 : _middlewareData$offse.placement) && (_middlewareData$arrow = middlewareData.arrow) != null && _middlewareData$arrow.alignmentOffset) {\n        return {};\n      }\n      return {\n        x: x + diffCoords.x,\n        y: y + diffCoords.y,\n        data: {\n          ...diffCoords,\n          placement\n        }\n      };\n    }\n  };\n};\n\n/**\n * Optimizes the visibility of the floating element by shifting it in order to\n * keep it in view when it will overflow the clipping boundary.\n * @see https://floating-ui.com/docs/shift\n */\nconst shift = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'shift',\n    options,\n    async fn(state) {\n      const {\n        x,\n        y,\n        placement\n      } = state;\n      const {\n        mainAxis: checkMainAxis = true,\n        crossAxis: checkCrossAxis = false,\n        limiter = {\n          fn: _ref => {\n            let {\n              x,\n              y\n            } = _ref;\n            return {\n              x,\n              y\n            };\n          }\n        },\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      const coords = {\n        x,\n        y\n      };\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const crossAxis = getSideAxis(getSide(placement));\n      const mainAxis = getOppositeAxis(crossAxis);\n      let mainAxisCoord = coords[mainAxis];\n      let crossAxisCoord = coords[crossAxis];\n      if (checkMainAxis) {\n        const minSide = mainAxis === 'y' ? 'top' : 'left';\n        const maxSide = mainAxis === 'y' ? 'bottom' : 'right';\n        const min = mainAxisCoord + overflow[minSide];\n        const max = mainAxisCoord - overflow[maxSide];\n        mainAxisCoord = clamp(min, mainAxisCoord, max);\n      }\n      if (checkCrossAxis) {\n        const minSide = crossAxis === 'y' ? 'top' : 'left';\n        const maxSide = crossAxis === 'y' ? 'bottom' : 'right';\n        const min = crossAxisCoord + overflow[minSide];\n        const max = crossAxisCoord - overflow[maxSide];\n        crossAxisCoord = clamp(min, crossAxisCoord, max);\n      }\n      const limitedCoords = limiter.fn({\n        ...state,\n        [mainAxis]: mainAxisCoord,\n        [crossAxis]: crossAxisCoord\n      });\n      return {\n        ...limitedCoords,\n        data: {\n          x: limitedCoords.x - x,\n          y: limitedCoords.y - y,\n          enabled: {\n            [mainAxis]: checkMainAxis,\n            [crossAxis]: checkCrossAxis\n          }\n        }\n      };\n    }\n  };\n};\n/**\n * Built-in `limiter` that will stop `shift()` at a certain point.\n */\nconst limitShift = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    options,\n    fn(state) {\n      const {\n        x,\n        y,\n        placement,\n        rects,\n        middlewareData\n      } = state;\n      const {\n        offset = 0,\n        mainAxis: checkMainAxis = true,\n        crossAxis: checkCrossAxis = true\n      } = evaluate(options, state);\n      const coords = {\n        x,\n        y\n      };\n      const crossAxis = getSideAxis(placement);\n      const mainAxis = getOppositeAxis(crossAxis);\n      let mainAxisCoord = coords[mainAxis];\n      let crossAxisCoord = coords[crossAxis];\n      const rawOffset = evaluate(offset, state);\n      const computedOffset = typeof rawOffset === 'number' ? {\n        mainAxis: rawOffset,\n        crossAxis: 0\n      } : {\n        mainAxis: 0,\n        crossAxis: 0,\n        ...rawOffset\n      };\n      if (checkMainAxis) {\n        const len = mainAxis === 'y' ? 'height' : 'width';\n        const limitMin = rects.reference[mainAxis] - rects.floating[len] + computedOffset.mainAxis;\n        const limitMax = rects.reference[mainAxis] + rects.reference[len] - computedOffset.mainAxis;\n        if (mainAxisCoord < limitMin) {\n          mainAxisCoord = limitMin;\n        } else if (mainAxisCoord > limitMax) {\n          mainAxisCoord = limitMax;\n        }\n      }\n      if (checkCrossAxis) {\n        var _middlewareData$offse, _middlewareData$offse2;\n        const len = mainAxis === 'y' ? 'width' : 'height';\n        const isOriginSide = originSides.has(getSide(placement));\n        const limitMin = rects.reference[crossAxis] - rects.floating[len] + (isOriginSide ? ((_middlewareData$offse = middlewareData.offset) == null ? void 0 : _middlewareData$offse[crossAxis]) || 0 : 0) + (isOriginSide ? 0 : computedOffset.crossAxis);\n        const limitMax = rects.reference[crossAxis] + rects.reference[len] + (isOriginSide ? 0 : ((_middlewareData$offse2 = middlewareData.offset) == null ? void 0 : _middlewareData$offse2[crossAxis]) || 0) - (isOriginSide ? computedOffset.crossAxis : 0);\n        if (crossAxisCoord < limitMin) {\n          crossAxisCoord = limitMin;\n        } else if (crossAxisCoord > limitMax) {\n          crossAxisCoord = limitMax;\n        }\n      }\n      return {\n        [mainAxis]: mainAxisCoord,\n        [crossAxis]: crossAxisCoord\n      };\n    }\n  };\n};\n\n/**\n * Provides data that allows you to change the size of the floating element —\n * for instance, prevent it from overflowing the clipping boundary or match the\n * width of the reference element.\n * @see https://floating-ui.com/docs/size\n */\nconst size = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'size',\n    options,\n    async fn(state) {\n      var _state$middlewareData, _state$middlewareData2;\n      const {\n        placement,\n        rects,\n        platform,\n        elements\n      } = state;\n      const {\n        apply = () => {},\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const side = getSide(placement);\n      const alignment = getAlignment(placement);\n      const isYAxis = getSideAxis(placement) === 'y';\n      const {\n        width,\n        height\n      } = rects.floating;\n      let heightSide;\n      let widthSide;\n      if (side === 'top' || side === 'bottom') {\n        heightSide = side;\n        widthSide = alignment === ((await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating))) ? 'start' : 'end') ? 'left' : 'right';\n      } else {\n        widthSide = side;\n        heightSide = alignment === 'end' ? 'top' : 'bottom';\n      }\n      const maximumClippingHeight = height - overflow.top - overflow.bottom;\n      const maximumClippingWidth = width - overflow.left - overflow.right;\n      const overflowAvailableHeight = min(height - overflow[heightSide], maximumClippingHeight);\n      const overflowAvailableWidth = min(width - overflow[widthSide], maximumClippingWidth);\n      const noShift = !state.middlewareData.shift;\n      let availableHeight = overflowAvailableHeight;\n      let availableWidth = overflowAvailableWidth;\n      if ((_state$middlewareData = state.middlewareData.shift) != null && _state$middlewareData.enabled.x) {\n        availableWidth = maximumClippingWidth;\n      }\n      if ((_state$middlewareData2 = state.middlewareData.shift) != null && _state$middlewareData2.enabled.y) {\n        availableHeight = maximumClippingHeight;\n      }\n      if (noShift && !alignment) {\n        const xMin = max(overflow.left, 0);\n        const xMax = max(overflow.right, 0);\n        const yMin = max(overflow.top, 0);\n        const yMax = max(overflow.bottom, 0);\n        if (isYAxis) {\n          availableWidth = width - 2 * (xMin !== 0 || xMax !== 0 ? xMin + xMax : max(overflow.left, overflow.right));\n        } else {\n          availableHeight = height - 2 * (yMin !== 0 || yMax !== 0 ? yMin + yMax : max(overflow.top, overflow.bottom));\n        }\n      }\n      await apply({\n        ...state,\n        availableWidth,\n        availableHeight\n      });\n      const nextDimensions = await platform.getDimensions(elements.floating);\n      if (width !== nextDimensions.width || height !== nextDimensions.height) {\n        return {\n          reset: {\n            rects: true\n          }\n        };\n      }\n      return {};\n    }\n  };\n};\n\nexport { arrow, autoPlacement, computePosition, detectOverflow, flip, hide, inline, limitShift, offset, shift, size };\n", "function hasWindow() {\n  return typeof window !== 'undefined';\n}\nfunction getNodeName(node) {\n  if (isNode(node)) {\n    return (node.nodeName || '').toLowerCase();\n  }\n  // Mocked nodes in testing environments may not be instances of Node. By\n  // returning `#document` an infinite loop won't occur.\n  // https://github.com/floating-ui/floating-ui/issues/2317\n  return '#document';\n}\nfunction getWindow(node) {\n  var _node$ownerDocument;\n  return (node == null || (_node$ownerDocument = node.ownerDocument) == null ? void 0 : _node$ownerDocument.defaultView) || window;\n}\nfunction getDocumentElement(node) {\n  var _ref;\n  return (_ref = (isNode(node) ? node.ownerDocument : node.document) || window.document) == null ? void 0 : _ref.documentElement;\n}\nfunction isNode(value) {\n  if (!hasWindow()) {\n    return false;\n  }\n  return value instanceof Node || value instanceof getWindow(value).Node;\n}\nfunction isElement(value) {\n  if (!hasWindow()) {\n    return false;\n  }\n  return value instanceof Element || value instanceof getWindow(value).Element;\n}\nfunction isHTMLElement(value) {\n  if (!hasWindow()) {\n    return false;\n  }\n  return value instanceof HTMLElement || value instanceof getWindow(value).HTMLElement;\n}\nfunction isShadowRoot(value) {\n  if (!hasWindow() || typeof ShadowRoot === 'undefined') {\n    return false;\n  }\n  return value instanceof ShadowRoot || value instanceof getWindow(value).ShadowRoot;\n}\nconst invalidOverflowDisplayValues = /*#__PURE__*/new Set(['inline', 'contents']);\nfunction isOverflowElement(element) {\n  const {\n    overflow,\n    overflowX,\n    overflowY,\n    display\n  } = getComputedStyle(element);\n  return /auto|scroll|overlay|hidden|clip/.test(overflow + overflowY + overflowX) && !invalidOverflowDisplayValues.has(display);\n}\nconst tableElements = /*#__PURE__*/new Set(['table', 'td', 'th']);\nfunction isTableElement(element) {\n  return tableElements.has(getNodeName(element));\n}\nconst topLayerSelectors = [':popover-open', ':modal'];\nfunction isTopLayer(element) {\n  return topLayerSelectors.some(selector => {\n    try {\n      return element.matches(selector);\n    } catch (_e) {\n      return false;\n    }\n  });\n}\nconst transformProperties = ['transform', 'translate', 'scale', 'rotate', 'perspective'];\nconst willChangeValues = ['transform', 'translate', 'scale', 'rotate', 'perspective', 'filter'];\nconst containValues = ['paint', 'layout', 'strict', 'content'];\nfunction isContainingBlock(elementOrCss) {\n  const webkit = isWebKit();\n  const css = isElement(elementOrCss) ? getComputedStyle(elementOrCss) : elementOrCss;\n\n  // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n  // https://drafts.csswg.org/css-transforms-2/#individual-transforms\n  return transformProperties.some(value => css[value] ? css[value] !== 'none' : false) || (css.containerType ? css.containerType !== 'normal' : false) || !webkit && (css.backdropFilter ? css.backdropFilter !== 'none' : false) || !webkit && (css.filter ? css.filter !== 'none' : false) || willChangeValues.some(value => (css.willChange || '').includes(value)) || containValues.some(value => (css.contain || '').includes(value));\n}\nfunction getContainingBlock(element) {\n  let currentNode = getParentNode(element);\n  while (isHTMLElement(currentNode) && !isLastTraversableNode(currentNode)) {\n    if (isContainingBlock(currentNode)) {\n      return currentNode;\n    } else if (isTopLayer(currentNode)) {\n      return null;\n    }\n    currentNode = getParentNode(currentNode);\n  }\n  return null;\n}\nfunction isWebKit() {\n  if (typeof CSS === 'undefined' || !CSS.supports) return false;\n  return CSS.supports('-webkit-backdrop-filter', 'none');\n}\nconst lastTraversableNodeNames = /*#__PURE__*/new Set(['html', 'body', '#document']);\nfunction isLastTraversableNode(node) {\n  return lastTraversableNodeNames.has(getNodeName(node));\n}\nfunction getComputedStyle(element) {\n  return getWindow(element).getComputedStyle(element);\n}\nfunction getNodeScroll(element) {\n  if (isElement(element)) {\n    return {\n      scrollLeft: element.scrollLeft,\n      scrollTop: element.scrollTop\n    };\n  }\n  return {\n    scrollLeft: element.scrollX,\n    scrollTop: element.scrollY\n  };\n}\nfunction getParentNode(node) {\n  if (getNodeName(node) === 'html') {\n    return node;\n  }\n  const result =\n  // Step into the shadow DOM of the parent of a slotted node.\n  node.assignedSlot ||\n  // DOM Element detected.\n  node.parentNode ||\n  // ShadowRoot detected.\n  isShadowRoot(node) && node.host ||\n  // Fallback.\n  getDocumentElement(node);\n  return isShadowRoot(result) ? result.host : result;\n}\nfunction getNearestOverflowAncestor(node) {\n  const parentNode = getParentNode(node);\n  if (isLastTraversableNode(parentNode)) {\n    return node.ownerDocument ? node.ownerDocument.body : node.body;\n  }\n  if (isHTMLElement(parentNode) && isOverflowElement(parentNode)) {\n    return parentNode;\n  }\n  return getNearestOverflowAncestor(parentNode);\n}\nfunction getOverflowAncestors(node, list, traverseIframes) {\n  var _node$ownerDocument2;\n  if (list === void 0) {\n    list = [];\n  }\n  if (traverseIframes === void 0) {\n    traverseIframes = true;\n  }\n  const scrollableAncestor = getNearestOverflowAncestor(node);\n  const isBody = scrollableAncestor === ((_node$ownerDocument2 = node.ownerDocument) == null ? void 0 : _node$ownerDocument2.body);\n  const win = getWindow(scrollableAncestor);\n  if (isBody) {\n    const frameElement = getFrameElement(win);\n    return list.concat(win, win.visualViewport || [], isOverflowElement(scrollableAncestor) ? scrollableAncestor : [], frameElement && traverseIframes ? getOverflowAncestors(frameElement) : []);\n  }\n  return list.concat(scrollableAncestor, getOverflowAncestors(scrollableAncestor, [], traverseIframes));\n}\nfunction getFrameElement(win) {\n  return win.parent && Object.getPrototypeOf(win.parent) ? win.frameElement : null;\n}\n\nexport { getComputedStyle, getContainingBlock, getDocumentElement, getFrameElement, getNearestOverflowAncestor, getNodeName, getNodeScroll, getOverflowAncestors, getParentNode, getWindow, isContainingBlock, isElement, isHTMLElement, isLastTraversableNode, isNode, isOverflowElement, isShadowRoot, isTableElement, isTopLayer, isWebKit };\n", "import { rectToClientRect, detectOverflow as detectOverflow$1, offset as offset$1, autoPlacement as autoPlacement$1, shift as shift$1, flip as flip$1, size as size$1, hide as hide$1, arrow as arrow$1, inline as inline$1, limitShift as limitShift$1, computePosition as computePosition$1 } from '@floating-ui/core';\nimport { round, createCoords, max, min, floor } from '@floating-ui/utils';\nimport { getComputedStyle, isHTMLElement, isElement, getWindow, isWebKit, getFrameElement, getNodeScroll, getDocumentElement, isTopLayer, getNodeName, isOverflowElement, getOverflowAncestors, getParentNode, isLastTraversableNode, isContainingBlock, isTableElement, getContainingBlock } from '@floating-ui/utils/dom';\nexport { getOverflowAncestors } from '@floating-ui/utils/dom';\n\nfunction getCssDimensions(element) {\n  const css = getComputedStyle(element);\n  // In testing environments, the `width` and `height` properties are empty\n  // strings for SVG elements, returning NaN. Fallback to `0` in this case.\n  let width = parseFloat(css.width) || 0;\n  let height = parseFloat(css.height) || 0;\n  const hasOffset = isHTMLElement(element);\n  const offsetWidth = hasOffset ? element.offsetWidth : width;\n  const offsetHeight = hasOffset ? element.offsetHeight : height;\n  const shouldFallback = round(width) !== offsetWidth || round(height) !== offsetHeight;\n  if (shouldFallback) {\n    width = offsetWidth;\n    height = offsetHeight;\n  }\n  return {\n    width,\n    height,\n    $: shouldFallback\n  };\n}\n\nfunction unwrapElement(element) {\n  return !isElement(element) ? element.contextElement : element;\n}\n\nfunction getScale(element) {\n  const domElement = unwrapElement(element);\n  if (!isHTMLElement(domElement)) {\n    return createCoords(1);\n  }\n  const rect = domElement.getBoundingClientRect();\n  const {\n    width,\n    height,\n    $\n  } = getCssDimensions(domElement);\n  let x = ($ ? round(rect.width) : rect.width) / width;\n  let y = ($ ? round(rect.height) : rect.height) / height;\n\n  // 0, NaN, or Infinity should always fallback to 1.\n\n  if (!x || !Number.isFinite(x)) {\n    x = 1;\n  }\n  if (!y || !Number.isFinite(y)) {\n    y = 1;\n  }\n  return {\n    x,\n    y\n  };\n}\n\nconst noOffsets = /*#__PURE__*/createCoords(0);\nfunction getVisualOffsets(element) {\n  const win = getWindow(element);\n  if (!isWebKit() || !win.visualViewport) {\n    return noOffsets;\n  }\n  return {\n    x: win.visualViewport.offsetLeft,\n    y: win.visualViewport.offsetTop\n  };\n}\nfunction shouldAddVisualOffsets(element, isFixed, floatingOffsetParent) {\n  if (isFixed === void 0) {\n    isFixed = false;\n  }\n  if (!floatingOffsetParent || isFixed && floatingOffsetParent !== getWindow(element)) {\n    return false;\n  }\n  return isFixed;\n}\n\nfunction getBoundingClientRect(element, includeScale, isFixedStrategy, offsetParent) {\n  if (includeScale === void 0) {\n    includeScale = false;\n  }\n  if (isFixedStrategy === void 0) {\n    isFixedStrategy = false;\n  }\n  const clientRect = element.getBoundingClientRect();\n  const domElement = unwrapElement(element);\n  let scale = createCoords(1);\n  if (includeScale) {\n    if (offsetParent) {\n      if (isElement(offsetParent)) {\n        scale = getScale(offsetParent);\n      }\n    } else {\n      scale = getScale(element);\n    }\n  }\n  const visualOffsets = shouldAddVisualOffsets(domElement, isFixedStrategy, offsetParent) ? getVisualOffsets(domElement) : createCoords(0);\n  let x = (clientRect.left + visualOffsets.x) / scale.x;\n  let y = (clientRect.top + visualOffsets.y) / scale.y;\n  let width = clientRect.width / scale.x;\n  let height = clientRect.height / scale.y;\n  if (domElement) {\n    const win = getWindow(domElement);\n    const offsetWin = offsetParent && isElement(offsetParent) ? getWindow(offsetParent) : offsetParent;\n    let currentWin = win;\n    let currentIFrame = getFrameElement(currentWin);\n    while (currentIFrame && offsetParent && offsetWin !== currentWin) {\n      const iframeScale = getScale(currentIFrame);\n      const iframeRect = currentIFrame.getBoundingClientRect();\n      const css = getComputedStyle(currentIFrame);\n      const left = iframeRect.left + (currentIFrame.clientLeft + parseFloat(css.paddingLeft)) * iframeScale.x;\n      const top = iframeRect.top + (currentIFrame.clientTop + parseFloat(css.paddingTop)) * iframeScale.y;\n      x *= iframeScale.x;\n      y *= iframeScale.y;\n      width *= iframeScale.x;\n      height *= iframeScale.y;\n      x += left;\n      y += top;\n      currentWin = getWindow(currentIFrame);\n      currentIFrame = getFrameElement(currentWin);\n    }\n  }\n  return rectToClientRect({\n    width,\n    height,\n    x,\n    y\n  });\n}\n\n// If <html> has a CSS width greater than the viewport, then this will be\n// incorrect for RTL.\nfunction getWindowScrollBarX(element, rect) {\n  const leftScroll = getNodeScroll(element).scrollLeft;\n  if (!rect) {\n    return getBoundingClientRect(getDocumentElement(element)).left + leftScroll;\n  }\n  return rect.left + leftScroll;\n}\n\nfunction getHTMLOffset(documentElement, scroll, ignoreScrollbarX) {\n  if (ignoreScrollbarX === void 0) {\n    ignoreScrollbarX = false;\n  }\n  const htmlRect = documentElement.getBoundingClientRect();\n  const x = htmlRect.left + scroll.scrollLeft - (ignoreScrollbarX ? 0 :\n  // RTL <body> scrollbar.\n  getWindowScrollBarX(documentElement, htmlRect));\n  const y = htmlRect.top + scroll.scrollTop;\n  return {\n    x,\n    y\n  };\n}\n\nfunction convertOffsetParentRelativeRectToViewportRelativeRect(_ref) {\n  let {\n    elements,\n    rect,\n    offsetParent,\n    strategy\n  } = _ref;\n  const isFixed = strategy === 'fixed';\n  const documentElement = getDocumentElement(offsetParent);\n  const topLayer = elements ? isTopLayer(elements.floating) : false;\n  if (offsetParent === documentElement || topLayer && isFixed) {\n    return rect;\n  }\n  let scroll = {\n    scrollLeft: 0,\n    scrollTop: 0\n  };\n  let scale = createCoords(1);\n  const offsets = createCoords(0);\n  const isOffsetParentAnElement = isHTMLElement(offsetParent);\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\n    if (getNodeName(offsetParent) !== 'body' || isOverflowElement(documentElement)) {\n      scroll = getNodeScroll(offsetParent);\n    }\n    if (isHTMLElement(offsetParent)) {\n      const offsetRect = getBoundingClientRect(offsetParent);\n      scale = getScale(offsetParent);\n      offsets.x = offsetRect.x + offsetParent.clientLeft;\n      offsets.y = offsetRect.y + offsetParent.clientTop;\n    }\n  }\n  const htmlOffset = documentElement && !isOffsetParentAnElement && !isFixed ? getHTMLOffset(documentElement, scroll, true) : createCoords(0);\n  return {\n    width: rect.width * scale.x,\n    height: rect.height * scale.y,\n    x: rect.x * scale.x - scroll.scrollLeft * scale.x + offsets.x + htmlOffset.x,\n    y: rect.y * scale.y - scroll.scrollTop * scale.y + offsets.y + htmlOffset.y\n  };\n}\n\nfunction getClientRects(element) {\n  return Array.from(element.getClientRects());\n}\n\n// Gets the entire size of the scrollable document area, even extending outside\n// of the `<html>` and `<body>` rect bounds if horizontally scrollable.\nfunction getDocumentRect(element) {\n  const html = getDocumentElement(element);\n  const scroll = getNodeScroll(element);\n  const body = element.ownerDocument.body;\n  const width = max(html.scrollWidth, html.clientWidth, body.scrollWidth, body.clientWidth);\n  const height = max(html.scrollHeight, html.clientHeight, body.scrollHeight, body.clientHeight);\n  let x = -scroll.scrollLeft + getWindowScrollBarX(element);\n  const y = -scroll.scrollTop;\n  if (getComputedStyle(body).direction === 'rtl') {\n    x += max(html.clientWidth, body.clientWidth) - width;\n  }\n  return {\n    width,\n    height,\n    x,\n    y\n  };\n}\n\nfunction getViewportRect(element, strategy) {\n  const win = getWindow(element);\n  const html = getDocumentElement(element);\n  const visualViewport = win.visualViewport;\n  let width = html.clientWidth;\n  let height = html.clientHeight;\n  let x = 0;\n  let y = 0;\n  if (visualViewport) {\n    width = visualViewport.width;\n    height = visualViewport.height;\n    const visualViewportBased = isWebKit();\n    if (!visualViewportBased || visualViewportBased && strategy === 'fixed') {\n      x = visualViewport.offsetLeft;\n      y = visualViewport.offsetTop;\n    }\n  }\n  return {\n    width,\n    height,\n    x,\n    y\n  };\n}\n\n// Returns the inner client rect, subtracting scrollbars if present.\nfunction getInnerBoundingClientRect(element, strategy) {\n  const clientRect = getBoundingClientRect(element, true, strategy === 'fixed');\n  const top = clientRect.top + element.clientTop;\n  const left = clientRect.left + element.clientLeft;\n  const scale = isHTMLElement(element) ? getScale(element) : createCoords(1);\n  const width = element.clientWidth * scale.x;\n  const height = element.clientHeight * scale.y;\n  const x = left * scale.x;\n  const y = top * scale.y;\n  return {\n    width,\n    height,\n    x,\n    y\n  };\n}\nfunction getClientRectFromClippingAncestor(element, clippingAncestor, strategy) {\n  let rect;\n  if (clippingAncestor === 'viewport') {\n    rect = getViewportRect(element, strategy);\n  } else if (clippingAncestor === 'document') {\n    rect = getDocumentRect(getDocumentElement(element));\n  } else if (isElement(clippingAncestor)) {\n    rect = getInnerBoundingClientRect(clippingAncestor, strategy);\n  } else {\n    const visualOffsets = getVisualOffsets(element);\n    rect = {\n      x: clippingAncestor.x - visualOffsets.x,\n      y: clippingAncestor.y - visualOffsets.y,\n      width: clippingAncestor.width,\n      height: clippingAncestor.height\n    };\n  }\n  return rectToClientRect(rect);\n}\nfunction hasFixedPositionAncestor(element, stopNode) {\n  const parentNode = getParentNode(element);\n  if (parentNode === stopNode || !isElement(parentNode) || isLastTraversableNode(parentNode)) {\n    return false;\n  }\n  return getComputedStyle(parentNode).position === 'fixed' || hasFixedPositionAncestor(parentNode, stopNode);\n}\n\n// A \"clipping ancestor\" is an `overflow` element with the characteristic of\n// clipping (or hiding) child elements. This returns all clipping ancestors\n// of the given element up the tree.\nfunction getClippingElementAncestors(element, cache) {\n  const cachedResult = cache.get(element);\n  if (cachedResult) {\n    return cachedResult;\n  }\n  let result = getOverflowAncestors(element, [], false).filter(el => isElement(el) && getNodeName(el) !== 'body');\n  let currentContainingBlockComputedStyle = null;\n  const elementIsFixed = getComputedStyle(element).position === 'fixed';\n  let currentNode = elementIsFixed ? getParentNode(element) : element;\n\n  // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n  while (isElement(currentNode) && !isLastTraversableNode(currentNode)) {\n    const computedStyle = getComputedStyle(currentNode);\n    const currentNodeIsContaining = isContainingBlock(currentNode);\n    if (!currentNodeIsContaining && computedStyle.position === 'fixed') {\n      currentContainingBlockComputedStyle = null;\n    }\n    const shouldDropCurrentNode = elementIsFixed ? !currentNodeIsContaining && !currentContainingBlockComputedStyle : !currentNodeIsContaining && computedStyle.position === 'static' && !!currentContainingBlockComputedStyle && ['absolute', 'fixed'].includes(currentContainingBlockComputedStyle.position) || isOverflowElement(currentNode) && !currentNodeIsContaining && hasFixedPositionAncestor(element, currentNode);\n    if (shouldDropCurrentNode) {\n      // Drop non-containing blocks.\n      result = result.filter(ancestor => ancestor !== currentNode);\n    } else {\n      // Record last containing block for next iteration.\n      currentContainingBlockComputedStyle = computedStyle;\n    }\n    currentNode = getParentNode(currentNode);\n  }\n  cache.set(element, result);\n  return result;\n}\n\n// Gets the maximum area that the element is visible in due to any number of\n// clipping ancestors.\nfunction getClippingRect(_ref) {\n  let {\n    element,\n    boundary,\n    rootBoundary,\n    strategy\n  } = _ref;\n  const elementClippingAncestors = boundary === 'clippingAncestors' ? isTopLayer(element) ? [] : getClippingElementAncestors(element, this._c) : [].concat(boundary);\n  const clippingAncestors = [...elementClippingAncestors, rootBoundary];\n  const firstClippingAncestor = clippingAncestors[0];\n  const clippingRect = clippingAncestors.reduce((accRect, clippingAncestor) => {\n    const rect = getClientRectFromClippingAncestor(element, clippingAncestor, strategy);\n    accRect.top = max(rect.top, accRect.top);\n    accRect.right = min(rect.right, accRect.right);\n    accRect.bottom = min(rect.bottom, accRect.bottom);\n    accRect.left = max(rect.left, accRect.left);\n    return accRect;\n  }, getClientRectFromClippingAncestor(element, firstClippingAncestor, strategy));\n  return {\n    width: clippingRect.right - clippingRect.left,\n    height: clippingRect.bottom - clippingRect.top,\n    x: clippingRect.left,\n    y: clippingRect.top\n  };\n}\n\nfunction getDimensions(element) {\n  const {\n    width,\n    height\n  } = getCssDimensions(element);\n  return {\n    width,\n    height\n  };\n}\n\nfunction getRectRelativeToOffsetParent(element, offsetParent, strategy) {\n  const isOffsetParentAnElement = isHTMLElement(offsetParent);\n  const documentElement = getDocumentElement(offsetParent);\n  const isFixed = strategy === 'fixed';\n  const rect = getBoundingClientRect(element, true, isFixed, offsetParent);\n  let scroll = {\n    scrollLeft: 0,\n    scrollTop: 0\n  };\n  const offsets = createCoords(0);\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\n    if (getNodeName(offsetParent) !== 'body' || isOverflowElement(documentElement)) {\n      scroll = getNodeScroll(offsetParent);\n    }\n    if (isOffsetParentAnElement) {\n      const offsetRect = getBoundingClientRect(offsetParent, true, isFixed, offsetParent);\n      offsets.x = offsetRect.x + offsetParent.clientLeft;\n      offsets.y = offsetRect.y + offsetParent.clientTop;\n    } else if (documentElement) {\n      // If the <body> scrollbar appears on the left (e.g. RTL systems). Use\n      // Firefox with layout.scrollbar.side = 3 in about:config to test this.\n      offsets.x = getWindowScrollBarX(documentElement);\n    }\n  }\n  const htmlOffset = documentElement && !isOffsetParentAnElement && !isFixed ? getHTMLOffset(documentElement, scroll) : createCoords(0);\n  const x = rect.left + scroll.scrollLeft - offsets.x - htmlOffset.x;\n  const y = rect.top + scroll.scrollTop - offsets.y - htmlOffset.y;\n  return {\n    x,\n    y,\n    width: rect.width,\n    height: rect.height\n  };\n}\n\nfunction isStaticPositioned(element) {\n  return getComputedStyle(element).position === 'static';\n}\n\nfunction getTrueOffsetParent(element, polyfill) {\n  if (!isHTMLElement(element) || getComputedStyle(element).position === 'fixed') {\n    return null;\n  }\n  if (polyfill) {\n    return polyfill(element);\n  }\n  let rawOffsetParent = element.offsetParent;\n\n  // Firefox returns the <html> element as the offsetParent if it's non-static,\n  // while Chrome and Safari return the <body> element. The <body> element must\n  // be used to perform the correct calculations even if the <html> element is\n  // non-static.\n  if (getDocumentElement(element) === rawOffsetParent) {\n    rawOffsetParent = rawOffsetParent.ownerDocument.body;\n  }\n  return rawOffsetParent;\n}\n\n// Gets the closest ancestor positioned element. Handles some edge cases,\n// such as table ancestors and cross browser bugs.\nfunction getOffsetParent(element, polyfill) {\n  const win = getWindow(element);\n  if (isTopLayer(element)) {\n    return win;\n  }\n  if (!isHTMLElement(element)) {\n    let svgOffsetParent = getParentNode(element);\n    while (svgOffsetParent && !isLastTraversableNode(svgOffsetParent)) {\n      if (isElement(svgOffsetParent) && !isStaticPositioned(svgOffsetParent)) {\n        return svgOffsetParent;\n      }\n      svgOffsetParent = getParentNode(svgOffsetParent);\n    }\n    return win;\n  }\n  let offsetParent = getTrueOffsetParent(element, polyfill);\n  while (offsetParent && isTableElement(offsetParent) && isStaticPositioned(offsetParent)) {\n    offsetParent = getTrueOffsetParent(offsetParent, polyfill);\n  }\n  if (offsetParent && isLastTraversableNode(offsetParent) && isStaticPositioned(offsetParent) && !isContainingBlock(offsetParent)) {\n    return win;\n  }\n  return offsetParent || getContainingBlock(element) || win;\n}\n\nconst getElementRects = async function (data) {\n  const getOffsetParentFn = this.getOffsetParent || getOffsetParent;\n  const getDimensionsFn = this.getDimensions;\n  const floatingDimensions = await getDimensionsFn(data.floating);\n  return {\n    reference: getRectRelativeToOffsetParent(data.reference, await getOffsetParentFn(data.floating), data.strategy),\n    floating: {\n      x: 0,\n      y: 0,\n      width: floatingDimensions.width,\n      height: floatingDimensions.height\n    }\n  };\n};\n\nfunction isRTL(element) {\n  return getComputedStyle(element).direction === 'rtl';\n}\n\nconst platform = {\n  convertOffsetParentRelativeRectToViewportRelativeRect,\n  getDocumentElement,\n  getClippingRect,\n  getOffsetParent,\n  getElementRects,\n  getClientRects,\n  getDimensions,\n  getScale,\n  isElement,\n  isRTL\n};\n\nfunction rectsAreEqual(a, b) {\n  return a.x === b.x && a.y === b.y && a.width === b.width && a.height === b.height;\n}\n\n// https://samthor.au/2021/observing-dom/\nfunction observeMove(element, onMove) {\n  let io = null;\n  let timeoutId;\n  const root = getDocumentElement(element);\n  function cleanup() {\n    var _io;\n    clearTimeout(timeoutId);\n    (_io = io) == null || _io.disconnect();\n    io = null;\n  }\n  function refresh(skip, threshold) {\n    if (skip === void 0) {\n      skip = false;\n    }\n    if (threshold === void 0) {\n      threshold = 1;\n    }\n    cleanup();\n    const elementRectForRootMargin = element.getBoundingClientRect();\n    const {\n      left,\n      top,\n      width,\n      height\n    } = elementRectForRootMargin;\n    if (!skip) {\n      onMove();\n    }\n    if (!width || !height) {\n      return;\n    }\n    const insetTop = floor(top);\n    const insetRight = floor(root.clientWidth - (left + width));\n    const insetBottom = floor(root.clientHeight - (top + height));\n    const insetLeft = floor(left);\n    const rootMargin = -insetTop + \"px \" + -insetRight + \"px \" + -insetBottom + \"px \" + -insetLeft + \"px\";\n    const options = {\n      rootMargin,\n      threshold: max(0, min(1, threshold)) || 1\n    };\n    let isFirstUpdate = true;\n    function handleObserve(entries) {\n      const ratio = entries[0].intersectionRatio;\n      if (ratio !== threshold) {\n        if (!isFirstUpdate) {\n          return refresh();\n        }\n        if (!ratio) {\n          // If the reference is clipped, the ratio is 0. Throttle the refresh\n          // to prevent an infinite loop of updates.\n          timeoutId = setTimeout(() => {\n            refresh(false, 1e-7);\n          }, 1000);\n        } else {\n          refresh(false, ratio);\n        }\n      }\n      if (ratio === 1 && !rectsAreEqual(elementRectForRootMargin, element.getBoundingClientRect())) {\n        // It's possible that even though the ratio is reported as 1, the\n        // element is not actually fully within the IntersectionObserver's root\n        // area anymore. This can happen under performance constraints. This may\n        // be a bug in the browser's IntersectionObserver implementation. To\n        // work around this, we compare the element's bounding rect now with\n        // what it was at the time we created the IntersectionObserver. If they\n        // are not equal then the element moved, so we refresh.\n        refresh();\n      }\n      isFirstUpdate = false;\n    }\n\n    // Older browsers don't support a `document` as the root and will throw an\n    // error.\n    try {\n      io = new IntersectionObserver(handleObserve, {\n        ...options,\n        // Handle <iframe>s\n        root: root.ownerDocument\n      });\n    } catch (e) {\n      io = new IntersectionObserver(handleObserve, options);\n    }\n    io.observe(element);\n  }\n  refresh(true);\n  return cleanup;\n}\n\n/**\n * Automatically updates the position of the floating element when necessary.\n * Should only be called when the floating element is mounted on the DOM or\n * visible on the screen.\n * @returns cleanup function that should be invoked when the floating element is\n * removed from the DOM or hidden from the screen.\n * @see https://floating-ui.com/docs/autoUpdate\n */\nfunction autoUpdate(reference, floating, update, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    ancestorScroll = true,\n    ancestorResize = true,\n    elementResize = typeof ResizeObserver === 'function',\n    layoutShift = typeof IntersectionObserver === 'function',\n    animationFrame = false\n  } = options;\n  const referenceEl = unwrapElement(reference);\n  const ancestors = ancestorScroll || ancestorResize ? [...(referenceEl ? getOverflowAncestors(referenceEl) : []), ...getOverflowAncestors(floating)] : [];\n  ancestors.forEach(ancestor => {\n    ancestorScroll && ancestor.addEventListener('scroll', update, {\n      passive: true\n    });\n    ancestorResize && ancestor.addEventListener('resize', update);\n  });\n  const cleanupIo = referenceEl && layoutShift ? observeMove(referenceEl, update) : null;\n  let reobserveFrame = -1;\n  let resizeObserver = null;\n  if (elementResize) {\n    resizeObserver = new ResizeObserver(_ref => {\n      let [firstEntry] = _ref;\n      if (firstEntry && firstEntry.target === referenceEl && resizeObserver) {\n        // Prevent update loops when using the `size` middleware.\n        // https://github.com/floating-ui/floating-ui/issues/1740\n        resizeObserver.unobserve(floating);\n        cancelAnimationFrame(reobserveFrame);\n        reobserveFrame = requestAnimationFrame(() => {\n          var _resizeObserver;\n          (_resizeObserver = resizeObserver) == null || _resizeObserver.observe(floating);\n        });\n      }\n      update();\n    });\n    if (referenceEl && !animationFrame) {\n      resizeObserver.observe(referenceEl);\n    }\n    resizeObserver.observe(floating);\n  }\n  let frameId;\n  let prevRefRect = animationFrame ? getBoundingClientRect(reference) : null;\n  if (animationFrame) {\n    frameLoop();\n  }\n  function frameLoop() {\n    const nextRefRect = getBoundingClientRect(reference);\n    if (prevRefRect && !rectsAreEqual(prevRefRect, nextRefRect)) {\n      update();\n    }\n    prevRefRect = nextRefRect;\n    frameId = requestAnimationFrame(frameLoop);\n  }\n  update();\n  return () => {\n    var _resizeObserver2;\n    ancestors.forEach(ancestor => {\n      ancestorScroll && ancestor.removeEventListener('scroll', update);\n      ancestorResize && ancestor.removeEventListener('resize', update);\n    });\n    cleanupIo == null || cleanupIo();\n    (_resizeObserver2 = resizeObserver) == null || _resizeObserver2.disconnect();\n    resizeObserver = null;\n    if (animationFrame) {\n      cancelAnimationFrame(frameId);\n    }\n  };\n}\n\n/**\n * Resolves with an object of overflow side offsets that determine how much the\n * element is overflowing a given clipping boundary on each side.\n * - positive = overflowing the boundary by that number of pixels\n * - negative = how many pixels left before it will overflow\n * - 0 = lies flush with the boundary\n * @see https://floating-ui.com/docs/detectOverflow\n */\nconst detectOverflow = detectOverflow$1;\n\n/**\n * Modifies the placement by translating the floating element along the\n * specified axes.\n * A number (shorthand for `mainAxis` or distance), or an axes configuration\n * object may be passed.\n * @see https://floating-ui.com/docs/offset\n */\nconst offset = offset$1;\n\n/**\n * Optimizes the visibility of the floating element by choosing the placement\n * that has the most space available automatically, without needing to specify a\n * preferred placement. Alternative to `flip`.\n * @see https://floating-ui.com/docs/autoPlacement\n */\nconst autoPlacement = autoPlacement$1;\n\n/**\n * Optimizes the visibility of the floating element by shifting it in order to\n * keep it in view when it will overflow the clipping boundary.\n * @see https://floating-ui.com/docs/shift\n */\nconst shift = shift$1;\n\n/**\n * Optimizes the visibility of the floating element by flipping the `placement`\n * in order to keep it in view when the preferred placement(s) will overflow the\n * clipping boundary. Alternative to `autoPlacement`.\n * @see https://floating-ui.com/docs/flip\n */\nconst flip = flip$1;\n\n/**\n * Provides data that allows you to change the size of the floating element —\n * for instance, prevent it from overflowing the clipping boundary or match the\n * width of the reference element.\n * @see https://floating-ui.com/docs/size\n */\nconst size = size$1;\n\n/**\n * Provides data to hide the floating element in applicable situations, such as\n * when it is not in the same clipping context as the reference element.\n * @see https://floating-ui.com/docs/hide\n */\nconst hide = hide$1;\n\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * @see https://floating-ui.com/docs/arrow\n */\nconst arrow = arrow$1;\n\n/**\n * Provides improved positioning for inline reference elements that can span\n * over multiple lines, such as hyperlinks or range selections.\n * @see https://floating-ui.com/docs/inline\n */\nconst inline = inline$1;\n\n/**\n * Built-in `limiter` that will stop `shift()` at a certain point.\n */\nconst limitShift = limitShift$1;\n\n/**\n * Computes the `x` and `y` coordinates that will place the floating element\n * next to a given reference element.\n */\nconst computePosition = (reference, floating, options) => {\n  // This caches the expensive `getClippingElementAncestors` function so that\n  // multiple lifecycle resets re-use the same result. It only lives for a\n  // single call. If other functions become expensive, we can add them as well.\n  const cache = new Map();\n  const mergedOptions = {\n    platform,\n    ...options\n  };\n  const platformWithCache = {\n    ...mergedOptions.platform,\n    _c: cache\n  };\n  return computePosition$1(reference, floating, {\n    ...mergedOptions,\n    platform: platformWithCache\n  });\n};\n\nexport { arrow, autoPlacement, autoUpdate, computePosition, detectOverflow, flip, hide, inline, limitShift, offset, platform, shift, size };\n", "import * as i0 from '@angular/core';\nimport { EventEmitter, Directive, Input, Output, HostBinding, Component, ViewChild, NgModule } from '@angular/core';\nimport { autoUpdate, computePosition, offset, flip, arrow } from '@floating-ui/dom';\nimport * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nconst _c0 = [\"content\"];\nconst _c1 = [\"*\"];\nfunction PopoverContent_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 4);\n  }\n}\nfunction PopoverContent_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 5);\n  }\n}\nclass PopoverContainer {\n  constructor(elementRef, ngZone, renderer, changeDetectorRef) {\n    this.elementRef = elementRef;\n    this.ngZone = ngZone;\n    this.renderer = renderer;\n    this.changeDetectorRef = changeDetectorRef;\n    this._align = \"bottom\";\n    this.alignmentClassPrefix = \"cds--popover--\";\n    /**\n     * Emits an event when the dialog is closed\n     */\n    this.onClose = new EventEmitter();\n    /**\n     * Emits an event when the dialog is opened\n     */\n    this.onOpen = new EventEmitter();\n    /**\n     * Emits an event when the state of `isOpen` changes. Allows `isOpen` to be double bound\n     */\n    this.isOpenChange = new EventEmitter();\n    /**\n     * Show caret at the alignment position\n     */\n    this.caret = true;\n    /**\n     * Enable drop shadow around the popover container\n     */\n    this.dropShadow = true;\n    /**\n     * Enable high contrast for popover container\n     */\n    this.highContrast = false;\n    /**\n     * **Experimental**: Use floating-ui to position the tooltip\n     * This is not toggleable - should be assigned once\n     */\n    this.autoAlign = false;\n    this.containerClass = true;\n    this.isOpen = false;\n  }\n  /**\n   * Set alignment of popover\n   * As of v5, `oldPlacements` are now deprecated in favor of Placements\n   *\n   * When `autoAlign` is set to `true`, alignment may change for best placement\n   */\n  set align(alignment) {\n    // If alignment is not passed, the default value will be `undefined`.\n    if (!alignment) {\n      return;\n    }\n    const previousAlignment = this._align;\n    switch (alignment) {\n      case \"top-left\":\n        this._align = \"top-start\";\n        break;\n      case \"top-right\":\n        this._align = \"top-end\";\n        break;\n      case \"bottom-left\":\n        this._align = \"bottom-start\";\n        break;\n      case \"bottom-right\":\n        this._align = \"bottom-end\";\n        break;\n      case \"left-top\":\n        this._align = \"left-start\";\n        break;\n      case \"left-bottom\":\n        this._align = \"left-end\";\n        break;\n      case \"right-top\":\n        this._align = \"right-start\";\n        break;\n      case \"right-bottom\":\n        this._align = \"right-end\";\n        break;\n      default:\n        this._align = alignment;\n        break;\n    }\n    this.updateAlignmentClass(this._align, previousAlignment);\n  }\n  /**\n   * Handles emitting open/close event\n   * @param open - Is the popover container open\n   * @param event - Event\n   */\n  handleChange(open, event) {\n    // We only emit the event when parameter has an event to keep existing behavior\n    if (this.isOpen !== open && event) {\n      this.isOpenChange.emit(open);\n    }\n    if (open) {\n      if (event) {\n        this.onOpen.emit(event);\n      }\n      // when auto alignment is enabled, use auto update to set the placement for the element\n      if (this.autoAlign) {\n        if (this.caretRef) {\n          // Get caret offset/height property\n          // Getting computed styles once every open, otherwise expensive.\n          const computedStyle = getComputedStyle(this.caretRef);\n          const offset = computedStyle.getPropertyValue(\"--cds-popover-offset\");\n          const height = computedStyle.getPropertyValue(\"--cds-popover-caret-height\");\n          this.caretOffset = (offset?.includes(\"px\") ? Number(offset.split(\"px\", 1)[0]) : Number(offset.split(\"rem\", 1)[0]) * 16) || 10;\n          this.caretHeight = (height?.includes(\"px\") ? Number(height.split(\"px\", 1)[0]) : Number(height.split(\"rem\", 1)[0]) * 16) || 6;\n        }\n        if (this.elementRef.nativeElement && this.popoverContentRef) {\n          this.unmountFloatingElement = autoUpdate(this.elementRef.nativeElement, this.popoverContentRef, this.recomputePosition.bind(this));\n        }\n      }\n    } else {\n      this.cleanUp();\n      if (event) {\n        this.onClose.emit(event);\n      }\n    }\n    this.isOpen = open;\n    this.changeDetectorRef.markForCheck();\n  }\n  roundByDPR(value) {\n    const dpr = window.devicePixelRatio || 1;\n    return Math.round(value * dpr) / dpr;\n  }\n  /**\n   * Compute position of tooltip when autoAlign is enabled\n   */\n  recomputePosition() {\n    // Run outside of angular zone to avoid unnecessary change detection and rely on floating-ui\n    this.ngZone.runOutsideAngular(async () => {\n      const {\n        x,\n        y,\n        placement,\n        middlewareData\n      } = await computePosition(this.elementRef.nativeElement, this.popoverContentRef, {\n        placement: this._align,\n        strategy: \"fixed\",\n        middleware: [offset(this.caretOffset), flip({\n          fallbackAxisSideDirection: \"start\"\n        }), arrow({\n          element: this.caretRef\n        })]\n      });\n      const previousAlignment = this._align;\n      this._align = placement;\n      this.updateAlignmentClass(this._align, previousAlignment);\n      // Using CSSOM to manipulate CSS to avoid content security policy inline-src\n      // https://github.com/w3c/webappsec-csp/issues/212\n      Object.assign(this.popoverContentRef.style, {\n        position: \"fixed\",\n        top: \"0\",\n        left: \"0\",\n        // Using transform instead of top/left position to improve performance\n        transform: `translate(${this.roundByDPR(x)}px,${this.roundByDPR(y)}px)`\n      });\n      if (middlewareData.arrow) {\n        const {\n          x: arrowX,\n          y: arrowY\n        } = middlewareData.arrow;\n        const staticSide = {\n          top: \"bottom\",\n          right: \"left\",\n          bottom: \"top\",\n          left: \"right\"\n        }[placement.split(\"-\")[0]];\n        this.caretRef.style.left = arrowX != null ? `${arrowX}px` : \"\";\n        this.caretRef.style.top = arrowY != null ? `${arrowY}px` : \"\";\n        this.caretRef.style.right = \"\";\n        this.caretRef.style.bottom = \"\";\n        if (staticSide) {\n          this.caretRef.style[staticSide] = `${-this.caretHeight}px`;\n        }\n      }\n    });\n  }\n  /**\n   * Close the popover and reopen it with updated values without emitting an event\n   * @param changes\n   */\n  ngOnChanges(changes) {\n    // Close and reopen the popover, handle alignment/programmatic open/close\n    const originalState = this.isOpen;\n    this.handleChange(false);\n    // Ignore first change since content is not initialized\n    if (changes.autoAlign && !changes.autoAlign.firstChange) {\n      // Reset the inline styles\n      this.popoverContentRef = this.elementRef.nativeElement.querySelector(\".cds--popover-content\");\n      this.popoverContentRef.setAttribute(\"style\", \"\");\n      this.caretRef = this.elementRef.nativeElement.querySelector(\"span.cds--popover-caret\");\n    }\n    this.handleChange(originalState);\n  }\n  /**\n   * Handle initialization of element\n   */\n  ngAfterViewInit() {\n    this.initializeReferences();\n  }\n  initializeReferences() {\n    this.updateAlignmentClass(this._align);\n    // Initialize html references since they will not change and are required for popover components\n    this.popoverContentRef = this.elementRef.nativeElement.querySelector(\".cds--popover-content\");\n    this.caretRef = this.elementRef.nativeElement.querySelector(\"span.cds--popover-caret\");\n    // Handle initial isOpen\n    this.handleChange(this.isOpen);\n  }\n  /**\n   * Clean up\n   */\n  ngOnDestroy() {\n    this.cleanUp();\n  }\n  /**\n   * Clean up `autoUpdate` if auto alignment is enabled\n   */\n  cleanUp() {\n    if (this.unmountFloatingElement) {\n      this.unmountFloatingElement();\n    }\n    this.unmountFloatingElement = undefined;\n  }\n  /**\n   * Replace existing previous alignment class with new\n   * @param previousAlignment\n   */\n  updateAlignmentClass(newAlignment, previousAlignment) {\n    if (this.elementRef.nativeElement && previousAlignment !== newAlignment) {\n      const regexp = new RegExp(\"right|top|left|bottom\");\n      // Since we are constantly switching, it's safer to delete all matching class names\n      this.elementRef.nativeElement.classList.forEach(className => {\n        if (regexp.test(className)) {\n          this.renderer.removeClass(this.elementRef.nativeElement, `${className}`);\n        }\n      });\n      this.renderer.addClass(this.elementRef.nativeElement, `${this.alignmentClassPrefix}${newAlignment}`);\n    }\n  }\n}\nPopoverContainer.ɵfac = function PopoverContainer_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || PopoverContainer)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n};\nPopoverContainer.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: PopoverContainer,\n  selectors: [[\"\", \"cdsPopover\", \"\"], [\"\", \"ibmPopover\", \"\"]],\n  hostVars: 12,\n  hostBindings: function PopoverContainer_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"cds--popover--caret\", ctx.caret)(\"cds--popover--drop-shadow\", ctx.dropShadow)(\"cds--popover--high-contrast\", ctx.highContrast)(\"cds--popover--auto-align\", ctx.autoAlign)(\"cds--popover-container\", ctx.containerClass)(\"cds--popover--open\", ctx.isOpen);\n    }\n  },\n  inputs: {\n    align: \"align\",\n    caret: \"caret\",\n    dropShadow: \"dropShadow\",\n    highContrast: \"highContrast\",\n    autoAlign: \"autoAlign\",\n    isOpen: \"isOpen\"\n  },\n  outputs: {\n    onClose: \"onClose\",\n    onOpen: \"onOpen\",\n    isOpenChange: \"isOpenChange\"\n  },\n  standalone: false,\n  features: [i0.ɵɵNgOnChangesFeature]\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PopoverContainer, [{\n    type: Directive,\n    args: [{\n      selector: \"[cdsPopover], [ibmPopover]\"\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i0.Renderer2\n    }, {\n      type: i0.ChangeDetectorRef\n    }];\n  }, {\n    align: [{\n      type: Input\n    }],\n    onClose: [{\n      type: Output\n    }],\n    onOpen: [{\n      type: Output\n    }],\n    isOpenChange: [{\n      type: Output\n    }],\n    caret: [{\n      type: HostBinding,\n      args: [\"class.cds--popover--caret\"]\n    }, {\n      type: Input\n    }],\n    dropShadow: [{\n      type: HostBinding,\n      args: [\"class.cds--popover--drop-shadow\"]\n    }, {\n      type: Input\n    }],\n    highContrast: [{\n      type: HostBinding,\n      args: [\"class.cds--popover--high-contrast\"]\n    }, {\n      type: Input\n    }],\n    autoAlign: [{\n      type: HostBinding,\n      args: [\"class.cds--popover--auto-align\"]\n    }, {\n      type: Input\n    }],\n    containerClass: [{\n      type: HostBinding,\n      args: [\"class.cds--popover-container\"]\n    }],\n    isOpen: [{\n      type: Input\n    }, {\n      type: HostBinding,\n      args: [\"class.cds--popover--open\"]\n    }]\n  });\n})();\n\n/**\n * [See demo](../../?path=/story/components-popover--basic)\n */\nclass PopoverContent {\n  constructor(changeDetectorRef) {\n    this.changeDetectorRef = changeDetectorRef;\n    this.popoverClass = true;\n    this.autoAlign = false;\n  }\n  ngAfterViewInit() {\n    if (this.popoverContent) {\n      // Check we are in a popover with autoAlign enabled\n      this.autoAlign = !!this.popoverContent.nativeElement.closest(\".cds--popover--auto-align\");\n      // Run change detection manually to resolve ExpressionHasChanged\n      this.changeDetectorRef.detectChanges();\n    }\n  }\n}\nPopoverContent.ɵfac = function PopoverContent_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || PopoverContent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n};\nPopoverContent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: PopoverContent,\n  selectors: [[\"cds-popover-content\"], [\"ibm-popover-content\"]],\n  viewQuery: function PopoverContent_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c0, 5);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.popoverContent = _t.first);\n    }\n  },\n  hostVars: 2,\n  hostBindings: function PopoverContent_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"cds--popover\", ctx.popoverClass);\n    }\n  },\n  standalone: false,\n  ngContentSelectors: _c1,\n  decls: 6,\n  vars: 2,\n  consts: [[\"content\", \"\"], [1, \"cds--popover-content\"], [\"class\", \"cds--popover-caret cds--popover--auto-align\", 4, \"ngIf\"], [\"class\", \"cds--popover-caret\", 4, \"ngIf\"], [1, \"cds--popover-caret\", \"cds--popover--auto-align\"], [1, \"cds--popover-caret\"]],\n  template: function PopoverContent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵelementStart(0, \"span\", 1, 0)(2, \"div\");\n      i0.ɵɵprojection(3);\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(4, PopoverContent_span_4_Template, 1, 0, \"span\", 2);\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(5, PopoverContent_span_5_Template, 1, 0, \"span\", 3);\n    }\n    if (rf & 2) {\n      i0.ɵɵadvance(4);\n      i0.ɵɵproperty(\"ngIf\", ctx.autoAlign);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", !ctx.autoAlign);\n    }\n  },\n  dependencies: [i1.NgIf],\n  encapsulation: 2\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PopoverContent, [{\n    type: Component,\n    args: [{\n      selector: \"cds-popover-content, ibm-popover-content\",\n      template: `\n\t\t<span class=\"cds--popover-content\" #content>\n\t\t\t<div>\n\t\t\t\t<ng-content></ng-content>\n\t\t\t</div>\n\t\t\t<span *ngIf=\"autoAlign\" class=\"cds--popover-caret cds--popover--auto-align\"></span>\n\t\t</span>\n\t\t<span *ngIf=\"!autoAlign\" class=\"cds--popover-caret\"></span>\n\t`\n    }]\n  }], function () {\n    return [{\n      type: i0.ChangeDetectorRef\n    }];\n  }, {\n    popoverClass: [{\n      type: HostBinding,\n      args: [\"class.cds--popover\"]\n    }],\n    popoverContent: [{\n      type: ViewChild,\n      args: [\"content\"]\n    }]\n  });\n})();\nclass PopoverModule {}\nPopoverModule.ɵfac = function PopoverModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || PopoverModule)();\n};\nPopoverModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: PopoverModule,\n  declarations: [PopoverContainer, PopoverContent],\n  imports: [CommonModule],\n  exports: [PopoverContainer, PopoverContent]\n});\nPopoverModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [CommonModule]\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PopoverModule, [{\n    type: NgModule,\n    args: [{\n      declarations: [PopoverContainer, PopoverContent],\n      exports: [PopoverContainer, PopoverContent],\n      imports: [CommonModule]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { PopoverContainer, PopoverContent, PopoverModule };\n", "import * as i0 from '@angular/core';\nimport { TemplateRef, Component, ChangeDetectionStrategy, HostBinding, Input, ViewChild, HostListener, NgModule } from '@angular/core';\nimport { PopoverContainer, PopoverModule } from 'carbon-components-angular/popover';\nimport * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\n\n/**\n * Default tooltip configuration for components to populate missing interface attributes\n */\nconst _c0 = [\"contentWrapper\"];\nconst _c1 = [\"*\"];\nconst _c2 = a0 => ({\n  $implicit: a0\n});\nfunction Tooltip_span_3_ng_container_1_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.description);\n  }\n}\nfunction Tooltip_span_3_ng_container_1_3_ng_template_0_Template(rf, ctx) {}\nfunction Tooltip_span_3_ng_container_1_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Tooltip_span_3_ng_container_1_3_ng_template_0_Template, 0, 0, \"ng-template\", 7);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.description)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c2, ctx_r0.templateContext));\n  }\n}\nfunction Tooltip_span_3_ng_container_1_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 8);\n  }\n}\nfunction Tooltip_span_3_ng_container_1_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 9);\n  }\n}\nfunction Tooltip_span_3_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"span\", 4);\n    i0.ɵɵtemplate(2, Tooltip_span_3_ng_container_1_ng_container_2_Template, 2, 1, \"ng-container\", 3)(3, Tooltip_span_3_ng_container_1_3_Template, 1, 4, null, 3)(4, Tooltip_span_3_ng_container_1_span_4_Template, 1, 0, \"span\", 5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, Tooltip_span_3_ng_container_1_span_5_Template, 1, 0, \"span\", 6);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.isTemplate(ctx_r0.description));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isTemplate(ctx_r0.description));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.autoAlign);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.autoAlign);\n  }\n}\nfunction Tooltip_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 2);\n    i0.ɵɵtemplate(1, Tooltip_span_3_ng_container_1_Template, 6, 4, \"ng-container\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"id\", ctx_r0.id);\n    i0.ɵɵattribute(\"aria-hidden\", !ctx_r0.isOpen);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.disabled);\n  }\n}\nfunction TooltipDefinition_span_2_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.description);\n  }\n}\nfunction TooltipDefinition_span_2_3_ng_template_0_Template(rf, ctx) {}\nfunction TooltipDefinition_span_2_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TooltipDefinition_span_2_3_ng_template_0_Template, 0, 0, \"ng-template\", 7);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.description)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c2, ctx_r1.templateContext));\n  }\n}\nfunction TooltipDefinition_span_2_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 8);\n  }\n}\nfunction TooltipDefinition_span_2_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 9);\n  }\n}\nfunction TooltipDefinition_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 2);\n    i0.ɵɵlistener(\"mousedown\", function TooltipDefinition_span_2_Template_span_mousedown_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onPopoverMouseDown());\n    })(\"mouseup\", function TooltipDefinition_span_2_Template_span_mouseup_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onPopoverMouseUp());\n    });\n    i0.ɵɵelementStart(1, \"span\", 3);\n    i0.ɵɵtemplate(2, TooltipDefinition_span_2_ng_container_2_Template, 2, 1, \"ng-container\", 4)(3, TooltipDefinition_span_2_3_Template, 1, 4, null, 4)(4, TooltipDefinition_span_2_span_4_Template, 1, 0, \"span\", 5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, TooltipDefinition_span_2_span_5_Template, 1, 0, \"span\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"id\", ctx_r1.id);\n    i0.ɵɵattribute(\"aria-hidden\", !ctx_r1.isOpen);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isTemplate(ctx_r1.description));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isTemplate(ctx_r1.description));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.autoAlign);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.autoAlign);\n  }\n}\nconst DEFAULT_TOOLTIP_CONFIG = {\n  align: \"bottom\",\n  caret: true,\n  dropShadow: true,\n  highContrast: true,\n  isOpen: false,\n  enterDelayMs: 100,\n  leaveDelayMs: 300\n};\n\n/**\n * Get started with importing the module:\n *\n * ```typescript\n * import { TooltipModule } from 'carbon-components-angular';\n * ```\n *\n * [See demo](../../?path=/story/components-tooltip--basic)\n */\nclass Tooltip extends PopoverContainer {\n  constructor(elementRef, ngZone, renderer, changeDetectorRef) {\n    super(elementRef, ngZone, renderer, changeDetectorRef);\n    this.elementRef = elementRef;\n    this.ngZone = ngZone;\n    this.renderer = renderer;\n    this.changeDetectorRef = changeDetectorRef;\n    this.tooltipClass = true;\n    this.id = `tooltip-${Tooltip.tooltipCount++}`;\n    /**\n     * Set delay before tooltip is shown\n     */\n    this.enterDelayMs = 100;\n    /**\n     * Set delay when tooltip disappears\n     */\n    this.leaveDelayMs = 300;\n    /**\n     * Prevent tooltip from showing, used by icon button\n     */\n    this.disabled = false;\n    this.highContrast = true;\n    this.dropShadow = false;\n  }\n  mouseenter(event) {\n    // If a mouseleave is triggered before the tooltip is displayed (before setTimeout of mouseenter completes)\n    // we trigger the mouseleave only avoiding having to unecessary show the tooltip\n    clearTimeout(this.timeoutId);\n    this.timeoutId = setTimeout(() => {\n      this.handleChange(true, event);\n    }, this.enterDelayMs);\n  }\n  mouseleave(event) {\n    // If a mouseleave is triggered before the tooltip is displayed (before setTimeout of mouseenter completes)\n    // we trigger the mouseleave only avoiding having to unecessary show the tooltip\n    clearTimeout(this.timeoutId);\n    this.timeoutId = setTimeout(() => {\n      this.handleChange(false, event);\n    }, this.leaveDelayMs);\n  }\n  hostkeys(event) {\n    if (open && event.key === \"Escape\") {\n      event.stopPropagation();\n      this.handleChange(false, event);\n    }\n  }\n  // We are not focusing on entire popover, only the trigger\n  handleFocus(event) {\n    this.handleChange(true, event);\n  }\n  handleFocusOut(event) {\n    this.handleChange(false, event);\n  }\n  isTemplate(value) {\n    return value instanceof TemplateRef;\n  }\n  /**\n   * Close the popover and reopen it with updated values without emitting an event\n   * @param changes\n   */\n  ngOnChanges(changes) {\n    // Close and reopen the popover, handle alignment/programmatic open/close\n    const originalState = this.isOpen;\n    this.handleChange(false);\n    // Ignore first change since content is not initialized\n    if (changes.autoAlign && !changes.autoAlign.firstChange || changes.disabled && !changes.disabled.firstChange && !changes.disabled.currentValue\n    // If description is set to empty string when open & autoAlign is true then set to a new value\n    // positioning of popover is broken because popover content ref/caret no longer exists\n    || changes.description) {\n      /**\n       * When `disabled` is `true`, popover content node is removed. So when re-enabling `disabled`,\n       * we manually update view so querySelector can detect the popover content node.\n       * Otherwise, the position of the popover will be incorrect when autoAlign is enabled.\n       */\n      this.changeDetectorRef.detectChanges();\n      // Reset the inline styles\n      this.popoverContentRef = this.elementRef.nativeElement.querySelector(\".cds--popover-content\");\n      this.popoverContentRef?.setAttribute(\"style\", \"\");\n      this.caretRef = this.elementRef.nativeElement.querySelector(\"span.cds--popover-caret\");\n    }\n    this.handleChange(originalState);\n  }\n  /**\n   * Check for any changes in the projected content & apply accessibility attribute if needed\n   */\n  ngAfterContentChecked() {\n    if (this.wrapper) {\n      const buttonElement = this.wrapper.nativeElement.querySelector(\"button\");\n      if (buttonElement && !buttonElement.getAttribute(\"aria-labelledby\")) {\n        buttonElement.setAttribute(\"aria-labelledby\", this.id);\n      }\n    }\n  }\n}\nTooltip.tooltipCount = 0;\nTooltip.ɵfac = function Tooltip_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || Tooltip)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n};\nTooltip.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: Tooltip,\n  selectors: [[\"cds-tooltip\"], [\"ibm-tooltip\"]],\n  viewQuery: function Tooltip_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c0, 5);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.wrapper = _t.first);\n    }\n  },\n  hostVars: 2,\n  hostBindings: function Tooltip_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"mouseenter\", function Tooltip_mouseenter_HostBindingHandler($event) {\n        return ctx.mouseenter($event);\n      })(\"mouseleave\", function Tooltip_mouseleave_HostBindingHandler($event) {\n        return ctx.mouseleave($event);\n      })(\"keyup\", function Tooltip_keyup_HostBindingHandler($event) {\n        return ctx.hostkeys($event);\n      })(\"focusin\", function Tooltip_focusin_HostBindingHandler($event) {\n        return ctx.handleFocus($event);\n      })(\"focusout\", function Tooltip_focusout_HostBindingHandler($event) {\n        return ctx.handleFocusOut($event);\n      });\n    }\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"cds--tooltip\", ctx.tooltipClass);\n    }\n  },\n  inputs: {\n    id: \"id\",\n    enterDelayMs: \"enterDelayMs\",\n    leaveDelayMs: \"leaveDelayMs\",\n    disabled: \"disabled\",\n    description: \"description\",\n    templateContext: \"templateContext\"\n  },\n  standalone: false,\n  features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature],\n  ngContentSelectors: _c1,\n  decls: 4,\n  vars: 1,\n  consts: [[\"contentWrapper\", \"\"], [\"class\", \"cds--popover\", \"role\", \"tooltip\", 3, \"id\", 4, \"ngIf\"], [\"role\", \"tooltip\", 1, \"cds--popover\", 3, \"id\"], [4, \"ngIf\"], [1, \"cds--popover-content\", \"cds--tooltip-content\"], [\"class\", \"cds--popover-caret cds--popover--auto-align\", 4, \"ngIf\"], [\"class\", \"cds--popover-caret\", 4, \"ngIf\"], [3, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [1, \"cds--popover-caret\", \"cds--popover--auto-align\"], [1, \"cds--popover-caret\"]],\n  template: function Tooltip_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵelementStart(0, \"span\", null, 0);\n      i0.ɵɵprojection(2);\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(3, Tooltip_span_3_Template, 2, 3, \"span\", 1);\n    }\n    if (rf & 2) {\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"ngIf\", ctx.description);\n    }\n  },\n  dependencies: [i1.NgIf, i1.NgTemplateOutlet],\n  encapsulation: 2,\n  changeDetection: 0\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Tooltip, [{\n    type: Component,\n    args: [{\n      selector: \"cds-tooltip, ibm-tooltip\",\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: `\n\t\t<span #contentWrapper>\n\t\t\t<ng-content></ng-content>\n\t\t</span>\n\t\t<span\n\t\t\t*ngIf=\"description\"\n\t\t\tclass=\"cds--popover\"\n\t\t\t[id]=\"id\"\n\t\t\t[attr.aria-hidden]=\"!isOpen\"\n\t\t\trole=\"tooltip\">\n\t\t\t<ng-container *ngIf=\"!disabled\">\n\t\t\t\t<span class=\"cds--popover-content cds--tooltip-content\">\n\t\t\t\t\t<ng-container *ngIf=\"!isTemplate(description)\">{{description}}</ng-container>\n\t\t\t\t\t<ng-template *ngIf=\"isTemplate(description)\" [ngTemplateOutlet]=\"description\" [ngTemplateOutletContext]=\"{ $implicit: templateContext }\"></ng-template>\n\t\t\t\t\t<span *ngIf=\"autoAlign\" class=\"cds--popover-caret cds--popover--auto-align\"></span>\n\t\t\t\t</span>\n\t\t\t\t<span *ngIf=\"!autoAlign\" class=\"cds--popover-caret\"></span>\n\t\t\t</ng-container>\n\t\t</span>\n\t`\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i0.Renderer2\n    }, {\n      type: i0.ChangeDetectorRef\n    }];\n  }, {\n    tooltipClass: [{\n      type: HostBinding,\n      args: [\"class.cds--tooltip\"]\n    }],\n    id: [{\n      type: Input\n    }],\n    enterDelayMs: [{\n      type: Input\n    }],\n    leaveDelayMs: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }],\n    description: [{\n      type: Input\n    }],\n    templateContext: [{\n      type: Input\n    }],\n    wrapper: [{\n      type: ViewChild,\n      args: [\"contentWrapper\"]\n    }],\n    mouseenter: [{\n      type: HostListener,\n      args: [\"mouseenter\", [\"$event\"]]\n    }],\n    mouseleave: [{\n      type: HostListener,\n      args: [\"mouseleave\", [\"$event\"]]\n    }],\n    hostkeys: [{\n      type: HostListener,\n      args: [\"keyup\", [\"$event\"]]\n    }],\n    handleFocus: [{\n      type: HostListener,\n      args: [\"focusin\", [\"$event\"]]\n    }],\n    handleFocusOut: [{\n      type: HostListener,\n      args: [\"focusout\", [\"$event\"]]\n    }]\n  });\n})();\n\n/**\n * Get started with importing the module:\n *\n * ```typescript\n * import { TooltipModule } from 'carbon-components-angular';\n * ```\n *\n * [See demo](../../?path=/story/components-tooltip-definition--basic)\n */\nclass TooltipDefinition extends PopoverContainer {\n  constructor(elementRef, ngZone, renderer, changeDetectorRef) {\n    super(elementRef, ngZone, renderer, changeDetectorRef);\n    this.elementRef = elementRef;\n    this.ngZone = ngZone;\n    this.renderer = renderer;\n    this.changeDetectorRef = changeDetectorRef;\n    this.id = `tooltip-definition-${TooltipDefinition.tooltipCount++}`;\n    this.openOnHover = false;\n    /**\n     * Helper variable to ensure button blur doesn't fire on `click` of popover content\n     */\n    this.isInteractingWithPopover = false;\n    this.highContrast = true;\n    this.dropShadow = false;\n  }\n  onBlur(event) {\n    // Only close if user is not interacting with popover content\n    if (!this.isInteractingWithPopover) {\n      this.handleChange(false, event);\n    }\n  }\n  onClick(event) {\n    if (event.button === 0) {\n      this.handleChange(!this.isOpen, event);\n    }\n  }\n  onPopoverMouseDown() {\n    this.isInteractingWithPopover = true;\n  }\n  onPopoverMouseUp() {\n    this.isInteractingWithPopover = false;\n  }\n  hostkeys(event) {\n    if (this.isOpen && event.key === \"Escape\") {\n      event.stopPropagation();\n      this.handleChange(false, event);\n    }\n  }\n  mouseleave(event) {\n    this.handleChange(false, event);\n  }\n  mouseenter(event) {\n    if (this.openOnHover) {\n      this.handleChange(true, event);\n    }\n  }\n  onFocus(event) {\n    this.handleChange(true, event);\n  }\n  isTemplate(value) {\n    return value instanceof TemplateRef;\n  }\n}\nTooltipDefinition.tooltipCount = 0;\nTooltipDefinition.ɵfac = function TooltipDefinition_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || TooltipDefinition)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n};\nTooltipDefinition.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: TooltipDefinition,\n  selectors: [[\"cds-tooltip-definition\"], [\"ibm-tooltip-definition\"]],\n  hostBindings: function TooltipDefinition_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"keyup\", function TooltipDefinition_keyup_HostBindingHandler($event) {\n        return ctx.hostkeys($event);\n      })(\"mouseleave\", function TooltipDefinition_mouseleave_HostBindingHandler($event) {\n        return ctx.mouseleave($event);\n      })(\"mouseenter\", function TooltipDefinition_mouseenter_HostBindingHandler($event) {\n        return ctx.mouseenter($event);\n      })(\"focusin\", function TooltipDefinition_focusin_HostBindingHandler($event) {\n        return ctx.onFocus($event);\n      });\n    }\n  },\n  inputs: {\n    id: \"id\",\n    description: \"description\",\n    templateContext: \"templateContext\",\n    openOnHover: \"openOnHover\"\n  },\n  standalone: false,\n  features: [i0.ɵɵInheritDefinitionFeature],\n  ngContentSelectors: _c1,\n  decls: 3,\n  vars: 4,\n  consts: [[\"type\", \"button\", 1, \"cds--definition-term\", 3, \"blur\", \"mousedown\"], [\"class\", \"cds--popover\", \"role\", \"tooltip\", 3, \"id\", \"mousedown\", \"mouseup\", 4, \"ngIf\"], [\"role\", \"tooltip\", 1, \"cds--popover\", 3, \"mousedown\", \"mouseup\", \"id\"], [\"aria-live\", \"polite\", 1, \"cds--popover-content\", \"cds--definition-tooltip\"], [4, \"ngIf\"], [\"class\", \"cds--popover-caret cds--popover--auto-align\", 4, \"ngIf\"], [\"class\", \"cds--popover-caret\", 4, \"ngIf\"], [3, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [1, \"cds--popover-caret\", \"cds--popover--auto-align\"], [1, \"cds--popover-caret\"]],\n  template: function TooltipDefinition_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵelementStart(0, \"button\", 0);\n      i0.ɵɵlistener(\"blur\", function TooltipDefinition_Template_button_blur_0_listener($event) {\n        return ctx.onBlur($event);\n      })(\"mousedown\", function TooltipDefinition_Template_button_mousedown_0_listener($event) {\n        return ctx.onClick($event);\n      });\n      i0.ɵɵprojection(1);\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(2, TooltipDefinition_span_2_Template, 6, 6, \"span\", 1);\n    }\n    if (rf & 2) {\n      i0.ɵɵattribute(\"aria-controls\", ctx.id)(\"aria-expanded\", ctx.isOpen)(\"aria-describedby\", ctx.isOpen ? ctx.id : null);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.description);\n    }\n  },\n  dependencies: [i1.NgIf, i1.NgTemplateOutlet],\n  encapsulation: 2,\n  changeDetection: 0\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TooltipDefinition, [{\n    type: Component,\n    args: [{\n      selector: \"cds-tooltip-definition, ibm-tooltip-definition\",\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: `\n\t\t<button\n\t\t\tclass=\"cds--definition-term\"\n\t\t\t[attr.aria-controls]=\"id\"\n\t\t\t[attr.aria-expanded]=\"isOpen\"\n\t\t\t[attr.aria-describedby]=\"isOpen ? id : null\"\n\t\t\t(blur)=\"onBlur($event)\"\n\t\t\t(mousedown)=\"onClick($event)\"\n\t\t\ttype=\"button\">\n\t\t\t<ng-content></ng-content>\n\t\t</button>\n\t\t<span\n\t\t\t*ngIf=\"description\"\n\t\t\tclass=\"cds--popover\"\n\t\t\t[id]=\"id\"\n\t\t\t[attr.aria-hidden]=\"!isOpen\"\n\t\t\trole=\"tooltip\"\n\t\t\t(mousedown)=\"onPopoverMouseDown()\"\n\t\t\t(mouseup)=\"onPopoverMouseUp()\">\n\t\t\t<span class=\"cds--popover-content cds--definition-tooltip\" aria-live=\"polite\">\n\t\t\t\t<ng-container *ngIf=\"!isTemplate(description)\">{{description}}</ng-container>\n\t\t\t\t<ng-template *ngIf=\"isTemplate(description)\" [ngTemplateOutlet]=\"description\" [ngTemplateOutletContext]=\"{ $implicit: templateContext }\"></ng-template>\n\t\t\t\t<span *ngIf=\"autoAlign\" class=\"cds--popover-caret cds--popover--auto-align\"></span>\n\t\t\t</span>\n\t\t\t<span *ngIf=\"!autoAlign\" class=\"cds--popover-caret\"></span>\n\t\t</span>\n\t`\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i0.Renderer2\n    }, {\n      type: i0.ChangeDetectorRef\n    }];\n  }, {\n    id: [{\n      type: Input\n    }],\n    description: [{\n      type: Input\n    }],\n    templateContext: [{\n      type: Input\n    }],\n    openOnHover: [{\n      type: Input\n    }],\n    hostkeys: [{\n      type: HostListener,\n      args: [\"keyup\", [\"$event\"]]\n    }],\n    mouseleave: [{\n      type: HostListener,\n      args: [\"mouseleave\", [\"$event\"]]\n    }],\n    mouseenter: [{\n      type: HostListener,\n      args: [\"mouseenter\", [\"$event\"]]\n    }],\n    onFocus: [{\n      type: HostListener,\n      args: [\"focusin\", [\"$event\"]]\n    }]\n  });\n})();\nclass TooltipModule {}\nTooltipModule.ɵfac = function TooltipModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || TooltipModule)();\n};\nTooltipModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: TooltipModule,\n  declarations: [Tooltip, TooltipDefinition],\n  imports: [CommonModule, PopoverModule],\n  exports: [Tooltip, TooltipDefinition]\n});\nTooltipModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [CommonModule, PopoverModule]\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TooltipModule, [{\n    type: NgModule,\n    args: [{\n      declarations: [Tooltip, TooltipDefinition],\n      exports: [Tooltip, TooltipDefinition],\n      imports: [CommonModule, PopoverModule]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { DEFAULT_TOOLTIP_CONFIG, Tooltip, TooltipDefinition, TooltipModule };\n", "import * as i0 from '@angular/core';\nimport { Directive, Input, HostBinding, Component, EventEmitter, ViewChild, Output, NgModule } from '@angular/core';\nimport * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i2 from 'carbon-components-angular/tooltip';\nimport { TooltipModule } from 'carbon-components-angular/tooltip';\n\n/**\n * A convenience directive for applying styling to a button. Get started with importing the module:\n *\n * ```typescript\n * import { ButtonModule } from 'carbon-components-angular';\n * ```\n *\n * Example:\n *\n * ```html\n * <button cdsButton>A button</button>\n * <button cdsButton=\"secondary\">A secondary button</button>\n * ```\n *\n * See the [vanilla carbon docs](http://www.carbondesignsystem.com/components/button/code) for more detail.\n *\n * [See demo](../../?path=/story/components-button--basic)\n */\nconst _c0 = [\"*\"];\nconst _c1 = [\"button\"];\nclass Button {\n  constructor() {\n    /**\n     * Sets the button type\n     * Accepts `ButtonType` or nothing (empty string which is equivalent to \"primary\")\n     * Empty string has been added as an option for Angular 16+ to resolve type errors\n     */\n    this.cdsButton = \"primary\";\n    /**\n     * Set to `true` for a skeleton state button\n     */\n    this.skeleton = false;\n    /**\n     * Set to `true` if the button contains only an icon\n     * This should only be used for creating custom icon buttons, otherwise use\n     * `<cds-icon-button></cds-icon-button>` component\n     */\n    this.iconOnly = false;\n    /**\n     * Set to `true` for a \"expressive\" style button\n     */\n    this.isExpressive = false;\n    // a whole lot of HostBindings ... this way we don't have to touch the elementRef directly\n    this.baseClass = true;\n  }\n  /**\n   * @deprecated as of v5 - Use `cdsButton` input property instead\n   */\n  set ibmButton(type) {\n    this.cdsButton = type;\n  }\n  get primaryButton() {\n    return this.cdsButton === \"primary\" || !this.cdsButton;\n  }\n  get secondaryButton() {\n    return this.cdsButton === \"secondary\";\n  }\n  get tertiaryButton() {\n    return this.cdsButton === \"tertiary\";\n  }\n  get ghostButton() {\n    return this.cdsButton === \"ghost\";\n  }\n  get dangerButton() {\n    return this.cdsButton === \"danger\" || this.cdsButton === \"danger--primary\";\n  }\n  get dangerTertiary() {\n    return this.cdsButton === \"danger--tertiary\";\n  }\n  get dangerGhost() {\n    return this.cdsButton === \"danger--ghost\";\n  }\n  /**\n   * @todo remove `cds--btn--${size}` classes in v12\n   */\n  get smallSize() {\n    return this.size === \"sm\" && !this.isExpressive;\n  }\n  get mediumSize() {\n    return this.size === \"md\" && !this.isExpressive;\n  }\n  get largeSize() {\n    return this.size === \"lg\";\n  }\n  get extraLargeSize() {\n    return this.size === \"xl\";\n  }\n  get twoExtraLargeSize() {\n    return this.size === \"2xl\";\n  }\n  // Size classes\n  get smallLayoutSize() {\n    return this.size === \"sm\" && !this.isExpressive;\n  }\n  get mediumLayoutSize() {\n    return this.size === \"md\" && !this.isExpressive;\n  }\n  get largeLayoutSize() {\n    return this.size === \"lg\";\n  }\n  get extraLargeLayoutSize() {\n    return this.size === \"xl\";\n  }\n  get twoExtraLargeLayoutSize() {\n    return this.size === \"2xl\";\n  }\n}\nButton.ɵfac = function Button_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || Button)();\n};\nButton.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: Button,\n  selectors: [[\"\", \"cdsButton\", \"\"], [\"\", \"ibmButton\", \"\"]],\n  hostVars: 42,\n  hostBindings: function Button_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"cds--skeleton\", ctx.skeleton)(\"cds--btn--icon-only\", ctx.iconOnly)(\"cds--btn--expressive\", ctx.isExpressive)(\"cds--btn\", ctx.baseClass)(\"cds--btn--primary\", ctx.primaryButton)(\"cds--btn--secondary\", ctx.secondaryButton)(\"cds--btn--tertiary\", ctx.tertiaryButton)(\"cds--btn--ghost\", ctx.ghostButton)(\"cds--btn--danger\", ctx.dangerButton)(\"cds--btn--danger--tertiary\", ctx.dangerTertiary)(\"cds--btn--danger--ghost\", ctx.dangerGhost)(\"cds--btn--sm\", ctx.smallSize)(\"cds--btn--md\", ctx.mediumSize)(\"cds--btn--lg\", ctx.largeSize)(\"cds--btn--xl\", ctx.extraLargeSize)(\"cds--btn--2xl\", ctx.twoExtraLargeSize)(\"cds--layout--size-sm\", ctx.smallLayoutSize)(\"cds--layout--size-md\", ctx.mediumLayoutSize)(\"cds--layout--size-lg\", ctx.largeLayoutSize)(\"cds--layout--size-xl\", ctx.extraLargeLayoutSize)(\"cds--layout--size-2xl\", ctx.twoExtraLargeLayoutSize);\n    }\n  },\n  inputs: {\n    ibmButton: \"ibmButton\",\n    cdsButton: \"cdsButton\",\n    size: \"size\",\n    skeleton: \"skeleton\",\n    iconOnly: \"iconOnly\",\n    isExpressive: \"isExpressive\"\n  },\n  standalone: false\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Button, [{\n    type: Directive,\n    args: [{\n      selector: \"[cdsButton], [ibmButton]\"\n    }]\n  }], null, {\n    ibmButton: [{\n      type: Input\n    }],\n    cdsButton: [{\n      type: Input\n    }],\n    size: [{\n      type: Input\n    }],\n    skeleton: [{\n      type: HostBinding,\n      args: [\"class.cds--skeleton\"]\n    }, {\n      type: Input\n    }],\n    iconOnly: [{\n      type: HostBinding,\n      args: [\"class.cds--btn--icon-only\"]\n    }, {\n      type: Input\n    }],\n    isExpressive: [{\n      type: HostBinding,\n      args: [\"class.cds--btn--expressive\"]\n    }, {\n      type: Input\n    }],\n    baseClass: [{\n      type: HostBinding,\n      args: [\"class.cds--btn\"]\n    }],\n    primaryButton: [{\n      type: HostBinding,\n      args: [\"class.cds--btn--primary\"]\n    }],\n    secondaryButton: [{\n      type: HostBinding,\n      args: [\"class.cds--btn--secondary\"]\n    }],\n    tertiaryButton: [{\n      type: HostBinding,\n      args: [\"class.cds--btn--tertiary\"]\n    }],\n    ghostButton: [{\n      type: HostBinding,\n      args: [\"class.cds--btn--ghost\"]\n    }],\n    dangerButton: [{\n      type: HostBinding,\n      args: [\"class.cds--btn--danger\"]\n    }],\n    dangerTertiary: [{\n      type: HostBinding,\n      args: [\"class.cds--btn--danger--tertiary\"]\n    }],\n    dangerGhost: [{\n      type: HostBinding,\n      args: [\"class.cds--btn--danger--ghost\"]\n    }],\n    smallSize: [{\n      type: HostBinding,\n      args: [\"class.cds--btn--sm\"]\n    }],\n    mediumSize: [{\n      type: HostBinding,\n      args: [\"class.cds--btn--md\"]\n    }],\n    largeSize: [{\n      type: HostBinding,\n      args: [\"class.cds--btn--lg\"]\n    }],\n    extraLargeSize: [{\n      type: HostBinding,\n      args: [\"class.cds--btn--xl\"]\n    }],\n    twoExtraLargeSize: [{\n      type: HostBinding,\n      args: [\"class.cds--btn--2xl\"]\n    }],\n    smallLayoutSize: [{\n      type: HostBinding,\n      args: [\"class.cds--layout--size-sm\"]\n    }],\n    mediumLayoutSize: [{\n      type: HostBinding,\n      args: [\"class.cds--layout--size-md\"]\n    }],\n    largeLayoutSize: [{\n      type: HostBinding,\n      args: [\"class.cds--layout--size-lg\"]\n    }],\n    extraLargeLayoutSize: [{\n      type: HostBinding,\n      args: [\"class.cds--layout--size-xl\"]\n    }],\n    twoExtraLargeLayoutSize: [{\n      type: HostBinding,\n      args: [\"class.cds--layout--size-2xl\"]\n    }]\n  });\n})();\n\n/**\n * Get started with importing the module:\n *\n * ```typescript\n * import { ButtonModule } from 'carbon-components-angular';\n * ```\n *\n * [See demo](../../?path=/story/components-button-button-set--basic)\n */\nclass ButtonSet {\n  constructor() {\n    this.buttonSetClass = true;\n  }\n}\nButtonSet.ɵfac = function ButtonSet_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || ButtonSet)();\n};\nButtonSet.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: ButtonSet,\n  selectors: [[\"cds-button-set\"], [\"ibm-button-set\"]],\n  hostVars: 2,\n  hostBindings: function ButtonSet_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"cds--btn-set\", ctx.buttonSetClass);\n    }\n  },\n  standalone: false,\n  ngContentSelectors: _c0,\n  decls: 1,\n  vars: 0,\n  template: function ButtonSet_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵprojection(0);\n    }\n  },\n  encapsulation: 2\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ButtonSet, [{\n    type: Component,\n    args: [{\n      selector: \"cds-button-set, ibm-button-set\",\n      template: \"<ng-content></ng-content>\"\n    }]\n  }], null, {\n    buttonSetClass: [{\n      type: HostBinding,\n      args: [\"class.cds--btn-set\"]\n    }]\n  });\n})();\n\n/**\n * Base button with common input properties for configuring icon button.\n * Extend class to inherit @Input meta data\n *\n * Used by pagination nav icon button, code snippet, etc.\n */\nclass BaseIconButton {\n  constructor() {\n    /**\n     * Set to `false` to hide caret\n     */\n    this.caret = true;\n    /**\n     * Set to `false` to hide shadow\n     */\n    this.dropShadow = true;\n    /**\n     * Set to `true` to enable high contrast\n     */\n    this.highContrast = true;\n    /**\n     * Set to `true` to have the popover open by default\n     */\n    this.isOpen = false;\n    /**\n     * Set popover alignment\n     */\n    this.align = \"bottom\";\n    /**\n     * **Experimental**: Use floating-ui to position the tooltip\n     * This is not toggleable - should be assigned once\n     */\n    this.autoAlign = false;\n    /**\n     * Set delay before tooltip is shown\n     */\n    this.enterDelayMs = 100;\n    /**\n     * Set delay when tooltip disappears\n     */\n    this.leaveDelayMs = 300;\n  }\n}\nBaseIconButton.ɵfac = function BaseIconButton_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || BaseIconButton)();\n};\nBaseIconButton.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: BaseIconButton,\n  selectors: [[\"ng-component\"]],\n  inputs: {\n    caret: \"caret\",\n    dropShadow: \"dropShadow\",\n    highContrast: \"highContrast\",\n    isOpen: \"isOpen\",\n    align: \"align\",\n    autoAlign: \"autoAlign\",\n    enterDelayMs: \"enterDelayMs\",\n    leaveDelayMs: \"leaveDelayMs\"\n  },\n  standalone: false,\n  decls: 0,\n  vars: 0,\n  template: function BaseIconButton_Template(rf, ctx) {},\n  encapsulation: 2\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BaseIconButton, [{\n    type: Component,\n    args: [{\n      template: \"\"\n    }]\n  }], null, {\n    caret: [{\n      type: Input\n    }],\n    dropShadow: [{\n      type: Input\n    }],\n    highContrast: [{\n      type: Input\n    }],\n    isOpen: [{\n      type: Input\n    }],\n    align: [{\n      type: Input\n    }],\n    autoAlign: [{\n      type: Input\n    }],\n    enterDelayMs: [{\n      type: Input\n    }],\n    leaveDelayMs: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Get started with importing the module:\n *\n * ```typescript\n * import { ButtonModule } from 'carbon-components-angular';\n * ```\n *\n * [See demo](../../?path=/story/components-button-icon-button--basic)\n */\nclass IconButton extends BaseIconButton {\n  constructor(renderer) {\n    super();\n    this.renderer = renderer;\n    /**\n     * Override id\n     */\n    this.buttonId = `icon-btn-${IconButton.iconButtonCounter++}`;\n    /**\n     * Sets the button type.\n     */\n    this.kind = \"primary\";\n    /**\n     * Specify the size of the button.\n     */\n    this.size = \"lg\";\n    /**\n     * Set button type, `button` by default\n     */\n    this.type = \"button\";\n    /**\n     * Set to `true` to make button expressive\n     */\n    this.isExpressive = false;\n    /**\n     * Set to `true` to disable button\n     */\n    this.disabled = false;\n    /**\n     * Indicates whether the tooltip should be shown when the button is disabled\n     */\n    this.showTooltipWhenDisabled = false;\n    /**\n     * Common button events\n     */\n    this.click = new EventEmitter();\n    this.focus = new EventEmitter();\n    this.blur = new EventEmitter();\n    /**\n     * Event to emit when click event is fired from tooltip\n     */\n    this.tooltipClick = new EventEmitter();\n    this.classList = {};\n    this.attributeList = {};\n  }\n  /**\n   * Pass global carbon classes to icon button\n   */\n  set buttonNgClass(obj) {\n    this.classList = Object.assign({\n      \"cds--btn--disabled\": this.disabled\n    }, obj);\n  }\n  get buttonNgClass() {\n    return this.classList;\n  }\n  /**\n   * @param obj: { [key: string]: string\n   * User can pass additional button attributes if component property does not already exist\n   * Key is the attribute name & value is the attribute value for the button\n   */\n  set buttonAttributes(obj) {\n    if (this.button) {\n      // Remove old attributes\n      Object.keys(this.attributeList).forEach(key => {\n        this.renderer.removeAttribute(this.button.nativeElement, key);\n      });\n      // Set new attributes\n      Object.keys(obj).forEach(key => {\n        this.renderer.setAttribute(this.button.nativeElement, key, obj[key]);\n      });\n    }\n    // Set new attributes\n    this.attributeList = obj;\n  }\n  get buttonAttributes() {\n    return this.buttonAttributes;\n  }\n  ngAfterViewInit() {\n    // Set attributes once element is found\n    this.buttonAttributes = this.attributeList;\n  }\n  /**\n   * Stop propogation of click event\n   * Else double fires (click) event\n   */\n  emitClickEvent(event, element = \"button\") {\n    event.preventDefault();\n    event.stopPropagation();\n    // Prevents (click) event from bubbling since it would appear user clicked the `button`\n    if (element === \"tooltip\") {\n      this.tooltipClick.emit(event);\n      return;\n    }\n    this.click.emit(event);\n  }\n}\nIconButton.iconButtonCounter = 0;\nIconButton.ɵfac = function IconButton_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || IconButton)(i0.ɵɵdirectiveInject(i0.Renderer2));\n};\nIconButton.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: IconButton,\n  selectors: [[\"cds-icon-button\"], [\"ibm-icon-button\"]],\n  viewQuery: function IconButton_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c1, 5);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.button = _t.first);\n    }\n  },\n  inputs: {\n    buttonNgClass: \"buttonNgClass\",\n    buttonAttributes: \"buttonAttributes\",\n    buttonId: \"buttonId\",\n    kind: \"kind\",\n    size: \"size\",\n    type: \"type\",\n    isExpressive: \"isExpressive\",\n    disabled: \"disabled\",\n    description: \"description\",\n    showTooltipWhenDisabled: \"showTooltipWhenDisabled\"\n  },\n  outputs: {\n    click: \"click\",\n    focus: \"focus\",\n    blur: \"blur\",\n    tooltipClick: \"tooltipClick\"\n  },\n  standalone: false,\n  features: [i0.ɵɵInheritDefinitionFeature],\n  ngContentSelectors: _c0,\n  decls: 4,\n  vars: 18,\n  consts: [[\"button\", \"\"], [1, \"cds--icon-tooltip\", 3, \"click\", \"description\", \"disabled\", \"caret\", \"dropShadow\", \"highContrast\", \"isOpen\", \"align\", \"autoAlign\", \"enterDelayMs\", \"leaveDelayMs\"], [3, \"click\", \"focus\", \"blur\", \"id\", \"disabled\", \"iconOnly\", \"ngClass\", \"cdsButton\", \"size\", \"isExpressive\"]],\n  template: function IconButton_Template(rf, ctx) {\n    if (rf & 1) {\n      const _r1 = i0.ɵɵgetCurrentView();\n      i0.ɵɵprojectionDef();\n      i0.ɵɵelementStart(0, \"cds-tooltip\", 1);\n      i0.ɵɵlistener(\"click\", function IconButton_Template_cds_tooltip_click_0_listener($event) {\n        i0.ɵɵrestoreView(_r1);\n        return i0.ɵɵresetView(ctx.emitClickEvent($event, \"tooltip\"));\n      });\n      i0.ɵɵelementStart(1, \"button\", 2, 0);\n      i0.ɵɵlistener(\"click\", function IconButton_Template_button_click_1_listener($event) {\n        i0.ɵɵrestoreView(_r1);\n        return i0.ɵɵresetView(ctx.emitClickEvent($event));\n      })(\"focus\", function IconButton_Template_button_focus_1_listener($event) {\n        i0.ɵɵrestoreView(_r1);\n        return i0.ɵɵresetView(ctx.focus.emit($event));\n      })(\"blur\", function IconButton_Template_button_blur_1_listener($event) {\n        i0.ɵɵrestoreView(_r1);\n        return i0.ɵɵresetView(ctx.blur.emit($event));\n      });\n      i0.ɵɵprojection(3);\n      i0.ɵɵelementEnd()();\n    }\n    if (rf & 2) {\n      i0.ɵɵproperty(\"description\", ctx.description)(\"disabled\", ctx.showTooltipWhenDisabled ? false : ctx.disabled)(\"caret\", ctx.caret)(\"dropShadow\", ctx.dropShadow)(\"highContrast\", ctx.highContrast)(\"isOpen\", ctx.isOpen)(\"align\", ctx.align)(\"autoAlign\", ctx.autoAlign)(\"enterDelayMs\", ctx.enterDelayMs)(\"leaveDelayMs\", ctx.leaveDelayMs);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"id\", ctx.buttonId)(\"disabled\", ctx.disabled)(\"iconOnly\", true)(\"ngClass\", ctx.buttonNgClass)(\"cdsButton\", ctx.kind)(\"size\", ctx.size)(\"isExpressive\", ctx.isExpressive);\n      i0.ɵɵattribute(\"type\", ctx.type);\n    }\n  },\n  dependencies: [i1.NgClass, i2.Tooltip, Button],\n  encapsulation: 2\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(IconButton, [{\n    type: Component,\n    args: [{\n      selector: \"cds-icon-button, ibm-icon-button\",\n      template: `\n\t<cds-tooltip\n\t\tclass=\"cds--icon-tooltip\"\n\t\t[description]=\"description\"\n\t\t[disabled]=\"showTooltipWhenDisabled ? false : disabled\"\n\t\t[caret]=\"caret\"\n\t\t[dropShadow]=\"dropShadow\"\n\t\t[highContrast]=\"highContrast\"\n\t\t[isOpen]=\"isOpen\"\n\t\t[align]=\"align\"\n\t\t[autoAlign]=\"autoAlign\"\n\t\t[enterDelayMs]=\"enterDelayMs\"\n\t\t[leaveDelayMs]=\"leaveDelayMs\"\n\t\t(click)=\"emitClickEvent($event, 'tooltip')\">\n\t\t<button\n\t\t\t#button\n\t\t\t[id]=\"buttonId\"\n\t\t\t[disabled]=\"disabled\"\n\t\t\t[attr.type]=\"type\"\n\t\t\t[iconOnly]=\"true\"\n\t\t\t[ngClass]=\"buttonNgClass\"\n\t\t\t[cdsButton]=\"kind\"\n\t\t\t[size]=\"size\"\n\t\t\t[isExpressive]=\"isExpressive\"\n\t\t\t(click)=\"emitClickEvent($event)\"\n\t\t\t(focus)=\"focus.emit($event)\"\n\t\t\t(blur)=\"blur.emit($event)\">\n\t\t\t<ng-content></ng-content>\n\t\t</button>\n\t</cds-tooltip>\n\t`\n    }]\n  }], function () {\n    return [{\n      type: i0.Renderer2\n    }];\n  }, {\n    buttonNgClass: [{\n      type: Input\n    }],\n    buttonAttributes: [{\n      type: Input\n    }],\n    button: [{\n      type: ViewChild,\n      args: [\"button\"]\n    }],\n    buttonId: [{\n      type: Input\n    }],\n    kind: [{\n      type: Input\n    }],\n    size: [{\n      type: Input\n    }],\n    type: [{\n      type: Input\n    }],\n    isExpressive: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }],\n    description: [{\n      type: Input\n    }],\n    showTooltipWhenDisabled: [{\n      type: Input\n    }],\n    click: [{\n      type: Output\n    }],\n    focus: [{\n      type: Output\n    }],\n    blur: [{\n      type: Output\n    }],\n    tooltipClick: [{\n      type: Output\n    }]\n  });\n})();\nclass ButtonModule {}\nButtonModule.ɵfac = function ButtonModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || ButtonModule)();\n};\nButtonModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: ButtonModule,\n  declarations: [Button, ButtonSet, BaseIconButton, IconButton],\n  imports: [CommonModule, TooltipModule],\n  exports: [Button, ButtonSet, IconButton]\n});\nButtonModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [CommonModule, TooltipModule]\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ButtonModule, [{\n    type: NgModule,\n    args: [{\n      declarations: [Button, ButtonSet, BaseIconButton, IconButton],\n      exports: [Button, ButtonSet, IconButton],\n      imports: [CommonModule, TooltipModule]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BaseIconButton, Button, ButtonModule, ButtonSet, IconButton };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKA,IAAM,QAAQ,CAAC,OAAO,SAAS,UAAU,MAAM;AAC/C,IAAM,aAAa,CAAC,SAAS,KAAK;AAClC,IAAM,aAA0B,MAAM,OAAO,CAAC,KAAK,SAAS,IAAI,OAAO,MAAM,OAAO,MAAM,WAAW,CAAC,GAAG,OAAO,MAAM,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC;AACxI,IAAM,MAAM,KAAK;AACjB,IAAM,MAAM,KAAK;AACjB,IAAM,QAAQ,KAAK;AACnB,IAAM,QAAQ,KAAK;AACnB,IAAM,eAAe,QAAM;AAAA,EACzB,GAAG;AAAA,EACH,GAAG;AACL;AACA,IAAM,kBAAkB;AAAA,EACtB,MAAM;AAAA,EACN,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,KAAK;AACP;AACA,IAAM,uBAAuB;AAAA,EAC3B,OAAO;AAAA,EACP,KAAK;AACP;AACA,SAAS,MAAM,OAAO,OAAO,KAAK;AAChC,SAAO,IAAI,OAAO,IAAI,OAAO,GAAG,CAAC;AACnC;AACA,SAAS,SAAS,OAAO,OAAO;AAC9B,SAAO,OAAO,UAAU,aAAa,MAAM,KAAK,IAAI;AACtD;AACA,SAAS,QAAQ,WAAW;AAC1B,SAAO,UAAU,MAAM,GAAG,EAAE,CAAC;AAC/B;AACA,SAAS,aAAa,WAAW;AAC/B,SAAO,UAAU,MAAM,GAAG,EAAE,CAAC;AAC/B;AACA,SAAS,gBAAgB,MAAM;AAC7B,SAAO,SAAS,MAAM,MAAM;AAC9B;AACA,SAAS,cAAc,MAAM;AAC3B,SAAO,SAAS,MAAM,WAAW;AACnC;AACA,IAAM,aAA0B,oBAAI,IAAI,CAAC,OAAO,QAAQ,CAAC;AACzD,SAAS,YAAY,WAAW;AAC9B,SAAO,WAAW,IAAI,QAAQ,SAAS,CAAC,IAAI,MAAM;AACpD;AACA,SAAS,iBAAiB,WAAW;AACnC,SAAO,gBAAgB,YAAY,SAAS,CAAC;AAC/C;AACA,SAAS,kBAAkB,WAAW,OAAO,KAAK;AAChD,MAAI,QAAQ,QAAQ;AAClB,UAAM;AAAA,EACR;AACA,QAAM,YAAY,aAAa,SAAS;AACxC,QAAM,gBAAgB,iBAAiB,SAAS;AAChD,QAAM,SAAS,cAAc,aAAa;AAC1C,MAAI,oBAAoB,kBAAkB,MAAM,eAAe,MAAM,QAAQ,WAAW,UAAU,SAAS,cAAc,UAAU,WAAW;AAC9I,MAAI,MAAM,UAAU,MAAM,IAAI,MAAM,SAAS,MAAM,GAAG;AACpD,wBAAoB,qBAAqB,iBAAiB;AAAA,EAC5D;AACA,SAAO,CAAC,mBAAmB,qBAAqB,iBAAiB,CAAC;AACpE;AACA,SAAS,sBAAsB,WAAW;AACxC,QAAM,oBAAoB,qBAAqB,SAAS;AACxD,SAAO,CAAC,8BAA8B,SAAS,GAAG,mBAAmB,8BAA8B,iBAAiB,CAAC;AACvH;AACA,SAAS,8BAA8B,WAAW;AAChD,SAAO,UAAU,QAAQ,cAAc,eAAa,qBAAqB,SAAS,CAAC;AACrF;AACA,IAAM,cAAc,CAAC,QAAQ,OAAO;AACpC,IAAM,cAAc,CAAC,SAAS,MAAM;AACpC,IAAM,cAAc,CAAC,OAAO,QAAQ;AACpC,IAAM,cAAc,CAAC,UAAU,KAAK;AACpC,SAAS,YAAY,MAAM,SAAS,KAAK;AACvC,UAAQ,MAAM;AAAA,IACZ,KAAK;AAAA,IACL,KAAK;AACH,UAAI,IAAK,QAAO,UAAU,cAAc;AACxC,aAAO,UAAU,cAAc;AAAA,IACjC,KAAK;AAAA,IACL,KAAK;AACH,aAAO,UAAU,cAAc;AAAA,IACjC;AACE,aAAO,CAAC;AAAA,EACZ;AACF;AACA,SAAS,0BAA0B,WAAW,eAAe,WAAW,KAAK;AAC3E,QAAM,YAAY,aAAa,SAAS;AACxC,MAAI,OAAO,YAAY,QAAQ,SAAS,GAAG,cAAc,SAAS,GAAG;AACrE,MAAI,WAAW;AACb,WAAO,KAAK,IAAI,UAAQ,OAAO,MAAM,SAAS;AAC9C,QAAI,eAAe;AACjB,aAAO,KAAK,OAAO,KAAK,IAAI,6BAA6B,CAAC;AAAA,IAC5D;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,qBAAqB,WAAW;AACvC,SAAO,UAAU,QAAQ,0BAA0B,UAAQ,gBAAgB,IAAI,CAAC;AAClF;AACA,SAAS,oBAAoB,SAAS;AACpC,SAAO;AAAA,IACL,KAAK;AAAA,IACL,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,MAAM;AAAA,KACH;AAEP;AACA,SAAS,iBAAiB,SAAS;AACjC,SAAO,OAAO,YAAY,WAAW,oBAAoB,OAAO,IAAI;AAAA,IAClE,KAAK;AAAA,IACL,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,MAAM;AAAA,EACR;AACF;AACA,SAAS,iBAAiB,MAAM;AAC9B,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,KAAK;AAAA,IACL,MAAM;AAAA,IACN,OAAO,IAAI;AAAA,IACX,QAAQ,IAAI;AAAA,IACZ;AAAA,IACA;AAAA,EACF;AACF;;;ACrIA,SAAS,2BAA2B,MAAM,WAAW,KAAK;AACxD,MAAI;AAAA,IACF;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,WAAW,YAAY,SAAS;AACtC,QAAM,gBAAgB,iBAAiB,SAAS;AAChD,QAAM,cAAc,cAAc,aAAa;AAC/C,QAAM,OAAO,QAAQ,SAAS;AAC9B,QAAM,aAAa,aAAa;AAChC,QAAM,UAAU,UAAU,IAAI,UAAU,QAAQ,IAAI,SAAS,QAAQ;AACrE,QAAM,UAAU,UAAU,IAAI,UAAU,SAAS,IAAI,SAAS,SAAS;AACvE,QAAM,cAAc,UAAU,WAAW,IAAI,IAAI,SAAS,WAAW,IAAI;AACzE,MAAI;AACJ,UAAQ,MAAM;AAAA,IACZ,KAAK;AACH,eAAS;AAAA,QACP,GAAG;AAAA,QACH,GAAG,UAAU,IAAI,SAAS;AAAA,MAC5B;AACA;AAAA,IACF,KAAK;AACH,eAAS;AAAA,QACP,GAAG;AAAA,QACH,GAAG,UAAU,IAAI,UAAU;AAAA,MAC7B;AACA;AAAA,IACF,KAAK;AACH,eAAS;AAAA,QACP,GAAG,UAAU,IAAI,UAAU;AAAA,QAC3B,GAAG;AAAA,MACL;AACA;AAAA,IACF,KAAK;AACH,eAAS;AAAA,QACP,GAAG,UAAU,IAAI,SAAS;AAAA,QAC1B,GAAG;AAAA,MACL;AACA;AAAA,IACF;AACE,eAAS;AAAA,QACP,GAAG,UAAU;AAAA,QACb,GAAG,UAAU;AAAA,MACf;AAAA,EACJ;AACA,UAAQ,aAAa,SAAS,GAAG;AAAA,IAC/B,KAAK;AACH,aAAO,aAAa,KAAK,eAAe,OAAO,aAAa,KAAK;AACjE;AAAA,IACF,KAAK;AACH,aAAO,aAAa,KAAK,eAAe,OAAO,aAAa,KAAK;AACjE;AAAA,EACJ;AACA,SAAO;AACT;AASA,IAAM,kBAAkB,OAAO,WAAW,UAAU,WAAW;AAC7D,QAAM;AAAA,IACJ,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,aAAa,CAAC;AAAA,IACd,UAAAA;AAAA,EACF,IAAI;AACJ,QAAM,kBAAkB,WAAW,OAAO,OAAO;AACjD,QAAM,MAAM,OAAOA,UAAS,SAAS,OAAO,SAASA,UAAS,MAAM,QAAQ;AAC5E,MAAI,QAAQ,MAAMA,UAAS,gBAAgB;AAAA,IACzC;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,MAAI;AAAA,IACF;AAAA,IACA;AAAA,EACF,IAAI,2BAA2B,OAAO,WAAW,GAAG;AACpD,MAAI,oBAAoB;AACxB,MAAI,iBAAiB,CAAC;AACtB,MAAI,aAAa;AACjB,WAAS,IAAI,GAAG,IAAI,gBAAgB,QAAQ,KAAK;AAC/C,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,gBAAgB,CAAC;AACrB,UAAM;AAAA,MACJ,GAAG;AAAA,MACH,GAAG;AAAA,MACH;AAAA,MACA;AAAA,IACF,IAAI,MAAM,GAAG;AAAA,MACX;AAAA,MACA;AAAA,MACA,kBAAkB;AAAA,MAClB,WAAW;AAAA,MACX;AAAA,MACA;AAAA,MACA;AAAA,MACA,UAAAA;AAAA,MACA,UAAU;AAAA,QACR;AAAA,QACA;AAAA,MACF;AAAA,IACF,CAAC;AACD,QAAI,SAAS,OAAO,QAAQ;AAC5B,QAAI,SAAS,OAAO,QAAQ;AAC5B,qBAAiB,iCACZ,iBADY;AAAA,MAEf,CAAC,IAAI,GAAG,kCACH,eAAe,IAAI,IACnB;AAAA,IAEP;AACA,QAAI,SAAS,cAAc,IAAI;AAC7B;AACA,UAAI,OAAO,UAAU,UAAU;AAC7B,YAAI,MAAM,WAAW;AACnB,8BAAoB,MAAM;AAAA,QAC5B;AACA,YAAI,MAAM,OAAO;AACf,kBAAQ,MAAM,UAAU,OAAO,MAAMA,UAAS,gBAAgB;AAAA,YAC5D;AAAA,YACA;AAAA,YACA;AAAA,UACF,CAAC,IAAI,MAAM;AAAA,QACb;AACA,SAAC;AAAA,UACC;AAAA,UACA;AAAA,QACF,IAAI,2BAA2B,OAAO,mBAAmB,GAAG;AAAA,MAC9D;AACA,UAAI;AAAA,IACN;AAAA,EACF;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,WAAW;AAAA,IACX;AAAA,IACA;AAAA,EACF;AACF;AAUA,eAAe,eAAe,OAAO,SAAS;AAC5C,MAAI;AACJ,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,UAAAA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM;AAAA,IACJ,WAAW;AAAA,IACX,eAAe;AAAA,IACf,iBAAiB;AAAA,IACjB,cAAc;AAAA,IACd,UAAU;AAAA,EACZ,IAAI,SAAS,SAAS,KAAK;AAC3B,QAAM,gBAAgB,iBAAiB,OAAO;AAC9C,QAAM,aAAa,mBAAmB,aAAa,cAAc;AACjE,QAAM,UAAU,SAAS,cAAc,aAAa,cAAc;AAClE,QAAM,qBAAqB,iBAAiB,MAAMA,UAAS,gBAAgB;AAAA,IACzE,WAAW,wBAAwB,OAAOA,UAAS,aAAa,OAAO,SAASA,UAAS,UAAU,OAAO,OAAO,OAAO,wBAAwB,QAAQ,UAAU,QAAQ,kBAAmB,OAAOA,UAAS,sBAAsB,OAAO,SAASA,UAAS,mBAAmB,SAAS,QAAQ;AAAA,IAChS;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC,CAAC;AACF,QAAM,OAAO,mBAAmB,aAAa;AAAA,IAC3C;AAAA,IACA;AAAA,IACA,OAAO,MAAM,SAAS;AAAA,IACtB,QAAQ,MAAM,SAAS;AAAA,EACzB,IAAI,MAAM;AACV,QAAM,eAAe,OAAOA,UAAS,mBAAmB,OAAO,SAASA,UAAS,gBAAgB,SAAS,QAAQ;AAClH,QAAM,cAAe,OAAOA,UAAS,aAAa,OAAO,SAASA,UAAS,UAAU,YAAY,KAAO,OAAOA,UAAS,YAAY,OAAO,SAASA,UAAS,SAAS,YAAY,MAAO;AAAA,IACvL,GAAG;AAAA,IACH,GAAG;AAAA,EACL,IAAI;AAAA,IACF,GAAG;AAAA,IACH,GAAG;AAAA,EACL;AACA,QAAM,oBAAoB,iBAAiBA,UAAS,wDAAwD,MAAMA,UAAS,sDAAsD;AAAA,IAC/K;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC,IAAI,IAAI;AACT,SAAO;AAAA,IACL,MAAM,mBAAmB,MAAM,kBAAkB,MAAM,cAAc,OAAO,YAAY;AAAA,IACxF,SAAS,kBAAkB,SAAS,mBAAmB,SAAS,cAAc,UAAU,YAAY;AAAA,IACpG,OAAO,mBAAmB,OAAO,kBAAkB,OAAO,cAAc,QAAQ,YAAY;AAAA,IAC5F,QAAQ,kBAAkB,QAAQ,mBAAmB,QAAQ,cAAc,SAAS,YAAY;AAAA,EAClG;AACF;AAOA,IAAM,QAAQ,cAAY;AAAA,EACxB,MAAM;AAAA,EACN;AAAA,EACA,MAAM,GAAG,OAAO;AACd,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,UAAAA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AAEJ,UAAM;AAAA,MACJ;AAAA,MACA,UAAU;AAAA,IACZ,IAAI,SAAS,SAAS,KAAK,KAAK,CAAC;AACjC,QAAI,WAAW,MAAM;AACnB,aAAO,CAAC;AAAA,IACV;AACA,UAAM,gBAAgB,iBAAiB,OAAO;AAC9C,UAAM,SAAS;AAAA,MACb;AAAA,MACA;AAAA,IACF;AACA,UAAM,OAAO,iBAAiB,SAAS;AACvC,UAAM,SAAS,cAAc,IAAI;AACjC,UAAM,kBAAkB,MAAMA,UAAS,cAAc,OAAO;AAC5D,UAAM,UAAU,SAAS;AACzB,UAAM,UAAU,UAAU,QAAQ;AAClC,UAAM,UAAU,UAAU,WAAW;AACrC,UAAM,aAAa,UAAU,iBAAiB;AAC9C,UAAM,UAAU,MAAM,UAAU,MAAM,IAAI,MAAM,UAAU,IAAI,IAAI,OAAO,IAAI,IAAI,MAAM,SAAS,MAAM;AACtG,UAAM,YAAY,OAAO,IAAI,IAAI,MAAM,UAAU,IAAI;AACrD,UAAM,oBAAoB,OAAOA,UAAS,mBAAmB,OAAO,SAASA,UAAS,gBAAgB,OAAO;AAC7G,QAAI,aAAa,oBAAoB,kBAAkB,UAAU,IAAI;AAGrE,QAAI,CAAC,cAAc,CAAE,OAAOA,UAAS,aAAa,OAAO,SAASA,UAAS,UAAU,iBAAiB,IAAK;AACzG,mBAAa,SAAS,SAAS,UAAU,KAAK,MAAM,SAAS,MAAM;AAAA,IACrE;AACA,UAAM,oBAAoB,UAAU,IAAI,YAAY;AAIpD,UAAM,yBAAyB,aAAa,IAAI,gBAAgB,MAAM,IAAI,IAAI;AAC9E,UAAM,aAAa,IAAI,cAAc,OAAO,GAAG,sBAAsB;AACrE,UAAM,aAAa,IAAI,cAAc,OAAO,GAAG,sBAAsB;AAIrE,UAAM,QAAQ;AACd,UAAMC,OAAM,aAAa,gBAAgB,MAAM,IAAI;AACnD,UAAM,SAAS,aAAa,IAAI,gBAAgB,MAAM,IAAI,IAAI;AAC9D,UAAMC,UAAS,MAAM,OAAO,QAAQD,IAAG;AAMvC,UAAM,kBAAkB,CAAC,eAAe,SAAS,aAAa,SAAS,KAAK,QAAQ,WAAWC,WAAU,MAAM,UAAU,MAAM,IAAI,KAAK,SAAS,QAAQ,aAAa,cAAc,gBAAgB,MAAM,IAAI,IAAI;AAClN,UAAM,kBAAkB,kBAAkB,SAAS,QAAQ,SAAS,QAAQ,SAASD,OAAM;AAC3F,WAAO;AAAA,MACL,CAAC,IAAI,GAAG,OAAO,IAAI,IAAI;AAAA,MACvB,MAAM;AAAA,QACJ,CAAC,IAAI,GAAGC;AAAA,QACR,cAAc,SAASA,UAAS;AAAA,SAC5B,mBAAmB;AAAA,QACrB;AAAA,MACF;AAAA,MAEF,OAAO;AAAA,IACT;AAAA,EACF;AACF;AA+GA,IAAM,OAAO,SAAU,SAAS;AAC9B,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,SAAO;AAAA,IACL,MAAM;AAAA,IACN;AAAA,IACA,MAAM,GAAG,OAAO;AACd,UAAI,uBAAuB;AAC3B,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,UAAAC;AAAA,QACA;AAAA,MACF,IAAI;AACJ,YAQI,cAAS,SAAS,KAAK,GAPzB;AAAA,kBAAU,gBAAgB;AAAA,QAC1B,WAAW,iBAAiB;AAAA,QAC5B,oBAAoB;AAAA,QACpB,mBAAmB;AAAA,QACnB,4BAA4B;AAAA,QAC5B,gBAAgB;AAAA,MA7axB,IA+aU,IADC,kCACD,IADC;AAAA,QANH;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA;AAQF,WAAK,wBAAwB,eAAe,UAAU,QAAQ,sBAAsB,iBAAiB;AACnG,eAAO,CAAC;AAAA,MACV;AACA,YAAM,OAAO,QAAQ,SAAS;AAC9B,YAAM,kBAAkB,YAAY,gBAAgB;AACpD,YAAM,kBAAkB,QAAQ,gBAAgB,MAAM;AACtD,YAAM,MAAM,OAAOA,UAAS,SAAS,OAAO,SAASA,UAAS,MAAM,SAAS,QAAQ;AACrF,YAAM,qBAAqB,gCAAgC,mBAAmB,CAAC,gBAAgB,CAAC,qBAAqB,gBAAgB,CAAC,IAAI,sBAAsB,gBAAgB;AAChL,YAAM,+BAA+B,8BAA8B;AACnE,UAAI,CAAC,+BAA+B,8BAA8B;AAChE,2BAAmB,KAAK,GAAG,0BAA0B,kBAAkB,eAAe,2BAA2B,GAAG,CAAC;AAAA,MACvH;AACA,YAAMC,cAAa,CAAC,kBAAkB,GAAG,kBAAkB;AAC3D,YAAM,WAAW,MAAM,eAAe,OAAO,qBAAqB;AAClE,YAAM,YAAY,CAAC;AACnB,UAAI,kBAAkB,uBAAuB,eAAe,SAAS,OAAO,SAAS,qBAAqB,cAAc,CAAC;AACzH,UAAI,eAAe;AACjB,kBAAU,KAAK,SAAS,IAAI,CAAC;AAAA,MAC/B;AACA,UAAI,gBAAgB;AAClB,cAAMC,SAAQ,kBAAkB,WAAW,OAAO,GAAG;AACrD,kBAAU,KAAK,SAASA,OAAM,CAAC,CAAC,GAAG,SAASA,OAAM,CAAC,CAAC,CAAC;AAAA,MACvD;AACA,sBAAgB,CAAC,GAAG,eAAe;AAAA,QACjC;AAAA,QACA;AAAA,MACF,CAAC;AAGD,UAAI,CAAC,UAAU,MAAM,CAAAC,UAAQA,SAAQ,CAAC,GAAG;AACvC,YAAI,uBAAuB;AAC3B,cAAM,eAAe,wBAAwB,eAAe,SAAS,OAAO,SAAS,sBAAsB,UAAU,KAAK;AAC1H,cAAM,gBAAgBF,YAAW,SAAS;AAC1C,YAAI,eAAe;AACjB,gBAAM,0BAA0B,mBAAmB,cAAc,oBAAoB,YAAY,aAAa,IAAI;AAClH,cAAI,CAAC;AAAA;AAAA,UAGL,cAAc,MAAM,OAAK,YAAY,EAAE,SAAS,MAAM,kBAAkB,EAAE,UAAU,CAAC,IAAI,IAAI,IAAI,GAAG;AAElG,mBAAO;AAAA,cACL,MAAM;AAAA,gBACJ,OAAO;AAAA,gBACP,WAAW;AAAA,cACb;AAAA,cACA,OAAO;AAAA,gBACL,WAAW;AAAA,cACb;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAIA,YAAI,kBAAkB,wBAAwB,cAAc,OAAO,OAAK,EAAE,UAAU,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,GAAG,MAAM,EAAE,UAAU,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC,EAAE,CAAC,MAAM,OAAO,SAAS,sBAAsB;AAG1L,YAAI,CAAC,gBAAgB;AACnB,kBAAQ,kBAAkB;AAAA,YACxB,KAAK,WACH;AACE,kBAAI;AACJ,oBAAMG,cAAa,yBAAyB,cAAc,OAAO,OAAK;AACpE,oBAAI,8BAA8B;AAChC,wBAAM,kBAAkB,YAAY,EAAE,SAAS;AAC/C,yBAAO,oBAAoB;AAAA;AAAA,kBAG3B,oBAAoB;AAAA,gBACtB;AACA,uBAAO;AAAA,cACT,CAAC,EAAE,IAAI,OAAK,CAAC,EAAE,WAAW,EAAE,UAAU,OAAO,CAAAC,cAAYA,YAAW,CAAC,EAAE,OAAO,CAAC,KAAKA,cAAa,MAAMA,WAAU,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,GAAG,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,MAAM,OAAO,SAAS,uBAAuB,CAAC;AACjM,kBAAID,YAAW;AACb,iCAAiBA;AAAA,cACnB;AACA;AAAA,YACF;AAAA,YACF,KAAK;AACH,+BAAiB;AACjB;AAAA,UACJ;AAAA,QACF;AACA,YAAI,cAAc,gBAAgB;AAChC,iBAAO;AAAA,YACL,OAAO;AAAA,cACL,WAAW;AAAA,YACb;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,aAAO,CAAC;AAAA,IACV;AAAA,EACF;AACF;AA0MA,IAAM,cAA2B,oBAAI,IAAI,CAAC,QAAQ,KAAK,CAAC;AAKxD,eAAe,qBAAqB,OAAO,SAAS;AAClD,QAAM;AAAA,IACJ;AAAA,IACA,UAAAE;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,MAAM,OAAOA,UAAS,SAAS,OAAO,SAASA,UAAS,MAAM,SAAS,QAAQ;AACrF,QAAM,OAAO,QAAQ,SAAS;AAC9B,QAAM,YAAY,aAAa,SAAS;AACxC,QAAM,aAAa,YAAY,SAAS,MAAM;AAC9C,QAAM,gBAAgB,YAAY,IAAI,IAAI,IAAI,KAAK;AACnD,QAAM,iBAAiB,OAAO,aAAa,KAAK;AAChD,QAAM,WAAW,SAAS,SAAS,KAAK;AAGxC,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,OAAO,aAAa,WAAW;AAAA,IACjC,UAAU;AAAA,IACV,WAAW;AAAA,IACX,eAAe;AAAA,EACjB,IAAI;AAAA,IACF,UAAU,SAAS,YAAY;AAAA,IAC/B,WAAW,SAAS,aAAa;AAAA,IACjC,eAAe,SAAS;AAAA,EAC1B;AACA,MAAI,aAAa,OAAO,kBAAkB,UAAU;AAClD,gBAAY,cAAc,QAAQ,gBAAgB,KAAK;AAAA,EACzD;AACA,SAAO,aAAa;AAAA,IAClB,GAAG,YAAY;AAAA,IACf,GAAG,WAAW;AAAA,EAChB,IAAI;AAAA,IACF,GAAG,WAAW;AAAA,IACd,GAAG,YAAY;AAAA,EACjB;AACF;AASA,IAAM,SAAS,SAAU,SAAS;AAChC,MAAI,YAAY,QAAQ;AACtB,cAAU;AAAA,EACZ;AACA,SAAO;AAAA,IACL,MAAM;AAAA,IACN;AAAA,IACA,MAAM,GAAG,OAAO;AACd,UAAI,uBAAuB;AAC3B,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,YAAM,aAAa,MAAM,qBAAqB,OAAO,OAAO;AAI5D,UAAI,gBAAgB,wBAAwB,eAAe,WAAW,OAAO,SAAS,sBAAsB,eAAe,wBAAwB,eAAe,UAAU,QAAQ,sBAAsB,iBAAiB;AACzN,eAAO,CAAC;AAAA,MACV;AACA,aAAO;AAAA,QACL,GAAG,IAAI,WAAW;AAAA,QAClB,GAAG,IAAI,WAAW;AAAA,QAClB,MAAM,iCACD,aADC;AAAA,UAEJ;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;;;AChzBA,SAAS,YAAY;AACnB,SAAO,OAAO,WAAW;AAC3B;AACA,SAAS,YAAY,MAAM;AACzB,MAAI,OAAO,IAAI,GAAG;AAChB,YAAQ,KAAK,YAAY,IAAI,YAAY;AAAA,EAC3C;AAIA,SAAO;AACT;AACA,SAAS,UAAU,MAAM;AACvB,MAAI;AACJ,UAAQ,QAAQ,SAAS,sBAAsB,KAAK,kBAAkB,OAAO,SAAS,oBAAoB,gBAAgB;AAC5H;AACA,SAAS,mBAAmB,MAAM;AAChC,MAAI;AACJ,UAAQ,QAAQ,OAAO,IAAI,IAAI,KAAK,gBAAgB,KAAK,aAAa,OAAO,aAAa,OAAO,SAAS,KAAK;AACjH;AACA,SAAS,OAAO,OAAO;AACrB,MAAI,CAAC,UAAU,GAAG;AAChB,WAAO;AAAA,EACT;AACA,SAAO,iBAAiB,QAAQ,iBAAiB,UAAU,KAAK,EAAE;AACpE;AACA,SAAS,UAAU,OAAO;AACxB,MAAI,CAAC,UAAU,GAAG;AAChB,WAAO;AAAA,EACT;AACA,SAAO,iBAAiB,WAAW,iBAAiB,UAAU,KAAK,EAAE;AACvE;AACA,SAAS,cAAc,OAAO;AAC5B,MAAI,CAAC,UAAU,GAAG;AAChB,WAAO;AAAA,EACT;AACA,SAAO,iBAAiB,eAAe,iBAAiB,UAAU,KAAK,EAAE;AAC3E;AACA,SAAS,aAAa,OAAO;AAC3B,MAAI,CAAC,UAAU,KAAK,OAAO,eAAe,aAAa;AACrD,WAAO;AAAA,EACT;AACA,SAAO,iBAAiB,cAAc,iBAAiB,UAAU,KAAK,EAAE;AAC1E;AACA,IAAM,+BAA4C,oBAAI,IAAI,CAAC,UAAU,UAAU,CAAC;AAChF,SAAS,kBAAkB,SAAS;AAClC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAIC,kBAAiB,OAAO;AAC5B,SAAO,kCAAkC,KAAK,WAAW,YAAY,SAAS,KAAK,CAAC,6BAA6B,IAAI,OAAO;AAC9H;AACA,IAAM,gBAA6B,oBAAI,IAAI,CAAC,SAAS,MAAM,IAAI,CAAC;AAChE,SAAS,eAAe,SAAS;AAC/B,SAAO,cAAc,IAAI,YAAY,OAAO,CAAC;AAC/C;AACA,IAAM,oBAAoB,CAAC,iBAAiB,QAAQ;AACpD,SAAS,WAAW,SAAS;AAC3B,SAAO,kBAAkB,KAAK,cAAY;AACxC,QAAI;AACF,aAAO,QAAQ,QAAQ,QAAQ;AAAA,IACjC,SAAS,IAAI;AACX,aAAO;AAAA,IACT;AAAA,EACF,CAAC;AACH;AACA,IAAM,sBAAsB,CAAC,aAAa,aAAa,SAAS,UAAU,aAAa;AACvF,IAAM,mBAAmB,CAAC,aAAa,aAAa,SAAS,UAAU,eAAe,QAAQ;AAC9F,IAAM,gBAAgB,CAAC,SAAS,UAAU,UAAU,SAAS;AAC7D,SAAS,kBAAkB,cAAc;AACvC,QAAM,SAAS,SAAS;AACxB,QAAM,MAAM,UAAU,YAAY,IAAIA,kBAAiB,YAAY,IAAI;AAIvE,SAAO,oBAAoB,KAAK,WAAS,IAAI,KAAK,IAAI,IAAI,KAAK,MAAM,SAAS,KAAK,MAAM,IAAI,gBAAgB,IAAI,kBAAkB,WAAW,UAAU,CAAC,WAAW,IAAI,iBAAiB,IAAI,mBAAmB,SAAS,UAAU,CAAC,WAAW,IAAI,SAAS,IAAI,WAAW,SAAS,UAAU,iBAAiB,KAAK,YAAU,IAAI,cAAc,IAAI,SAAS,KAAK,CAAC,KAAK,cAAc,KAAK,YAAU,IAAI,WAAW,IAAI,SAAS,KAAK,CAAC;AACza;AACA,SAAS,mBAAmB,SAAS;AACnC,MAAI,cAAc,cAAc,OAAO;AACvC,SAAO,cAAc,WAAW,KAAK,CAAC,sBAAsB,WAAW,GAAG;AACxE,QAAI,kBAAkB,WAAW,GAAG;AAClC,aAAO;AAAA,IACT,WAAW,WAAW,WAAW,GAAG;AAClC,aAAO;AAAA,IACT;AACA,kBAAc,cAAc,WAAW;AAAA,EACzC;AACA,SAAO;AACT;AACA,SAAS,WAAW;AAClB,MAAI,OAAO,QAAQ,eAAe,CAAC,IAAI,SAAU,QAAO;AACxD,SAAO,IAAI,SAAS,2BAA2B,MAAM;AACvD;AACA,IAAM,2BAAwC,oBAAI,IAAI,CAAC,QAAQ,QAAQ,WAAW,CAAC;AACnF,SAAS,sBAAsB,MAAM;AACnC,SAAO,yBAAyB,IAAI,YAAY,IAAI,CAAC;AACvD;AACA,SAASA,kBAAiB,SAAS;AACjC,SAAO,UAAU,OAAO,EAAE,iBAAiB,OAAO;AACpD;AACA,SAAS,cAAc,SAAS;AAC9B,MAAI,UAAU,OAAO,GAAG;AACtB,WAAO;AAAA,MACL,YAAY,QAAQ;AAAA,MACpB,WAAW,QAAQ;AAAA,IACrB;AAAA,EACF;AACA,SAAO;AAAA,IACL,YAAY,QAAQ;AAAA,IACpB,WAAW,QAAQ;AAAA,EACrB;AACF;AACA,SAAS,cAAc,MAAM;AAC3B,MAAI,YAAY,IAAI,MAAM,QAAQ;AAChC,WAAO;AAAA,EACT;AACA,QAAM;AAAA;AAAA,IAEN,KAAK;AAAA,IAEL,KAAK;AAAA,IAEL,aAAa,IAAI,KAAK,KAAK;AAAA,IAE3B,mBAAmB,IAAI;AAAA;AACvB,SAAO,aAAa,MAAM,IAAI,OAAO,OAAO;AAC9C;AACA,SAAS,2BAA2B,MAAM;AACxC,QAAM,aAAa,cAAc,IAAI;AACrC,MAAI,sBAAsB,UAAU,GAAG;AACrC,WAAO,KAAK,gBAAgB,KAAK,cAAc,OAAO,KAAK;AAAA,EAC7D;AACA,MAAI,cAAc,UAAU,KAAK,kBAAkB,UAAU,GAAG;AAC9D,WAAO;AAAA,EACT;AACA,SAAO,2BAA2B,UAAU;AAC9C;AACA,SAAS,qBAAqB,MAAM,MAAM,iBAAiB;AACzD,MAAI;AACJ,MAAI,SAAS,QAAQ;AACnB,WAAO,CAAC;AAAA,EACV;AACA,MAAI,oBAAoB,QAAQ;AAC9B,sBAAkB;AAAA,EACpB;AACA,QAAM,qBAAqB,2BAA2B,IAAI;AAC1D,QAAM,SAAS,yBAAyB,uBAAuB,KAAK,kBAAkB,OAAO,SAAS,qBAAqB;AAC3H,QAAM,MAAM,UAAU,kBAAkB;AACxC,MAAI,QAAQ;AACV,UAAM,eAAe,gBAAgB,GAAG;AACxC,WAAO,KAAK,OAAO,KAAK,IAAI,kBAAkB,CAAC,GAAG,kBAAkB,kBAAkB,IAAI,qBAAqB,CAAC,GAAG,gBAAgB,kBAAkB,qBAAqB,YAAY,IAAI,CAAC,CAAC;AAAA,EAC9L;AACA,SAAO,KAAK,OAAO,oBAAoB,qBAAqB,oBAAoB,CAAC,GAAG,eAAe,CAAC;AACtG;AACA,SAAS,gBAAgB,KAAK;AAC5B,SAAO,IAAI,UAAU,OAAO,eAAe,IAAI,MAAM,IAAI,IAAI,eAAe;AAC9E;;;ACzJA,SAAS,iBAAiB,SAAS;AACjC,QAAM,MAAMC,kBAAiB,OAAO;AAGpC,MAAI,QAAQ,WAAW,IAAI,KAAK,KAAK;AACrC,MAAI,SAAS,WAAW,IAAI,MAAM,KAAK;AACvC,QAAM,YAAY,cAAc,OAAO;AACvC,QAAM,cAAc,YAAY,QAAQ,cAAc;AACtD,QAAM,eAAe,YAAY,QAAQ,eAAe;AACxD,QAAM,iBAAiB,MAAM,KAAK,MAAM,eAAe,MAAM,MAAM,MAAM;AACzE,MAAI,gBAAgB;AAClB,YAAQ;AACR,aAAS;AAAA,EACX;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACL;AACF;AAEA,SAAS,cAAc,SAAS;AAC9B,SAAO,CAAC,UAAU,OAAO,IAAI,QAAQ,iBAAiB;AACxD;AAEA,SAAS,SAAS,SAAS;AACzB,QAAM,aAAa,cAAc,OAAO;AACxC,MAAI,CAAC,cAAc,UAAU,GAAG;AAC9B,WAAO,aAAa,CAAC;AAAA,EACvB;AACA,QAAM,OAAO,WAAW,sBAAsB;AAC9C,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,iBAAiB,UAAU;AAC/B,MAAI,KAAK,IAAI,MAAM,KAAK,KAAK,IAAI,KAAK,SAAS;AAC/C,MAAI,KAAK,IAAI,MAAM,KAAK,MAAM,IAAI,KAAK,UAAU;AAIjD,MAAI,CAAC,KAAK,CAAC,OAAO,SAAS,CAAC,GAAG;AAC7B,QAAI;AAAA,EACN;AACA,MAAI,CAAC,KAAK,CAAC,OAAO,SAAS,CAAC,GAAG;AAC7B,QAAI;AAAA,EACN;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAM,YAAyB,aAAa,CAAC;AAC7C,SAAS,iBAAiB,SAAS;AACjC,QAAM,MAAM,UAAU,OAAO;AAC7B,MAAI,CAAC,SAAS,KAAK,CAAC,IAAI,gBAAgB;AACtC,WAAO;AAAA,EACT;AACA,SAAO;AAAA,IACL,GAAG,IAAI,eAAe;AAAA,IACtB,GAAG,IAAI,eAAe;AAAA,EACxB;AACF;AACA,SAAS,uBAAuB,SAAS,SAAS,sBAAsB;AACtE,MAAI,YAAY,QAAQ;AACtB,cAAU;AAAA,EACZ;AACA,MAAI,CAAC,wBAAwB,WAAW,yBAAyB,UAAU,OAAO,GAAG;AACnF,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAEA,SAAS,sBAAsB,SAAS,cAAc,iBAAiB,cAAc;AACnF,MAAI,iBAAiB,QAAQ;AAC3B,mBAAe;AAAA,EACjB;AACA,MAAI,oBAAoB,QAAQ;AAC9B,sBAAkB;AAAA,EACpB;AACA,QAAM,aAAa,QAAQ,sBAAsB;AACjD,QAAM,aAAa,cAAc,OAAO;AACxC,MAAI,QAAQ,aAAa,CAAC;AAC1B,MAAI,cAAc;AAChB,QAAI,cAAc;AAChB,UAAI,UAAU,YAAY,GAAG;AAC3B,gBAAQ,SAAS,YAAY;AAAA,MAC/B;AAAA,IACF,OAAO;AACL,cAAQ,SAAS,OAAO;AAAA,IAC1B;AAAA,EACF;AACA,QAAM,gBAAgB,uBAAuB,YAAY,iBAAiB,YAAY,IAAI,iBAAiB,UAAU,IAAI,aAAa,CAAC;AACvI,MAAI,KAAK,WAAW,OAAO,cAAc,KAAK,MAAM;AACpD,MAAI,KAAK,WAAW,MAAM,cAAc,KAAK,MAAM;AACnD,MAAI,QAAQ,WAAW,QAAQ,MAAM;AACrC,MAAI,SAAS,WAAW,SAAS,MAAM;AACvC,MAAI,YAAY;AACd,UAAM,MAAM,UAAU,UAAU;AAChC,UAAM,YAAY,gBAAgB,UAAU,YAAY,IAAI,UAAU,YAAY,IAAI;AACtF,QAAI,aAAa;AACjB,QAAI,gBAAgB,gBAAgB,UAAU;AAC9C,WAAO,iBAAiB,gBAAgB,cAAc,YAAY;AAChE,YAAM,cAAc,SAAS,aAAa;AAC1C,YAAM,aAAa,cAAc,sBAAsB;AACvD,YAAM,MAAMA,kBAAiB,aAAa;AAC1C,YAAM,OAAO,WAAW,QAAQ,cAAc,aAAa,WAAW,IAAI,WAAW,KAAK,YAAY;AACtG,YAAM,MAAM,WAAW,OAAO,cAAc,YAAY,WAAW,IAAI,UAAU,KAAK,YAAY;AAClG,WAAK,YAAY;AACjB,WAAK,YAAY;AACjB,eAAS,YAAY;AACrB,gBAAU,YAAY;AACtB,WAAK;AACL,WAAK;AACL,mBAAa,UAAU,aAAa;AACpC,sBAAgB,gBAAgB,UAAU;AAAA,IAC5C;AAAA,EACF;AACA,SAAO,iBAAiB;AAAA,IACtB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACH;AAIA,SAAS,oBAAoB,SAAS,MAAM;AAC1C,QAAM,aAAa,cAAc,OAAO,EAAE;AAC1C,MAAI,CAAC,MAAM;AACT,WAAO,sBAAsB,mBAAmB,OAAO,CAAC,EAAE,OAAO;AAAA,EACnE;AACA,SAAO,KAAK,OAAO;AACrB;AAEA,SAAS,cAAc,iBAAiB,QAAQ,kBAAkB;AAChE,MAAI,qBAAqB,QAAQ;AAC/B,uBAAmB;AAAA,EACrB;AACA,QAAM,WAAW,gBAAgB,sBAAsB;AACvD,QAAM,IAAI,SAAS,OAAO,OAAO,cAAc,mBAAmB;AAAA;AAAA,IAElE,oBAAoB,iBAAiB,QAAQ;AAAA;AAC7C,QAAM,IAAI,SAAS,MAAM,OAAO;AAChC,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,sDAAsD,MAAM;AACnE,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,UAAU,aAAa;AAC7B,QAAM,kBAAkB,mBAAmB,YAAY;AACvD,QAAM,WAAW,WAAW,WAAW,SAAS,QAAQ,IAAI;AAC5D,MAAI,iBAAiB,mBAAmB,YAAY,SAAS;AAC3D,WAAO;AAAA,EACT;AACA,MAAI,SAAS;AAAA,IACX,YAAY;AAAA,IACZ,WAAW;AAAA,EACb;AACA,MAAI,QAAQ,aAAa,CAAC;AAC1B,QAAM,UAAU,aAAa,CAAC;AAC9B,QAAM,0BAA0B,cAAc,YAAY;AAC1D,MAAI,2BAA2B,CAAC,2BAA2B,CAAC,SAAS;AACnE,QAAI,YAAY,YAAY,MAAM,UAAU,kBAAkB,eAAe,GAAG;AAC9E,eAAS,cAAc,YAAY;AAAA,IACrC;AACA,QAAI,cAAc,YAAY,GAAG;AAC/B,YAAM,aAAa,sBAAsB,YAAY;AACrD,cAAQ,SAAS,YAAY;AAC7B,cAAQ,IAAI,WAAW,IAAI,aAAa;AACxC,cAAQ,IAAI,WAAW,IAAI,aAAa;AAAA,IAC1C;AAAA,EACF;AACA,QAAM,aAAa,mBAAmB,CAAC,2BAA2B,CAAC,UAAU,cAAc,iBAAiB,QAAQ,IAAI,IAAI,aAAa,CAAC;AAC1I,SAAO;AAAA,IACL,OAAO,KAAK,QAAQ,MAAM;AAAA,IAC1B,QAAQ,KAAK,SAAS,MAAM;AAAA,IAC5B,GAAG,KAAK,IAAI,MAAM,IAAI,OAAO,aAAa,MAAM,IAAI,QAAQ,IAAI,WAAW;AAAA,IAC3E,GAAG,KAAK,IAAI,MAAM,IAAI,OAAO,YAAY,MAAM,IAAI,QAAQ,IAAI,WAAW;AAAA,EAC5E;AACF;AAEA,SAAS,eAAe,SAAS;AAC/B,SAAO,MAAM,KAAK,QAAQ,eAAe,CAAC;AAC5C;AAIA,SAAS,gBAAgB,SAAS;AAChC,QAAM,OAAO,mBAAmB,OAAO;AACvC,QAAM,SAAS,cAAc,OAAO;AACpC,QAAM,OAAO,QAAQ,cAAc;AACnC,QAAM,QAAQ,IAAI,KAAK,aAAa,KAAK,aAAa,KAAK,aAAa,KAAK,WAAW;AACxF,QAAM,SAAS,IAAI,KAAK,cAAc,KAAK,cAAc,KAAK,cAAc,KAAK,YAAY;AAC7F,MAAI,IAAI,CAAC,OAAO,aAAa,oBAAoB,OAAO;AACxD,QAAM,IAAI,CAAC,OAAO;AAClB,MAAIA,kBAAiB,IAAI,EAAE,cAAc,OAAO;AAC9C,SAAK,IAAI,KAAK,aAAa,KAAK,WAAW,IAAI;AAAA,EACjD;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,gBAAgB,SAAS,UAAU;AAC1C,QAAM,MAAM,UAAU,OAAO;AAC7B,QAAM,OAAO,mBAAmB,OAAO;AACvC,QAAM,iBAAiB,IAAI;AAC3B,MAAI,QAAQ,KAAK;AACjB,MAAI,SAAS,KAAK;AAClB,MAAI,IAAI;AACR,MAAI,IAAI;AACR,MAAI,gBAAgB;AAClB,YAAQ,eAAe;AACvB,aAAS,eAAe;AACxB,UAAM,sBAAsB,SAAS;AACrC,QAAI,CAAC,uBAAuB,uBAAuB,aAAa,SAAS;AACvE,UAAI,eAAe;AACnB,UAAI,eAAe;AAAA,IACrB;AAAA,EACF;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAGA,SAAS,2BAA2B,SAAS,UAAU;AACrD,QAAM,aAAa,sBAAsB,SAAS,MAAM,aAAa,OAAO;AAC5E,QAAM,MAAM,WAAW,MAAM,QAAQ;AACrC,QAAM,OAAO,WAAW,OAAO,QAAQ;AACvC,QAAM,QAAQ,cAAc,OAAO,IAAI,SAAS,OAAO,IAAI,aAAa,CAAC;AACzE,QAAM,QAAQ,QAAQ,cAAc,MAAM;AAC1C,QAAM,SAAS,QAAQ,eAAe,MAAM;AAC5C,QAAM,IAAI,OAAO,MAAM;AACvB,QAAM,IAAI,MAAM,MAAM;AACtB,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AACA,SAAS,kCAAkC,SAAS,kBAAkB,UAAU;AAC9E,MAAI;AACJ,MAAI,qBAAqB,YAAY;AACnC,WAAO,gBAAgB,SAAS,QAAQ;AAAA,EAC1C,WAAW,qBAAqB,YAAY;AAC1C,WAAO,gBAAgB,mBAAmB,OAAO,CAAC;AAAA,EACpD,WAAW,UAAU,gBAAgB,GAAG;AACtC,WAAO,2BAA2B,kBAAkB,QAAQ;AAAA,EAC9D,OAAO;AACL,UAAM,gBAAgB,iBAAiB,OAAO;AAC9C,WAAO;AAAA,MACL,GAAG,iBAAiB,IAAI,cAAc;AAAA,MACtC,GAAG,iBAAiB,IAAI,cAAc;AAAA,MACtC,OAAO,iBAAiB;AAAA,MACxB,QAAQ,iBAAiB;AAAA,IAC3B;AAAA,EACF;AACA,SAAO,iBAAiB,IAAI;AAC9B;AACA,SAAS,yBAAyB,SAAS,UAAU;AACnD,QAAM,aAAa,cAAc,OAAO;AACxC,MAAI,eAAe,YAAY,CAAC,UAAU,UAAU,KAAK,sBAAsB,UAAU,GAAG;AAC1F,WAAO;AAAA,EACT;AACA,SAAOA,kBAAiB,UAAU,EAAE,aAAa,WAAW,yBAAyB,YAAY,QAAQ;AAC3G;AAKA,SAAS,4BAA4B,SAAS,OAAO;AACnD,QAAM,eAAe,MAAM,IAAI,OAAO;AACtC,MAAI,cAAc;AAChB,WAAO;AAAA,EACT;AACA,MAAI,SAAS,qBAAqB,SAAS,CAAC,GAAG,KAAK,EAAE,OAAO,QAAM,UAAU,EAAE,KAAK,YAAY,EAAE,MAAM,MAAM;AAC9G,MAAI,sCAAsC;AAC1C,QAAM,iBAAiBA,kBAAiB,OAAO,EAAE,aAAa;AAC9D,MAAI,cAAc,iBAAiB,cAAc,OAAO,IAAI;AAG5D,SAAO,UAAU,WAAW,KAAK,CAAC,sBAAsB,WAAW,GAAG;AACpE,UAAM,gBAAgBA,kBAAiB,WAAW;AAClD,UAAM,0BAA0B,kBAAkB,WAAW;AAC7D,QAAI,CAAC,2BAA2B,cAAc,aAAa,SAAS;AAClE,4CAAsC;AAAA,IACxC;AACA,UAAM,wBAAwB,iBAAiB,CAAC,2BAA2B,CAAC,sCAAsC,CAAC,2BAA2B,cAAc,aAAa,YAAY,CAAC,CAAC,uCAAuC,CAAC,YAAY,OAAO,EAAE,SAAS,oCAAoC,QAAQ,KAAK,kBAAkB,WAAW,KAAK,CAAC,2BAA2B,yBAAyB,SAAS,WAAW;AACzZ,QAAI,uBAAuB;AAEzB,eAAS,OAAO,OAAO,cAAY,aAAa,WAAW;AAAA,IAC7D,OAAO;AAEL,4CAAsC;AAAA,IACxC;AACA,kBAAc,cAAc,WAAW;AAAA,EACzC;AACA,QAAM,IAAI,SAAS,MAAM;AACzB,SAAO;AACT;AAIA,SAAS,gBAAgB,MAAM;AAC7B,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,2BAA2B,aAAa,sBAAsB,WAAW,OAAO,IAAI,CAAC,IAAI,4BAA4B,SAAS,KAAK,EAAE,IAAI,CAAC,EAAE,OAAO,QAAQ;AACjK,QAAM,oBAAoB,CAAC,GAAG,0BAA0B,YAAY;AACpE,QAAM,wBAAwB,kBAAkB,CAAC;AACjD,QAAM,eAAe,kBAAkB,OAAO,CAAC,SAAS,qBAAqB;AAC3E,UAAM,OAAO,kCAAkC,SAAS,kBAAkB,QAAQ;AAClF,YAAQ,MAAM,IAAI,KAAK,KAAK,QAAQ,GAAG;AACvC,YAAQ,QAAQ,IAAI,KAAK,OAAO,QAAQ,KAAK;AAC7C,YAAQ,SAAS,IAAI,KAAK,QAAQ,QAAQ,MAAM;AAChD,YAAQ,OAAO,IAAI,KAAK,MAAM,QAAQ,IAAI;AAC1C,WAAO;AAAA,EACT,GAAG,kCAAkC,SAAS,uBAAuB,QAAQ,CAAC;AAC9E,SAAO;AAAA,IACL,OAAO,aAAa,QAAQ,aAAa;AAAA,IACzC,QAAQ,aAAa,SAAS,aAAa;AAAA,IAC3C,GAAG,aAAa;AAAA,IAChB,GAAG,aAAa;AAAA,EAClB;AACF;AAEA,SAAS,cAAc,SAAS;AAC9B,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI,iBAAiB,OAAO;AAC5B,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,8BAA8B,SAAS,cAAc,UAAU;AACtE,QAAM,0BAA0B,cAAc,YAAY;AAC1D,QAAM,kBAAkB,mBAAmB,YAAY;AACvD,QAAM,UAAU,aAAa;AAC7B,QAAM,OAAO,sBAAsB,SAAS,MAAM,SAAS,YAAY;AACvE,MAAI,SAAS;AAAA,IACX,YAAY;AAAA,IACZ,WAAW;AAAA,EACb;AACA,QAAM,UAAU,aAAa,CAAC;AAC9B,MAAI,2BAA2B,CAAC,2BAA2B,CAAC,SAAS;AACnE,QAAI,YAAY,YAAY,MAAM,UAAU,kBAAkB,eAAe,GAAG;AAC9E,eAAS,cAAc,YAAY;AAAA,IACrC;AACA,QAAI,yBAAyB;AAC3B,YAAM,aAAa,sBAAsB,cAAc,MAAM,SAAS,YAAY;AAClF,cAAQ,IAAI,WAAW,IAAI,aAAa;AACxC,cAAQ,IAAI,WAAW,IAAI,aAAa;AAAA,IAC1C,WAAW,iBAAiB;AAG1B,cAAQ,IAAI,oBAAoB,eAAe;AAAA,IACjD;AAAA,EACF;AACA,QAAM,aAAa,mBAAmB,CAAC,2BAA2B,CAAC,UAAU,cAAc,iBAAiB,MAAM,IAAI,aAAa,CAAC;AACpI,QAAM,IAAI,KAAK,OAAO,OAAO,aAAa,QAAQ,IAAI,WAAW;AACjE,QAAM,IAAI,KAAK,MAAM,OAAO,YAAY,QAAQ,IAAI,WAAW;AAC/D,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,OAAO,KAAK;AAAA,IACZ,QAAQ,KAAK;AAAA,EACf;AACF;AAEA,SAAS,mBAAmB,SAAS;AACnC,SAAOA,kBAAiB,OAAO,EAAE,aAAa;AAChD;AAEA,SAAS,oBAAoB,SAAS,UAAU;AAC9C,MAAI,CAAC,cAAc,OAAO,KAAKA,kBAAiB,OAAO,EAAE,aAAa,SAAS;AAC7E,WAAO;AAAA,EACT;AACA,MAAI,UAAU;AACZ,WAAO,SAAS,OAAO;AAAA,EACzB;AACA,MAAI,kBAAkB,QAAQ;AAM9B,MAAI,mBAAmB,OAAO,MAAM,iBAAiB;AACnD,sBAAkB,gBAAgB,cAAc;AAAA,EAClD;AACA,SAAO;AACT;AAIA,SAAS,gBAAgB,SAAS,UAAU;AAC1C,QAAM,MAAM,UAAU,OAAO;AAC7B,MAAI,WAAW,OAAO,GAAG;AACvB,WAAO;AAAA,EACT;AACA,MAAI,CAAC,cAAc,OAAO,GAAG;AAC3B,QAAI,kBAAkB,cAAc,OAAO;AAC3C,WAAO,mBAAmB,CAAC,sBAAsB,eAAe,GAAG;AACjE,UAAI,UAAU,eAAe,KAAK,CAAC,mBAAmB,eAAe,GAAG;AACtE,eAAO;AAAA,MACT;AACA,wBAAkB,cAAc,eAAe;AAAA,IACjD;AACA,WAAO;AAAA,EACT;AACA,MAAI,eAAe,oBAAoB,SAAS,QAAQ;AACxD,SAAO,gBAAgB,eAAe,YAAY,KAAK,mBAAmB,YAAY,GAAG;AACvF,mBAAe,oBAAoB,cAAc,QAAQ;AAAA,EAC3D;AACA,MAAI,gBAAgB,sBAAsB,YAAY,KAAK,mBAAmB,YAAY,KAAK,CAAC,kBAAkB,YAAY,GAAG;AAC/H,WAAO;AAAA,EACT;AACA,SAAO,gBAAgB,mBAAmB,OAAO,KAAK;AACxD;AAEA,IAAM,kBAAkB,eAAgB,MAAM;AAC5C,QAAM,oBAAoB,KAAK,mBAAmB;AAClD,QAAM,kBAAkB,KAAK;AAC7B,QAAM,qBAAqB,MAAM,gBAAgB,KAAK,QAAQ;AAC9D,SAAO;AAAA,IACL,WAAW,8BAA8B,KAAK,WAAW,MAAM,kBAAkB,KAAK,QAAQ,GAAG,KAAK,QAAQ;AAAA,IAC9G,UAAU;AAAA,MACR,GAAG;AAAA,MACH,GAAG;AAAA,MACH,OAAO,mBAAmB;AAAA,MAC1B,QAAQ,mBAAmB;AAAA,IAC7B;AAAA,EACF;AACF;AAEA,SAAS,MAAM,SAAS;AACtB,SAAOA,kBAAiB,OAAO,EAAE,cAAc;AACjD;AAEA,IAAM,WAAW;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEA,SAAS,cAAc,GAAG,GAAG;AAC3B,SAAO,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,SAAS,EAAE,WAAW,EAAE;AAC7E;AAGA,SAAS,YAAY,SAAS,QAAQ;AACpC,MAAI,KAAK;AACT,MAAI;AACJ,QAAM,OAAO,mBAAmB,OAAO;AACvC,WAAS,UAAU;AACjB,QAAI;AACJ,iBAAa,SAAS;AACtB,KAAC,MAAM,OAAO,QAAQ,IAAI,WAAW;AACrC,SAAK;AAAA,EACP;AACA,WAAS,QAAQ,MAAM,WAAW;AAChC,QAAI,SAAS,QAAQ;AACnB,aAAO;AAAA,IACT;AACA,QAAI,cAAc,QAAQ;AACxB,kBAAY;AAAA,IACd;AACA,YAAQ;AACR,UAAM,2BAA2B,QAAQ,sBAAsB;AAC/D,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,CAAC,MAAM;AACT,aAAO;AAAA,IACT;AACA,QAAI,CAAC,SAAS,CAAC,QAAQ;AACrB;AAAA,IACF;AACA,UAAM,WAAW,MAAM,GAAG;AAC1B,UAAM,aAAa,MAAM,KAAK,eAAe,OAAO,MAAM;AAC1D,UAAM,cAAc,MAAM,KAAK,gBAAgB,MAAM,OAAO;AAC5D,UAAM,YAAY,MAAM,IAAI;AAC5B,UAAM,aAAa,CAAC,WAAW,QAAQ,CAAC,aAAa,QAAQ,CAAC,cAAc,QAAQ,CAAC,YAAY;AACjG,UAAM,UAAU;AAAA,MACd;AAAA,MACA,WAAW,IAAI,GAAG,IAAI,GAAG,SAAS,CAAC,KAAK;AAAA,IAC1C;AACA,QAAI,gBAAgB;AACpB,aAAS,cAAc,SAAS;AAC9B,YAAM,QAAQ,QAAQ,CAAC,EAAE;AACzB,UAAI,UAAU,WAAW;AACvB,YAAI,CAAC,eAAe;AAClB,iBAAO,QAAQ;AAAA,QACjB;AACA,YAAI,CAAC,OAAO;AAGV,sBAAY,WAAW,MAAM;AAC3B,oBAAQ,OAAO,IAAI;AAAA,UACrB,GAAG,GAAI;AAAA,QACT,OAAO;AACL,kBAAQ,OAAO,KAAK;AAAA,QACtB;AAAA,MACF;AACA,UAAI,UAAU,KAAK,CAAC,cAAc,0BAA0B,QAAQ,sBAAsB,CAAC,GAAG;AAQ5F,gBAAQ;AAAA,MACV;AACA,sBAAgB;AAAA,IAClB;AAIA,QAAI;AACF,WAAK,IAAI,qBAAqB,eAAe,iCACxC,UADwC;AAAA;AAAA,QAG3C,MAAM,KAAK;AAAA,MACb,EAAC;AAAA,IACH,SAAS,GAAG;AACV,WAAK,IAAI,qBAAqB,eAAe,OAAO;AAAA,IACtD;AACA,OAAG,QAAQ,OAAO;AAAA,EACpB;AACA,UAAQ,IAAI;AACZ,SAAO;AACT;AAUA,SAAS,WAAW,WAAW,UAAU,QAAQ,SAAS;AACxD,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,QAAM;AAAA,IACJ,iBAAiB;AAAA,IACjB,iBAAiB;AAAA,IACjB,gBAAgB,OAAO,mBAAmB;AAAA,IAC1C,cAAc,OAAO,yBAAyB;AAAA,IAC9C,iBAAiB;AAAA,EACnB,IAAI;AACJ,QAAM,cAAc,cAAc,SAAS;AAC3C,QAAM,YAAY,kBAAkB,iBAAiB,CAAC,GAAI,cAAc,qBAAqB,WAAW,IAAI,CAAC,GAAI,GAAG,qBAAqB,QAAQ,CAAC,IAAI,CAAC;AACvJ,YAAU,QAAQ,cAAY;AAC5B,sBAAkB,SAAS,iBAAiB,UAAU,QAAQ;AAAA,MAC5D,SAAS;AAAA,IACX,CAAC;AACD,sBAAkB,SAAS,iBAAiB,UAAU,MAAM;AAAA,EAC9D,CAAC;AACD,QAAM,YAAY,eAAe,cAAc,YAAY,aAAa,MAAM,IAAI;AAClF,MAAI,iBAAiB;AACrB,MAAI,iBAAiB;AACrB,MAAI,eAAe;AACjB,qBAAiB,IAAI,eAAe,UAAQ;AAC1C,UAAI,CAAC,UAAU,IAAI;AACnB,UAAI,cAAc,WAAW,WAAW,eAAe,gBAAgB;AAGrE,uBAAe,UAAU,QAAQ;AACjC,6BAAqB,cAAc;AACnC,yBAAiB,sBAAsB,MAAM;AAC3C,cAAI;AACJ,WAAC,kBAAkB,mBAAmB,QAAQ,gBAAgB,QAAQ,QAAQ;AAAA,QAChF,CAAC;AAAA,MACH;AACA,aAAO;AAAA,IACT,CAAC;AACD,QAAI,eAAe,CAAC,gBAAgB;AAClC,qBAAe,QAAQ,WAAW;AAAA,IACpC;AACA,mBAAe,QAAQ,QAAQ;AAAA,EACjC;AACA,MAAI;AACJ,MAAI,cAAc,iBAAiB,sBAAsB,SAAS,IAAI;AACtE,MAAI,gBAAgB;AAClB,cAAU;AAAA,EACZ;AACA,WAAS,YAAY;AACnB,UAAM,cAAc,sBAAsB,SAAS;AACnD,QAAI,eAAe,CAAC,cAAc,aAAa,WAAW,GAAG;AAC3D,aAAO;AAAA,IACT;AACA,kBAAc;AACd,cAAU,sBAAsB,SAAS;AAAA,EAC3C;AACA,SAAO;AACP,SAAO,MAAM;AACX,QAAI;AACJ,cAAU,QAAQ,cAAY;AAC5B,wBAAkB,SAAS,oBAAoB,UAAU,MAAM;AAC/D,wBAAkB,SAAS,oBAAoB,UAAU,MAAM;AAAA,IACjE,CAAC;AACD,iBAAa,QAAQ,UAAU;AAC/B,KAAC,mBAAmB,mBAAmB,QAAQ,iBAAiB,WAAW;AAC3E,qBAAiB;AACjB,QAAI,gBAAgB;AAClB,2BAAqB,OAAO;AAAA,IAC9B;AAAA,EACF;AACF;AAmBA,IAAMC,UAAS;AAuBf,IAAMC,QAAO;AAsBb,IAAMC,SAAQ;AAkBd,IAAMC,mBAAkB,CAAC,WAAW,UAAU,YAAY;AAIxD,QAAM,QAAQ,oBAAI,IAAI;AACtB,QAAM,gBAAgB;AAAA,IACpB;AAAA,KACG;AAEL,QAAM,oBAAoB,iCACrB,cAAc,WADO;AAAA,IAExB,IAAI;AAAA,EACN;AACA,SAAO,gBAAkB,WAAW,UAAU,iCACzC,gBADyC;AAAA,IAE5C,UAAU;AAAA,EACZ,EAAC;AACH;;;ACxuBA,IAAM,MAAM,CAAC,SAAS;AACtB,IAAM,MAAM,CAAC,GAAG;AAChB,SAAS,+BAA+B,IAAI,KAAK;AAC/C,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,CAAC;AAAA,EAC3B;AACF;AACA,SAAS,+BAA+B,IAAI,KAAK;AAC/C,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,CAAC;AAAA,EAC3B;AACF;AACA,IAAM,mBAAN,MAAuB;AAAA,EACrB,YAAY,YAAY,QAAQ,UAAU,mBAAmB;AAC3D,SAAK,aAAa;AAClB,SAAK,SAAS;AACd,SAAK,WAAW;AAChB,SAAK,oBAAoB;AACzB,SAAK,SAAS;AACd,SAAK,uBAAuB;AAI5B,SAAK,UAAU,IAAI,aAAa;AAIhC,SAAK,SAAS,IAAI,aAAa;AAI/B,SAAK,eAAe,IAAI,aAAa;AAIrC,SAAK,QAAQ;AAIb,SAAK,aAAa;AAIlB,SAAK,eAAe;AAKpB,SAAK,YAAY;AACjB,SAAK,iBAAiB;AACtB,SAAK,SAAS;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,MAAM,WAAW;AAEnB,QAAI,CAAC,WAAW;AACd;AAAA,IACF;AACA,UAAM,oBAAoB,KAAK;AAC/B,YAAQ,WAAW;AAAA,MACjB,KAAK;AACH,aAAK,SAAS;AACd;AAAA,MACF,KAAK;AACH,aAAK,SAAS;AACd;AAAA,MACF,KAAK;AACH,aAAK,SAAS;AACd;AAAA,MACF,KAAK;AACH,aAAK,SAAS;AACd;AAAA,MACF,KAAK;AACH,aAAK,SAAS;AACd;AAAA,MACF,KAAK;AACH,aAAK,SAAS;AACd;AAAA,MACF,KAAK;AACH,aAAK,SAAS;AACd;AAAA,MACF,KAAK;AACH,aAAK,SAAS;AACd;AAAA,MACF;AACE,aAAK,SAAS;AACd;AAAA,IACJ;AACA,SAAK,qBAAqB,KAAK,QAAQ,iBAAiB;AAAA,EAC1D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,aAAaC,OAAM,OAAO;AAExB,QAAI,KAAK,WAAWA,SAAQ,OAAO;AACjC,WAAK,aAAa,KAAKA,KAAI;AAAA,IAC7B;AACA,QAAIA,OAAM;AACR,UAAI,OAAO;AACT,aAAK,OAAO,KAAK,KAAK;AAAA,MACxB;AAEA,UAAI,KAAK,WAAW;AAClB,YAAI,KAAK,UAAU;AAGjB,gBAAM,gBAAgB,iBAAiB,KAAK,QAAQ;AACpD,gBAAMC,UAAS,cAAc,iBAAiB,sBAAsB;AACpE,gBAAM,SAAS,cAAc,iBAAiB,4BAA4B;AAC1E,eAAK,eAAeA,SAAQ,SAAS,IAAI,IAAI,OAAOA,QAAO,MAAM,MAAM,CAAC,EAAE,CAAC,CAAC,IAAI,OAAOA,QAAO,MAAM,OAAO,CAAC,EAAE,CAAC,CAAC,IAAI,OAAO;AAC3H,eAAK,eAAe,QAAQ,SAAS,IAAI,IAAI,OAAO,OAAO,MAAM,MAAM,CAAC,EAAE,CAAC,CAAC,IAAI,OAAO,OAAO,MAAM,OAAO,CAAC,EAAE,CAAC,CAAC,IAAI,OAAO;AAAA,QAC7H;AACA,YAAI,KAAK,WAAW,iBAAiB,KAAK,mBAAmB;AAC3D,eAAK,yBAAyB,WAAW,KAAK,WAAW,eAAe,KAAK,mBAAmB,KAAK,kBAAkB,KAAK,IAAI,CAAC;AAAA,QACnI;AAAA,MACF;AAAA,IACF,OAAO;AACL,WAAK,QAAQ;AACb,UAAI,OAAO;AACT,aAAK,QAAQ,KAAK,KAAK;AAAA,MACzB;AAAA,IACF;AACA,SAAK,SAASD;AACd,SAAK,kBAAkB,aAAa;AAAA,EACtC;AAAA,EACA,WAAW,OAAO;AAChB,UAAM,MAAM,OAAO,oBAAoB;AACvC,WAAO,KAAK,MAAM,QAAQ,GAAG,IAAI;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA,EAIA,oBAAoB;AAElB,SAAK,OAAO,kBAAkB,YAAY;AACxC,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI,MAAME,iBAAgB,KAAK,WAAW,eAAe,KAAK,mBAAmB;AAAA,QAC/E,WAAW,KAAK;AAAA,QAChB,UAAU;AAAA,QACV,YAAY,CAACD,QAAO,KAAK,WAAW,GAAGE,MAAK;AAAA,UAC1C,2BAA2B;AAAA,QAC7B,CAAC,GAAGC,OAAM;AAAA,UACR,SAAS,KAAK;AAAA,QAChB,CAAC,CAAC;AAAA,MACJ,CAAC;AACD,YAAM,oBAAoB,KAAK;AAC/B,WAAK,SAAS;AACd,WAAK,qBAAqB,KAAK,QAAQ,iBAAiB;AAGxD,aAAO,OAAO,KAAK,kBAAkB,OAAO;AAAA,QAC1C,UAAU;AAAA,QACV,KAAK;AAAA,QACL,MAAM;AAAA;AAAA,QAEN,WAAW,aAAa,KAAK,WAAW,CAAC,CAAC,MAAM,KAAK,WAAW,CAAC,CAAC;AAAA,MACpE,CAAC;AACD,UAAI,eAAe,OAAO;AACxB,cAAM;AAAA,UACJ,GAAG;AAAA,UACH,GAAG;AAAA,QACL,IAAI,eAAe;AACnB,cAAM,aAAa;AAAA,UACjB,KAAK;AAAA,UACL,OAAO;AAAA,UACP,QAAQ;AAAA,UACR,MAAM;AAAA,QACR,EAAE,UAAU,MAAM,GAAG,EAAE,CAAC,CAAC;AACzB,aAAK,SAAS,MAAM,OAAO,UAAU,OAAO,GAAG,MAAM,OAAO;AAC5D,aAAK,SAAS,MAAM,MAAM,UAAU,OAAO,GAAG,MAAM,OAAO;AAC3D,aAAK,SAAS,MAAM,QAAQ;AAC5B,aAAK,SAAS,MAAM,SAAS;AAC7B,YAAI,YAAY;AACd,eAAK,SAAS,MAAM,UAAU,IAAI,GAAG,CAAC,KAAK,WAAW;AAAA,QACxD;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY,SAAS;AAEnB,UAAM,gBAAgB,KAAK;AAC3B,SAAK,aAAa,KAAK;AAEvB,QAAI,QAAQ,aAAa,CAAC,QAAQ,UAAU,aAAa;AAEvD,WAAK,oBAAoB,KAAK,WAAW,cAAc,cAAc,uBAAuB;AAC5F,WAAK,kBAAkB,aAAa,SAAS,EAAE;AAC/C,WAAK,WAAW,KAAK,WAAW,cAAc,cAAc,yBAAyB;AAAA,IACvF;AACA,SAAK,aAAa,aAAa;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA,EAIA,kBAAkB;AAChB,SAAK,qBAAqB;AAAA,EAC5B;AAAA,EACA,uBAAuB;AACrB,SAAK,qBAAqB,KAAK,MAAM;AAErC,SAAK,oBAAoB,KAAK,WAAW,cAAc,cAAc,uBAAuB;AAC5F,SAAK,WAAW,KAAK,WAAW,cAAc,cAAc,yBAAyB;AAErF,SAAK,aAAa,KAAK,MAAM;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc;AACZ,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA;AAAA;AAAA,EAIA,UAAU;AACR,QAAI,KAAK,wBAAwB;AAC/B,WAAK,uBAAuB;AAAA,IAC9B;AACA,SAAK,yBAAyB;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,qBAAqB,cAAc,mBAAmB;AACpD,QAAI,KAAK,WAAW,iBAAiB,sBAAsB,cAAc;AACvE,YAAM,SAAS,IAAI,OAAO,uBAAuB;AAEjD,WAAK,WAAW,cAAc,UAAU,QAAQ,eAAa;AAC3D,YAAI,OAAO,KAAK,SAAS,GAAG;AAC1B,eAAK,SAAS,YAAY,KAAK,WAAW,eAAe,GAAG,SAAS,EAAE;AAAA,QACzE;AAAA,MACF,CAAC;AACD,WAAK,SAAS,SAAS,KAAK,WAAW,eAAe,GAAG,KAAK,oBAAoB,GAAG,YAAY,EAAE;AAAA,IACrG;AAAA,EACF;AACF;AACA,iBAAiB,OAAO,SAAS,yBAAyB,mBAAmB;AAC3E,SAAO,KAAK,qBAAqB,kBAAqB,kBAAqB,UAAU,GAAM,kBAAqB,MAAM,GAAM,kBAAqB,SAAS,GAAM,kBAAqB,iBAAiB,CAAC;AACzM;AACA,iBAAiB,OAAyB,kBAAkB;AAAA,EAC1D,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,IAAI,cAAc,EAAE,GAAG,CAAC,IAAI,cAAc,EAAE,CAAC;AAAA,EAC1D,UAAU;AAAA,EACV,cAAc,SAAS,8BAA8B,IAAI,KAAK;AAC5D,QAAI,KAAK,GAAG;AACV,MAAG,YAAY,uBAAuB,IAAI,KAAK,EAAE,6BAA6B,IAAI,UAAU,EAAE,+BAA+B,IAAI,YAAY,EAAE,4BAA4B,IAAI,SAAS,EAAE,0BAA0B,IAAI,cAAc,EAAE,sBAAsB,IAAI,MAAM;AAAA,IAC1Q;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,OAAO;AAAA,IACP,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,WAAW;AAAA,IACX,QAAQ;AAAA,EACV;AAAA,EACA,SAAS;AAAA,IACP,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,cAAc;AAAA,EAChB;AAAA,EACA,YAAY;AAAA,EACZ,UAAU,CAAI,oBAAoB;AACpC,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAS;AAAA,IACX,CAAC;AAAA,EACH,GAAG;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC,2BAA2B;AAAA,IACpC,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,iCAAiC;AAAA,IAC1C,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,mCAAmC;AAAA,IAC5C,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,gCAAgC;AAAA,IACzC,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,8BAA8B;AAAA,IACvC,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,0BAA0B;AAAA,IACnC,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAKH,IAAM,iBAAN,MAAqB;AAAA,EACnB,YAAY,mBAAmB;AAC7B,SAAK,oBAAoB;AACzB,SAAK,eAAe;AACpB,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,kBAAkB;AAChB,QAAI,KAAK,gBAAgB;AAEvB,WAAK,YAAY,CAAC,CAAC,KAAK,eAAe,cAAc,QAAQ,2BAA2B;AAExF,WAAK,kBAAkB,cAAc;AAAA,IACvC;AAAA,EACF;AACF;AACA,eAAe,OAAO,SAAS,uBAAuB,mBAAmB;AACvE,SAAO,KAAK,qBAAqB,gBAAmB,kBAAqB,iBAAiB,CAAC;AAC7F;AACA,eAAe,OAAyB,kBAAkB;AAAA,EACxD,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,qBAAqB,GAAG,CAAC,qBAAqB,CAAC;AAAA,EAC5D,WAAW,SAAS,qBAAqB,IAAI,KAAK;AAChD,QAAI,KAAK,GAAG;AACV,MAAG,YAAY,KAAK,CAAC;AAAA,IACvB;AACA,QAAI,KAAK,GAAG;AACV,UAAI;AACJ,MAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,iBAAiB,GAAG;AAAA,IACvE;AAAA,EACF;AAAA,EACA,UAAU;AAAA,EACV,cAAc,SAAS,4BAA4B,IAAI,KAAK;AAC1D,QAAI,KAAK,GAAG;AACV,MAAG,YAAY,gBAAgB,IAAI,YAAY;AAAA,IACjD;AAAA,EACF;AAAA,EACA,YAAY;AAAA,EACZ,oBAAoB;AAAA,EACpB,OAAO;AAAA,EACP,MAAM;AAAA,EACN,QAAQ,CAAC,CAAC,WAAW,EAAE,GAAG,CAAC,GAAG,sBAAsB,GAAG,CAAC,SAAS,+CAA+C,GAAG,MAAM,GAAG,CAAC,SAAS,sBAAsB,GAAG,MAAM,GAAG,CAAC,GAAG,sBAAsB,0BAA0B,GAAG,CAAC,GAAG,oBAAoB,CAAC;AAAA,EACxP,UAAU,SAAS,wBAAwB,IAAI,KAAK;AAClD,QAAI,KAAK,GAAG;AACV,MAAG,gBAAgB;AACnB,MAAG,eAAe,GAAG,QAAQ,GAAG,CAAC,EAAE,GAAG,KAAK;AAC3C,MAAG,aAAa,CAAC;AACjB,MAAG,aAAa;AAChB,MAAG,WAAW,GAAG,gCAAgC,GAAG,GAAG,QAAQ,CAAC;AAChE,MAAG,aAAa;AAChB,MAAG,WAAW,GAAG,gCAAgC,GAAG,GAAG,QAAQ,CAAC;AAAA,IAClE;AACA,QAAI,KAAK,GAAG;AACV,MAAG,UAAU,CAAC;AACd,MAAG,WAAW,QAAQ,IAAI,SAAS;AACnC,MAAG,UAAU;AACb,MAAG,WAAW,QAAQ,CAAC,IAAI,SAAS;AAAA,IACtC;AAAA,EACF;AAAA,EACA,cAAc,CAAI,IAAI;AAAA,EACtB,eAAe;AACjB,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IASZ,CAAC;AAAA,EACH,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAS;AAAA,IACX,CAAC;AAAA,EACH,GAAG;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,IAC7B,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,SAAS;AAAA,IAClB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,gBAAN,MAAoB;AAAC;AACrB,cAAc,OAAO,SAAS,sBAAsB,mBAAmB;AACrE,SAAO,KAAK,qBAAqB,eAAe;AAClD;AACA,cAAc,OAAyB,iBAAiB;AAAA,EACtD,MAAM;AAAA,EACN,cAAc,CAAC,kBAAkB,cAAc;AAAA,EAC/C,SAAS,CAAC,YAAY;AAAA,EACtB,SAAS,CAAC,kBAAkB,cAAc;AAC5C,CAAC;AACD,cAAc,OAAyB,iBAAiB;AAAA,EACtD,SAAS,CAAC,YAAY;AACxB,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,cAAc,CAAC,kBAAkB,cAAc;AAAA,MAC/C,SAAS,CAAC,kBAAkB,cAAc;AAAA,MAC1C,SAAS,CAAC,YAAY;AAAA,IACxB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;AC3cH,IAAMC,OAAM,CAAC,gBAAgB;AAC7B,IAAMC,OAAM,CAAC,GAAG;AAChB,IAAM,MAAM,SAAO;AAAA,EACjB,WAAW;AACb;AACA,SAAS,sDAAsD,IAAI,KAAK;AACtE,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,OAAO,CAAC;AACX,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,WAAW;AAAA,EACzC;AACF;AACA,SAAS,uDAAuD,IAAI,KAAK;AAAC;AAC1E,SAAS,yCAAyC,IAAI,KAAK;AACzD,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,wDAAwD,GAAG,GAAG,eAAe,CAAC;AAAA,EACjG;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,oBAAoB,OAAO,WAAW,EAAE,2BAA8B,gBAAgB,GAAG,KAAK,OAAO,eAAe,CAAC;AAAA,EACrI;AACF;AACA,SAAS,8CAA8C,IAAI,KAAK;AAC9D,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,CAAC;AAAA,EAC3B;AACF;AACA,SAAS,8CAA8C,IAAI,KAAK;AAC9D,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,CAAC;AAAA,EAC3B;AACF;AACA,SAAS,uCAAuC,IAAI,KAAK;AACvD,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,WAAW,GAAG,uDAAuD,GAAG,GAAG,gBAAgB,CAAC,EAAE,GAAG,0CAA0C,GAAG,GAAG,MAAM,CAAC,EAAE,GAAG,+CAA+C,GAAG,GAAG,QAAQ,CAAC;AAC9N,IAAG,aAAa;AAChB,IAAG,WAAW,GAAG,+CAA+C,GAAG,GAAG,QAAQ,CAAC;AAC/E,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,QAAQ,CAAC,OAAO,WAAW,OAAO,WAAW,CAAC;AAC5D,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,WAAW,OAAO,WAAW,CAAC;AAC3D,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,SAAS;AACtC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,OAAO,SAAS;AAAA,EACzC;AACF;AACA,SAAS,wBAAwB,IAAI,KAAK;AACxC,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,WAAW,GAAG,wCAAwC,GAAG,GAAG,gBAAgB,CAAC;AAChF,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,MAAM,OAAO,EAAE;AAC7B,IAAG,YAAY,eAAe,CAAC,OAAO,MAAM;AAC5C,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,OAAO,QAAQ;AAAA,EACxC;AACF;AACA,SAAS,iDAAiD,IAAI,KAAK;AACjE,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,OAAO,CAAC;AACX,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,WAAW;AAAA,EACzC;AACF;AACA,SAAS,kDAAkD,IAAI,KAAK;AAAC;AACrE,SAAS,oCAAoC,IAAI,KAAK;AACpD,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,mDAAmD,GAAG,GAAG,eAAe,CAAC;AAAA,EAC5F;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,oBAAoB,OAAO,WAAW,EAAE,2BAA8B,gBAAgB,GAAG,KAAK,OAAO,eAAe,CAAC;AAAA,EACrI;AACF;AACA,SAAS,yCAAyC,IAAI,KAAK;AACzD,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,CAAC;AAAA,EAC3B;AACF;AACA,SAAS,yCAAyC,IAAI,KAAK;AACzD,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,CAAC;AAAA,EAC3B;AACF;AACA,SAAS,kCAAkC,IAAI,KAAK;AAClD,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,WAAW,aAAa,SAAS,8DAA8D;AAChG,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,mBAAmB,CAAC;AAAA,IACnD,CAAC,EAAE,WAAW,SAAS,4DAA4D;AACjF,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,iBAAiB,CAAC;AAAA,IACjD,CAAC;AACD,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,WAAW,GAAG,kDAAkD,GAAG,GAAG,gBAAgB,CAAC,EAAE,GAAG,qCAAqC,GAAG,GAAG,MAAM,CAAC,EAAE,GAAG,0CAA0C,GAAG,GAAG,QAAQ,CAAC;AAC/M,IAAG,aAAa;AAChB,IAAG,WAAW,GAAG,0CAA0C,GAAG,GAAG,QAAQ,CAAC;AAC1E,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,MAAM,OAAO,EAAE;AAC7B,IAAG,YAAY,eAAe,CAAC,OAAO,MAAM;AAC5C,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,QAAQ,CAAC,OAAO,WAAW,OAAO,WAAW,CAAC;AAC5D,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,WAAW,OAAO,WAAW,CAAC;AAC3D,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,SAAS;AACtC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,OAAO,SAAS;AAAA,EACzC;AACF;AAoBA,IAAM,UAAN,MAAM,iBAAgB,iBAAiB;AAAA,EACrC,YAAY,YAAY,QAAQ,UAAU,mBAAmB;AAC3D,UAAM,YAAY,QAAQ,UAAU,iBAAiB;AACrD,SAAK,aAAa;AAClB,SAAK,SAAS;AACd,SAAK,WAAW;AAChB,SAAK,oBAAoB;AACzB,SAAK,eAAe;AACpB,SAAK,KAAK,WAAW,SAAQ,cAAc;AAI3C,SAAK,eAAe;AAIpB,SAAK,eAAe;AAIpB,SAAK,WAAW;AAChB,SAAK,eAAe;AACpB,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,WAAW,OAAO;AAGhB,iBAAa,KAAK,SAAS;AAC3B,SAAK,YAAY,WAAW,MAAM;AAChC,WAAK,aAAa,MAAM,KAAK;AAAA,IAC/B,GAAG,KAAK,YAAY;AAAA,EACtB;AAAA,EACA,WAAW,OAAO;AAGhB,iBAAa,KAAK,SAAS;AAC3B,SAAK,YAAY,WAAW,MAAM;AAChC,WAAK,aAAa,OAAO,KAAK;AAAA,IAChC,GAAG,KAAK,YAAY;AAAA,EACtB;AAAA,EACA,SAAS,OAAO;AACd,QAAI,QAAQ,MAAM,QAAQ,UAAU;AAClC,YAAM,gBAAgB;AACtB,WAAK,aAAa,OAAO,KAAK;AAAA,IAChC;AAAA,EACF;AAAA;AAAA,EAEA,YAAY,OAAO;AACjB,SAAK,aAAa,MAAM,KAAK;AAAA,EAC/B;AAAA,EACA,eAAe,OAAO;AACpB,SAAK,aAAa,OAAO,KAAK;AAAA,EAChC;AAAA,EACA,WAAW,OAAO;AAChB,WAAO,iBAAiB;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY,SAAS;AAEnB,UAAM,gBAAgB,KAAK;AAC3B,SAAK,aAAa,KAAK;AAEvB,QAAI,QAAQ,aAAa,CAAC,QAAQ,UAAU,eAAe,QAAQ,YAAY,CAAC,QAAQ,SAAS,eAAe,CAAC,QAAQ,SAAS,gBAG/H,QAAQ,aAAa;AAMtB,WAAK,kBAAkB,cAAc;AAErC,WAAK,oBAAoB,KAAK,WAAW,cAAc,cAAc,uBAAuB;AAC5F,WAAK,mBAAmB,aAAa,SAAS,EAAE;AAChD,WAAK,WAAW,KAAK,WAAW,cAAc,cAAc,yBAAyB;AAAA,IACvF;AACA,SAAK,aAAa,aAAa;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA,EAIA,wBAAwB;AACtB,QAAI,KAAK,SAAS;AAChB,YAAM,gBAAgB,KAAK,QAAQ,cAAc,cAAc,QAAQ;AACvE,UAAI,iBAAiB,CAAC,cAAc,aAAa,iBAAiB,GAAG;AACnE,sBAAc,aAAa,mBAAmB,KAAK,EAAE;AAAA,MACvD;AAAA,IACF;AAAA,EACF;AACF;AACA,QAAQ,eAAe;AACvB,QAAQ,OAAO,SAAS,gBAAgB,mBAAmB;AACzD,SAAO,KAAK,qBAAqB,SAAY,kBAAqB,UAAU,GAAM,kBAAqB,MAAM,GAAM,kBAAqB,SAAS,GAAM,kBAAqB,iBAAiB,CAAC;AAChM;AACA,QAAQ,OAAyB,kBAAkB;AAAA,EACjD,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,aAAa,GAAG,CAAC,aAAa,CAAC;AAAA,EAC5C,WAAW,SAAS,cAAc,IAAI,KAAK;AACzC,QAAI,KAAK,GAAG;AACV,MAAG,YAAYC,MAAK,CAAC;AAAA,IACvB;AACA,QAAI,KAAK,GAAG;AACV,UAAI;AACJ,MAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,UAAU,GAAG;AAAA,IAChE;AAAA,EACF;AAAA,EACA,UAAU;AAAA,EACV,cAAc,SAAS,qBAAqB,IAAI,KAAK;AACnD,QAAI,KAAK,GAAG;AACV,MAAG,WAAW,cAAc,SAAS,sCAAsC,QAAQ;AACjF,eAAO,IAAI,WAAW,MAAM;AAAA,MAC9B,CAAC,EAAE,cAAc,SAAS,sCAAsC,QAAQ;AACtE,eAAO,IAAI,WAAW,MAAM;AAAA,MAC9B,CAAC,EAAE,SAAS,SAAS,iCAAiC,QAAQ;AAC5D,eAAO,IAAI,SAAS,MAAM;AAAA,MAC5B,CAAC,EAAE,WAAW,SAAS,mCAAmC,QAAQ;AAChE,eAAO,IAAI,YAAY,MAAM;AAAA,MAC/B,CAAC,EAAE,YAAY,SAAS,oCAAoC,QAAQ;AAClE,eAAO,IAAI,eAAe,MAAM;AAAA,MAClC,CAAC;AAAA,IACH;AACA,QAAI,KAAK,GAAG;AACV,MAAG,YAAY,gBAAgB,IAAI,YAAY;AAAA,IACjD;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,IAAI;AAAA,IACJ,cAAc;AAAA,IACd,cAAc;AAAA,IACd,UAAU;AAAA,IACV,aAAa;AAAA,IACb,iBAAiB;AAAA,EACnB;AAAA,EACA,YAAY;AAAA,EACZ,UAAU,CAAI,4BAA+B,oBAAoB;AAAA,EACjE,oBAAoBC;AAAA,EACpB,OAAO;AAAA,EACP,MAAM;AAAA,EACN,QAAQ,CAAC,CAAC,kBAAkB,EAAE,GAAG,CAAC,SAAS,gBAAgB,QAAQ,WAAW,GAAG,MAAM,GAAG,MAAM,GAAG,CAAC,QAAQ,WAAW,GAAG,gBAAgB,GAAG,IAAI,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG,wBAAwB,sBAAsB,GAAG,CAAC,SAAS,+CAA+C,GAAG,MAAM,GAAG,CAAC,SAAS,sBAAsB,GAAG,MAAM,GAAG,CAAC,GAAG,oBAAoB,yBAAyB,GAAG,CAAC,GAAG,sBAAsB,0BAA0B,GAAG,CAAC,GAAG,oBAAoB,CAAC;AAAA,EAC3c,UAAU,SAAS,iBAAiB,IAAI,KAAK;AAC3C,QAAI,KAAK,GAAG;AACV,MAAG,gBAAgB;AACnB,MAAG,eAAe,GAAG,QAAQ,MAAM,CAAC;AACpC,MAAG,aAAa,CAAC;AACjB,MAAG,aAAa;AAChB,MAAG,WAAW,GAAG,yBAAyB,GAAG,GAAG,QAAQ,CAAC;AAAA,IAC3D;AACA,QAAI,KAAK,GAAG;AACV,MAAG,UAAU,CAAC;AACd,MAAG,WAAW,QAAQ,IAAI,WAAW;AAAA,IACvC;AAAA,EACF;AAAA,EACA,cAAc,CAAI,MAAS,gBAAgB;AAAA,EAC3C,eAAe;AAAA,EACf,iBAAiB;AACnB,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,SAAS,CAAC;AAAA,IAChF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAoBZ,CAAC;AAAA,EACH,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAS;AAAA,IACX,CAAC;AAAA,EACH,GAAG;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,IAC7B,CAAC;AAAA,IACD,IAAI,CAAC;AAAA,MACH,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC;AAAA,IACjC,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC;AAAA,IACjC,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC;AAAA,IAC5B,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC;AAAA,IAC9B,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC;AAAA,IAC/B,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAWH,IAAM,oBAAN,MAAM,2BAA0B,iBAAiB;AAAA,EAC/C,YAAY,YAAY,QAAQ,UAAU,mBAAmB;AAC3D,UAAM,YAAY,QAAQ,UAAU,iBAAiB;AACrD,SAAK,aAAa;AAClB,SAAK,SAAS;AACd,SAAK,WAAW;AAChB,SAAK,oBAAoB;AACzB,SAAK,KAAK,sBAAsB,mBAAkB,cAAc;AAChE,SAAK,cAAc;AAInB,SAAK,2BAA2B;AAChC,SAAK,eAAe;AACpB,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,OAAO,OAAO;AAEZ,QAAI,CAAC,KAAK,0BAA0B;AAClC,WAAK,aAAa,OAAO,KAAK;AAAA,IAChC;AAAA,EACF;AAAA,EACA,QAAQ,OAAO;AACb,QAAI,MAAM,WAAW,GAAG;AACtB,WAAK,aAAa,CAAC,KAAK,QAAQ,KAAK;AAAA,IACvC;AAAA,EACF;AAAA,EACA,qBAAqB;AACnB,SAAK,2BAA2B;AAAA,EAClC;AAAA,EACA,mBAAmB;AACjB,SAAK,2BAA2B;AAAA,EAClC;AAAA,EACA,SAAS,OAAO;AACd,QAAI,KAAK,UAAU,MAAM,QAAQ,UAAU;AACzC,YAAM,gBAAgB;AACtB,WAAK,aAAa,OAAO,KAAK;AAAA,IAChC;AAAA,EACF;AAAA,EACA,WAAW,OAAO;AAChB,SAAK,aAAa,OAAO,KAAK;AAAA,EAChC;AAAA,EACA,WAAW,OAAO;AAChB,QAAI,KAAK,aAAa;AACpB,WAAK,aAAa,MAAM,KAAK;AAAA,IAC/B;AAAA,EACF;AAAA,EACA,QAAQ,OAAO;AACb,SAAK,aAAa,MAAM,KAAK;AAAA,EAC/B;AAAA,EACA,WAAW,OAAO;AAChB,WAAO,iBAAiB;AAAA,EAC1B;AACF;AACA,kBAAkB,eAAe;AACjC,kBAAkB,OAAO,SAAS,0BAA0B,mBAAmB;AAC7E,SAAO,KAAK,qBAAqB,mBAAsB,kBAAqB,UAAU,GAAM,kBAAqB,MAAM,GAAM,kBAAqB,SAAS,GAAM,kBAAqB,iBAAiB,CAAC;AAC1M;AACA,kBAAkB,OAAyB,kBAAkB;AAAA,EAC3D,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,wBAAwB,GAAG,CAAC,wBAAwB,CAAC;AAAA,EAClE,cAAc,SAAS,+BAA+B,IAAI,KAAK;AAC7D,QAAI,KAAK,GAAG;AACV,MAAG,WAAW,SAAS,SAAS,2CAA2C,QAAQ;AACjF,eAAO,IAAI,SAAS,MAAM;AAAA,MAC5B,CAAC,EAAE,cAAc,SAAS,gDAAgD,QAAQ;AAChF,eAAO,IAAI,WAAW,MAAM;AAAA,MAC9B,CAAC,EAAE,cAAc,SAAS,gDAAgD,QAAQ;AAChF,eAAO,IAAI,WAAW,MAAM;AAAA,MAC9B,CAAC,EAAE,WAAW,SAAS,6CAA6C,QAAQ;AAC1E,eAAO,IAAI,QAAQ,MAAM;AAAA,MAC3B,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,IAAI;AAAA,IACJ,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,aAAa;AAAA,EACf;AAAA,EACA,YAAY;AAAA,EACZ,UAAU,CAAI,0BAA0B;AAAA,EACxC,oBAAoBA;AAAA,EACpB,OAAO;AAAA,EACP,MAAM;AAAA,EACN,QAAQ,CAAC,CAAC,QAAQ,UAAU,GAAG,wBAAwB,GAAG,QAAQ,WAAW,GAAG,CAAC,SAAS,gBAAgB,QAAQ,WAAW,GAAG,MAAM,aAAa,WAAW,GAAG,MAAM,GAAG,CAAC,QAAQ,WAAW,GAAG,gBAAgB,GAAG,aAAa,WAAW,IAAI,GAAG,CAAC,aAAa,UAAU,GAAG,wBAAwB,yBAAyB,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,SAAS,+CAA+C,GAAG,MAAM,GAAG,CAAC,SAAS,sBAAsB,GAAG,MAAM,GAAG,CAAC,GAAG,oBAAoB,yBAAyB,GAAG,CAAC,GAAG,sBAAsB,0BAA0B,GAAG,CAAC,GAAG,oBAAoB,CAAC;AAAA,EACpkB,UAAU,SAAS,2BAA2B,IAAI,KAAK;AACrD,QAAI,KAAK,GAAG;AACV,MAAG,gBAAgB;AACnB,MAAG,eAAe,GAAG,UAAU,CAAC;AAChC,MAAG,WAAW,QAAQ,SAAS,kDAAkD,QAAQ;AACvF,eAAO,IAAI,OAAO,MAAM;AAAA,MAC1B,CAAC,EAAE,aAAa,SAAS,uDAAuD,QAAQ;AACtF,eAAO,IAAI,QAAQ,MAAM;AAAA,MAC3B,CAAC;AACD,MAAG,aAAa,CAAC;AACjB,MAAG,aAAa;AAChB,MAAG,WAAW,GAAG,mCAAmC,GAAG,GAAG,QAAQ,CAAC;AAAA,IACrE;AACA,QAAI,KAAK,GAAG;AACV,MAAG,YAAY,iBAAiB,IAAI,EAAE,EAAE,iBAAiB,IAAI,MAAM,EAAE,oBAAoB,IAAI,SAAS,IAAI,KAAK,IAAI;AACnH,MAAG,UAAU,CAAC;AACd,MAAG,WAAW,QAAQ,IAAI,WAAW;AAAA,IACvC;AAAA,EACF;AAAA,EACA,cAAc,CAAI,MAAS,gBAAgB;AAAA,EAC3C,eAAe;AAAA,EACf,iBAAiB;AACnB,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IA2BZ,CAAC;AAAA,EACH,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAS;AAAA,IACX,CAAC;AAAA,EACH,GAAG;AAAA,IACD,IAAI,CAAC;AAAA,MACH,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC;AAAA,IAC5B,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC;AAAA,IACjC,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC;AAAA,IACjC,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC;AAAA,IAC9B,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,gBAAN,MAAoB;AAAC;AACrB,cAAc,OAAO,SAAS,sBAAsB,mBAAmB;AACrE,SAAO,KAAK,qBAAqB,eAAe;AAClD;AACA,cAAc,OAAyB,iBAAiB;AAAA,EACtD,MAAM;AAAA,EACN,cAAc,CAAC,SAAS,iBAAiB;AAAA,EACzC,SAAS,CAAC,cAAc,aAAa;AAAA,EACrC,SAAS,CAAC,SAAS,iBAAiB;AACtC,CAAC;AACD,cAAc,OAAyB,iBAAiB;AAAA,EACtD,SAAS,CAAC,cAAc,aAAa;AACvC,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,cAAc,CAAC,SAAS,iBAAiB;AAAA,MACzC,SAAS,CAAC,SAAS,iBAAiB;AAAA,MACpC,SAAS,CAAC,cAAc,aAAa;AAAA,IACvC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;AC1lBH,IAAMC,OAAM,CAAC,GAAG;AAChB,IAAMC,OAAM,CAAC,QAAQ;AACrB,IAAM,SAAN,MAAa;AAAA,EACX,cAAc;AAMZ,SAAK,YAAY;AAIjB,SAAK,WAAW;AAMhB,SAAK,WAAW;AAIhB,SAAK,eAAe;AAEpB,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,UAAU,MAAM;AAClB,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,IAAI,gBAAgB;AAClB,WAAO,KAAK,cAAc,aAAa,CAAC,KAAK;AAAA,EAC/C;AAAA,EACA,IAAI,kBAAkB;AACpB,WAAO,KAAK,cAAc;AAAA,EAC5B;AAAA,EACA,IAAI,iBAAiB;AACnB,WAAO,KAAK,cAAc;AAAA,EAC5B;AAAA,EACA,IAAI,cAAc;AAChB,WAAO,KAAK,cAAc;AAAA,EAC5B;AAAA,EACA,IAAI,eAAe;AACjB,WAAO,KAAK,cAAc,YAAY,KAAK,cAAc;AAAA,EAC3D;AAAA,EACA,IAAI,iBAAiB;AACnB,WAAO,KAAK,cAAc;AAAA,EAC5B;AAAA,EACA,IAAI,cAAc;AAChB,WAAO,KAAK,cAAc;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,YAAY;AACd,WAAO,KAAK,SAAS,QAAQ,CAAC,KAAK;AAAA,EACrC;AAAA,EACA,IAAI,aAAa;AACf,WAAO,KAAK,SAAS,QAAQ,CAAC,KAAK;AAAA,EACrC;AAAA,EACA,IAAI,YAAY;AACd,WAAO,KAAK,SAAS;AAAA,EACvB;AAAA,EACA,IAAI,iBAAiB;AACnB,WAAO,KAAK,SAAS;AAAA,EACvB;AAAA,EACA,IAAI,oBAAoB;AACtB,WAAO,KAAK,SAAS;AAAA,EACvB;AAAA;AAAA,EAEA,IAAI,kBAAkB;AACpB,WAAO,KAAK,SAAS,QAAQ,CAAC,KAAK;AAAA,EACrC;AAAA,EACA,IAAI,mBAAmB;AACrB,WAAO,KAAK,SAAS,QAAQ,CAAC,KAAK;AAAA,EACrC;AAAA,EACA,IAAI,kBAAkB;AACpB,WAAO,KAAK,SAAS;AAAA,EACvB;AAAA,EACA,IAAI,uBAAuB;AACzB,WAAO,KAAK,SAAS;AAAA,EACvB;AAAA,EACA,IAAI,0BAA0B;AAC5B,WAAO,KAAK,SAAS;AAAA,EACvB;AACF;AACA,OAAO,OAAO,SAAS,eAAe,mBAAmB;AACvD,SAAO,KAAK,qBAAqB,QAAQ;AAC3C;AACA,OAAO,OAAyB,kBAAkB;AAAA,EAChD,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,IAAI,aAAa,EAAE,GAAG,CAAC,IAAI,aAAa,EAAE,CAAC;AAAA,EACxD,UAAU;AAAA,EACV,cAAc,SAAS,oBAAoB,IAAI,KAAK;AAClD,QAAI,KAAK,GAAG;AACV,MAAG,YAAY,iBAAiB,IAAI,QAAQ,EAAE,uBAAuB,IAAI,QAAQ,EAAE,wBAAwB,IAAI,YAAY,EAAE,YAAY,IAAI,SAAS,EAAE,qBAAqB,IAAI,aAAa,EAAE,uBAAuB,IAAI,eAAe,EAAE,sBAAsB,IAAI,cAAc,EAAE,mBAAmB,IAAI,WAAW,EAAE,oBAAoB,IAAI,YAAY,EAAE,8BAA8B,IAAI,cAAc,EAAE,2BAA2B,IAAI,WAAW,EAAE,gBAAgB,IAAI,SAAS,EAAE,gBAAgB,IAAI,UAAU,EAAE,gBAAgB,IAAI,SAAS,EAAE,gBAAgB,IAAI,cAAc,EAAE,iBAAiB,IAAI,iBAAiB,EAAE,wBAAwB,IAAI,eAAe,EAAE,wBAAwB,IAAI,gBAAgB,EAAE,wBAAwB,IAAI,eAAe,EAAE,wBAAwB,IAAI,oBAAoB,EAAE,yBAAyB,IAAI,uBAAuB;AAAA,IACx1B;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,IACX,WAAW;AAAA,IACX,MAAM;AAAA,IACN,UAAU;AAAA,IACV,UAAU;AAAA,IACV,cAAc;AAAA,EAChB;AAAA,EACA,YAAY;AACd,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,QAAQ,CAAC;AAAA,IAC/E,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,IAC9B,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,2BAA2B;AAAA,IACpC,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,4BAA4B;AAAA,IACrC,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC,yBAAyB;AAAA,IAClC,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,MACN,MAAM,CAAC,2BAA2B;AAAA,IACpC,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,0BAA0B;AAAA,IACnC,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC,uBAAuB;AAAA,IAChC,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,wBAAwB;AAAA,IACjC,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,kCAAkC;AAAA,IAC3C,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC,+BAA+B;AAAA,IACxC,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,IAC7B,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,IAC7B,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,IAC7B,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,IAC7B,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,IAC9B,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,MACN,MAAM,CAAC,4BAA4B;AAAA,IACrC,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,4BAA4B;AAAA,IACrC,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,MACN,MAAM,CAAC,4BAA4B;AAAA,IACrC,CAAC;AAAA,IACD,sBAAsB,CAAC;AAAA,MACrB,MAAM;AAAA,MACN,MAAM,CAAC,4BAA4B;AAAA,IACrC,CAAC;AAAA,IACD,yBAAyB,CAAC;AAAA,MACxB,MAAM;AAAA,MACN,MAAM,CAAC,6BAA6B;AAAA,IACtC,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAWH,IAAM,YAAN,MAAgB;AAAA,EACd,cAAc;AACZ,SAAK,iBAAiB;AAAA,EACxB;AACF;AACA,UAAU,OAAO,SAAS,kBAAkB,mBAAmB;AAC7D,SAAO,KAAK,qBAAqB,WAAW;AAC9C;AACA,UAAU,OAAyB,kBAAkB;AAAA,EACnD,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,gBAAgB,GAAG,CAAC,gBAAgB,CAAC;AAAA,EAClD,UAAU;AAAA,EACV,cAAc,SAAS,uBAAuB,IAAI,KAAK;AACrD,QAAI,KAAK,GAAG;AACV,MAAG,YAAY,gBAAgB,IAAI,cAAc;AAAA,IACnD;AAAA,EACF;AAAA,EACA,YAAY;AAAA,EACZ,oBAAoBD;AAAA,EACpB,OAAO;AAAA,EACP,MAAM;AAAA,EACN,UAAU,SAAS,mBAAmB,IAAI,KAAK;AAC7C,QAAI,KAAK,GAAG;AACV,MAAG,gBAAgB;AACnB,MAAG,aAAa,CAAC;AAAA,IACnB;AAAA,EACF;AAAA,EACA,eAAe;AACjB,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,WAAW,CAAC;AAAA,IAClF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,IAC7B,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAQH,IAAM,iBAAN,MAAqB;AAAA,EACnB,cAAc;AAIZ,SAAK,QAAQ;AAIb,SAAK,aAAa;AAIlB,SAAK,eAAe;AAIpB,SAAK,SAAS;AAId,SAAK,QAAQ;AAKb,SAAK,YAAY;AAIjB,SAAK,eAAe;AAIpB,SAAK,eAAe;AAAA,EACtB;AACF;AACA,eAAe,OAAO,SAAS,uBAAuB,mBAAmB;AACvE,SAAO,KAAK,qBAAqB,gBAAgB;AACnD;AACA,eAAe,OAAyB,kBAAkB;AAAA,EACxD,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,cAAc,CAAC;AAAA,EAC5B,QAAQ;AAAA,IACN,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,WAAW;AAAA,IACX,cAAc;AAAA,IACd,cAAc;AAAA,EAChB;AAAA,EACA,YAAY;AAAA,EACZ,OAAO;AAAA,EACP,MAAM;AAAA,EACN,UAAU,SAAS,wBAAwB,IAAI,KAAK;AAAA,EAAC;AAAA,EACrD,eAAe;AACjB,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAWH,IAAM,aAAN,MAAM,oBAAmB,eAAe;AAAA,EACtC,YAAY,UAAU;AACpB,UAAM;AACN,SAAK,WAAW;AAIhB,SAAK,WAAW,YAAY,YAAW,mBAAmB;AAI1D,SAAK,OAAO;AAIZ,SAAK,OAAO;AAIZ,SAAK,OAAO;AAIZ,SAAK,eAAe;AAIpB,SAAK,WAAW;AAIhB,SAAK,0BAA0B;AAI/B,SAAK,QAAQ,IAAI,aAAa;AAC9B,SAAK,QAAQ,IAAI,aAAa;AAC9B,SAAK,OAAO,IAAI,aAAa;AAI7B,SAAK,eAAe,IAAI,aAAa;AACrC,SAAK,YAAY,CAAC;AAClB,SAAK,gBAAgB,CAAC;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,cAAc,KAAK;AACrB,SAAK,YAAY,OAAO,OAAO;AAAA,MAC7B,sBAAsB,KAAK;AAAA,IAC7B,GAAG,GAAG;AAAA,EACR;AAAA,EACA,IAAI,gBAAgB;AAClB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,iBAAiB,KAAK;AACxB,QAAI,KAAK,QAAQ;AAEf,aAAO,KAAK,KAAK,aAAa,EAAE,QAAQ,SAAO;AAC7C,aAAK,SAAS,gBAAgB,KAAK,OAAO,eAAe,GAAG;AAAA,MAC9D,CAAC;AAED,aAAO,KAAK,GAAG,EAAE,QAAQ,SAAO;AAC9B,aAAK,SAAS,aAAa,KAAK,OAAO,eAAe,KAAK,IAAI,GAAG,CAAC;AAAA,MACrE,CAAC;AAAA,IACH;AAEA,SAAK,gBAAgB;AAAA,EACvB;AAAA,EACA,IAAI,mBAAmB;AACrB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,kBAAkB;AAEhB,SAAK,mBAAmB,KAAK;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe,OAAO,UAAU,UAAU;AACxC,UAAM,eAAe;AACrB,UAAM,gBAAgB;AAEtB,QAAI,YAAY,WAAW;AACzB,WAAK,aAAa,KAAK,KAAK;AAC5B;AAAA,IACF;AACA,SAAK,MAAM,KAAK,KAAK;AAAA,EACvB;AACF;AACA,WAAW,oBAAoB;AAC/B,WAAW,OAAO,SAAS,mBAAmB,mBAAmB;AAC/D,SAAO,KAAK,qBAAqB,YAAe,kBAAqB,SAAS,CAAC;AACjF;AACA,WAAW,OAAyB,kBAAkB;AAAA,EACpD,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,iBAAiB,GAAG,CAAC,iBAAiB,CAAC;AAAA,EACpD,WAAW,SAAS,iBAAiB,IAAI,KAAK;AAC5C,QAAI,KAAK,GAAG;AACV,MAAG,YAAYC,MAAK,CAAC;AAAA,IACvB;AACA,QAAI,KAAK,GAAG;AACV,UAAI;AACJ,MAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,SAAS,GAAG;AAAA,IAC/D;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,eAAe;AAAA,IACf,kBAAkB;AAAA,IAClB,UAAU;AAAA,IACV,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc;AAAA,IACd,UAAU;AAAA,IACV,aAAa;AAAA,IACb,yBAAyB;AAAA,EAC3B;AAAA,EACA,SAAS;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA,EACA,YAAY;AAAA,EACZ,UAAU,CAAI,0BAA0B;AAAA,EACxC,oBAAoBD;AAAA,EACpB,OAAO;AAAA,EACP,MAAM;AAAA,EACN,QAAQ,CAAC,CAAC,UAAU,EAAE,GAAG,CAAC,GAAG,qBAAqB,GAAG,SAAS,eAAe,YAAY,SAAS,cAAc,gBAAgB,UAAU,SAAS,aAAa,gBAAgB,cAAc,GAAG,CAAC,GAAG,SAAS,SAAS,QAAQ,MAAM,YAAY,YAAY,WAAW,aAAa,QAAQ,cAAc,CAAC;AAAA,EAC5S,UAAU,SAAS,oBAAoB,IAAI,KAAK;AAC9C,QAAI,KAAK,GAAG;AACV,YAAM,MAAS,iBAAiB;AAChC,MAAG,gBAAgB;AACnB,MAAG,eAAe,GAAG,eAAe,CAAC;AACrC,MAAG,WAAW,SAAS,SAAS,iDAAiD,QAAQ;AACvF,QAAG,cAAc,GAAG;AACpB,eAAU,YAAY,IAAI,eAAe,QAAQ,SAAS,CAAC;AAAA,MAC7D,CAAC;AACD,MAAG,eAAe,GAAG,UAAU,GAAG,CAAC;AACnC,MAAG,WAAW,SAAS,SAAS,4CAA4C,QAAQ;AAClF,QAAG,cAAc,GAAG;AACpB,eAAU,YAAY,IAAI,eAAe,MAAM,CAAC;AAAA,MAClD,CAAC,EAAE,SAAS,SAAS,4CAA4C,QAAQ;AACvE,QAAG,cAAc,GAAG;AACpB,eAAU,YAAY,IAAI,MAAM,KAAK,MAAM,CAAC;AAAA,MAC9C,CAAC,EAAE,QAAQ,SAAS,2CAA2C,QAAQ;AACrE,QAAG,cAAc,GAAG;AACpB,eAAU,YAAY,IAAI,KAAK,KAAK,MAAM,CAAC;AAAA,MAC7C,CAAC;AACD,MAAG,aAAa,CAAC;AACjB,MAAG,aAAa,EAAE;AAAA,IACpB;AACA,QAAI,KAAK,GAAG;AACV,MAAG,WAAW,eAAe,IAAI,WAAW,EAAE,YAAY,IAAI,0BAA0B,QAAQ,IAAI,QAAQ,EAAE,SAAS,IAAI,KAAK,EAAE,cAAc,IAAI,UAAU,EAAE,gBAAgB,IAAI,YAAY,EAAE,UAAU,IAAI,MAAM,EAAE,SAAS,IAAI,KAAK,EAAE,aAAa,IAAI,SAAS,EAAE,gBAAgB,IAAI,YAAY,EAAE,gBAAgB,IAAI,YAAY;AAC1U,MAAG,UAAU;AACb,MAAG,WAAW,MAAM,IAAI,QAAQ,EAAE,YAAY,IAAI,QAAQ,EAAE,YAAY,IAAI,EAAE,WAAW,IAAI,aAAa,EAAE,aAAa,IAAI,IAAI,EAAE,QAAQ,IAAI,IAAI,EAAE,gBAAgB,IAAI,YAAY;AACrL,MAAG,YAAY,QAAQ,IAAI,IAAI;AAAA,IACjC;AAAA,EACF;AAAA,EACA,cAAc,CAAI,SAAY,SAAS,MAAM;AAAA,EAC7C,eAAe;AACjB,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,YAAY,CAAC;AAAA,IACnF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IA+BZ,CAAC;AAAA,EACH,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAS;AAAA,IACX,CAAC;AAAA,EACH,GAAG;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,yBAAyB,CAAC;AAAA,MACxB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,eAAN,MAAmB;AAAC;AACpB,aAAa,OAAO,SAAS,qBAAqB,mBAAmB;AACnE,SAAO,KAAK,qBAAqB,cAAc;AACjD;AACA,aAAa,OAAyB,iBAAiB;AAAA,EACrD,MAAM;AAAA,EACN,cAAc,CAAC,QAAQ,WAAW,gBAAgB,UAAU;AAAA,EAC5D,SAAS,CAAC,cAAc,aAAa;AAAA,EACrC,SAAS,CAAC,QAAQ,WAAW,UAAU;AACzC,CAAC;AACD,aAAa,OAAyB,iBAAiB;AAAA,EACrD,SAAS,CAAC,cAAc,aAAa;AACvC,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,cAAc,CAAC,QAAQ,WAAW,gBAAgB,UAAU;AAAA,MAC5D,SAAS,CAAC,QAAQ,WAAW,UAAU;AAAA,MACvC,SAAS,CAAC,cAAc,aAAa;AAAA,IACvC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["platform", "max", "offset", "platform", "placements", "sides", "side", "placement", "overflow", "platform", "getComputedStyle", "getComputedStyle", "offset", "flip", "arrow", "computePosition", "open", "offset", "computePosition", "flip", "arrow", "_c0", "_c1", "_c0", "_c1", "_c0", "_c1"]}