import { Component, signal, inject, OnInit } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { AuthService } from '../services/auth.service';
import { FirebaseTestService } from '../services/firebase-test.service';

// Carbon Design System imports
import { InputModule } from 'carbon-components-angular/input';
import { ButtonModule } from 'carbon-components-angular/button';

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    InputModule,
    ButtonModule
  ],
  templateUrl: './login.component.html',
  styleUrl: './login.component.scss'
})
export class LoginComponent implements OnInit {
  private formBuilder = inject(FormBuilder);
  private auth = inject(AuthService);
  private firebaseTest = inject(FirebaseTestService);

  protected readonly loginForm: FormGroup;
  protected readonly isSubmitting = signal(false);
  protected readonly error = signal('');

  constructor() {
    this.loginForm = this.formBuilder.group({
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required]]
    });
  }

  ngOnInit() {
    // Test Firebase connection when component initializes
    this.firebaseTest.testFirebaseConnection();
  }



  protected async onSubmit(): Promise<void> {
    if (this.loginForm.invalid) {
      this.loginForm.markAllAsTouched();
      return;
    }

    this.isSubmitting.set(true);
    this.error.set('');

    try {
      const { email, password } = this.loginForm.value;
      console.log('Form values:', { email, password: password ? '[REDACTED]' : 'empty' });

      await this.auth.signIn(email!, password!);
      // No manual navigation needed—redirect guard on /login will push to /projects
    } catch (e: any) {
      console.error('Login component error:', e);

      // Provide more user-friendly error messages
      let errorMessage = 'Sign-in failed';

      if (e?.code) {
        switch (e.code) {
          case 'auth/user-not-found':
            errorMessage = 'No account found with this email address';
            break;
          case 'auth/wrong-password':
            errorMessage = 'Incorrect password';
            break;
          case 'auth/invalid-email':
            errorMessage = 'Invalid email address';
            break;
          case 'auth/user-disabled':
            errorMessage = 'This account has been disabled';
            break;
          case 'auth/too-many-requests':
            errorMessage = 'Too many failed attempts. Please try again later';
            break;
          case 'auth/network-request-failed':
            errorMessage = 'Network error. Please check your connection';
            break;
          default:
            errorMessage = `Authentication error: ${e.code}`;
        }
      } else if (e?.message) {
        errorMessage = e.message;
      }

      this.error.set(errorMessage);
    } finally {
      this.isSubmitting.set(false);
    }
  }

  protected getEmailErrorMessage(): string {
    const emailControl = this.loginForm.get('email');
    if (emailControl?.hasError('required')) {
      return 'Email is required';
    }
    if (emailControl?.hasError('email')) {
      return 'Please enter a valid email address';
    }
    return '';
  }

  protected getPasswordErrorMessage(): string {
    const passwordControl = this.loginForm.get('password');
    if (passwordControl?.hasError('required')) {
      return 'Password is required';
    }
    return '';
  }

  protected isEmailInvalid(): boolean {
    const emailControl = this.loginForm.get('email');
    return !!(emailControl?.invalid && (emailControl?.dirty || emailControl?.touched));
  }

  protected isPasswordInvalid(): boolean {
    const passwordControl = this.loginForm.get('password');
    return !!(passwordControl?.invalid && (passwordControl?.dirty || passwordControl?.touched));
  }
}
