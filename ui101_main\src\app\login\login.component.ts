import { Component, signal, inject } from '@angular/core';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { AuthService } from '../services/auth.service';

// Carbon Design System imports
import { InputModule } from 'carbon-components-angular/input';
import { ButtonModule } from 'carbon-components-angular/button';

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    InputModule,
    ButtonModule
  ],
  templateUrl: './login.component.html',
  styleUrl: './login.component.scss'
})
export class LoginComponent {
  private formBuilder = inject(FormBuilder);
  private auth = inject(AuthService);

  protected readonly loginForm: FormGroup;
  protected readonly isSubmitting = signal(false);
  protected readonly error = signal('');

  constructor() {
    this.loginForm = this.formBuilder.group({
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required]]
    });
  }



  protected async onSubmit(): Promise<void> {
    if (this.loginForm.invalid) {
      this.loginForm.markAllAsTouched();
      return;
    }

    this.isSubmitting.set(true);
    this.error.set('');

    try {
      const { email, password } = this.loginForm.value;
      await this.auth.signIn(email!, password!);
      // No manual navigation needed—redirect guard on /login will push to /projects
    } catch (e: any) {
      this.error.set((e?.code ?? e?.message) || 'Sign-in failed');
    } finally {
      this.isSubmitting.set(false);
    }
  }

  protected getEmailErrorMessage(): string {
    const emailControl = this.loginForm.get('email');
    if (emailControl?.hasError('required')) {
      return 'Email is required';
    }
    if (emailControl?.hasError('email')) {
      return 'Please enter a valid email address';
    }
    return '';
  }

  protected getPasswordErrorMessage(): string {
    const passwordControl = this.loginForm.get('password');
    if (passwordControl?.hasError('required')) {
      return 'Password is required';
    }
    return '';
  }

  protected isEmailInvalid(): boolean {
    const emailControl = this.loginForm.get('email');
    return !!(emailControl?.invalid && (emailControl?.dirty || emailControl?.touched));
  }

  protected isPasswordInvalid(): boolean {
    const passwordControl = this.loginForm.get('password');
    return !!(passwordControl?.invalid && (passwordControl?.dirty || passwordControl?.touched));
  }
}
