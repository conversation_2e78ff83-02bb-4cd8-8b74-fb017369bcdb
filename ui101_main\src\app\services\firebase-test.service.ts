import { Injectable, inject } from '@angular/core';
import { Auth } from '@angular/fire/auth';

@Injectable({ providedIn: 'root' })
export class FirebaseTestService {
  private auth = inject(Auth);

  testFirebaseConnection() {
    console.log('=== Firebase Connection Test ===');
    console.log('Auth instance:', this.auth);
    console.log('Auth app:', this.auth.app);
    console.log('Auth config:', this.auth.config);
    console.log('Auth current user:', this.auth.currentUser);
    
    // Test if Firebase is properly initialized
    try {
      console.log('Firebase app name:', this.auth.app.name);
      console.log('Firebase app options:', this.auth.app.options);
      console.log('✅ Firebase appears to be properly initialized');
    } catch (error) {
      console.error('❌ Firebase initialization error:', error);
    }
  }
}
