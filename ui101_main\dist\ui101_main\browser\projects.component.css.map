{"version": 3, "sources": ["src/app/projects/projects.component.ts"], "sourcesContent": ["\n    .projects-container {\n      padding: 2rem;\n      max-width: 1200px;\n      margin: 0 auto;\n    }\n    \n    .projects-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: 2rem;\n      padding-bottom: 1rem;\n      border-bottom: 1px solid #e0e0e0;\n    }\n    \n    .projects-header h1 {\n      margin: 0;\n      font-size: 2rem;\n      font-weight: 400;\n    }\n    \n    .projects-content {\n      padding: 1rem 0;\n    }\n  "], "mappings": ";AACI,CAAA;AACE,WAAA;AACA,aAAA;AACA,UAAA,EAAA;;AAGF,CAAA;AACE,WAAA;AACA,mBAAA;AACA,eAAA;AACA,iBAAA;AACA,kBAAA;AACA,iBAAA,IAAA,MAAA;;AAGF,CATA,gBASA;AACE,UAAA;AACA,aAAA;AACA,eAAA;;AAGF,CAAA;AACE,WAAA,KAAA;;", "names": []}