import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AuthService } from '../services/auth.service';

// Carbon Design System imports
import { ButtonModule } from 'carbon-components-angular/button';

@Component({
  selector: 'app-projects',
  standalone: true,
  imports: [
    CommonModule,
    ButtonModule
  ],
  template: `
    <div class="projects-container">
      <header class="projects-header">
        <h1>Projects Dashboard</h1>
        <button 
          ibmButton="primary" 
          size="md"
          (click)="signOut()">
          Sign Out
        </button>
      </header>
      
      <main class="projects-content">
        <p>Welcome to your projects dashboard!</p>
        <p *ngIf="auth.user$ | async as user">
          Logged in as: {{ user.email }}
        </p>
      </main>
    </div>
  `,
  styles: [`
    .projects-container {
      padding: 2rem;
      max-width: 1200px;
      margin: 0 auto;
    }
    
    .projects-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 2rem;
      padding-bottom: 1rem;
      border-bottom: 1px solid #e0e0e0;
    }
    
    .projects-header h1 {
      margin: 0;
      font-size: 2rem;
      font-weight: 400;
    }
    
    .projects-content {
      padding: 1rem 0;
    }
  `]
})
export class ProjectsComponent {
  protected auth = inject(AuthService);

  async signOut() {
    try {
      await this.auth.signOut();
    } catch (error) {
      console.error('Sign out error:', error);
    }
  }
}
