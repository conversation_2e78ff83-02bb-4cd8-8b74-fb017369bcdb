{"name": "ui101-main", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "serve:ssr:ui101_main": "node dist/ui101_main/server/server.mjs"}, "prettier": {"printWidth": 100, "singleQuote": true, "overrides": [{"files": "*.html", "options": {"parser": "angular"}}]}, "private": true, "dependencies": {"@angular/common": "^20.2.0", "@angular/compiler": "^20.2.0", "@angular/core": "^20.2.0", "@angular/forms": "^20.2.0", "@angular/platform-browser": "^20.2.0", "@angular/platform-server": "^20.2.0", "@angular/router": "^20.2.0", "@angular/ssr": "^20.2.1", "@carbon/icons": "^11.66.0", "@carbon/styles": "^1.89.0", "carbon-components-angular": "^5.59.2", "express": "^5.1.0", "firebase": "^12.2.1", "rxjs": "~7.8.0", "tslib": "^2.3.0"}, "devDependencies": {"@angular/build": "^20.2.1", "@angular/cli": "^20.2.1", "@angular/compiler-cli": "^20.2.0", "@types/express": "^5.0.1", "@types/jasmine": "~5.1.0", "@types/node": "^20.17.19", "jasmine-core": "~5.9.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.9.2"}}