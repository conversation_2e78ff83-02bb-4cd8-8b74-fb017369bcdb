import { Injectable, inject } from '@angular/core';
import { Auth, authState, signInWithEmailAndPassword, signOut } from '@angular/fire/auth';
import { map } from 'rxjs/operators';

@Injectable({ providedIn: 'root' })
export class AuthService {
  private auth = inject(Auth);
  user$ = authState(this.auth);
  uid$ = this.user$.pipe(map(u => u?.uid ?? null));
  
  signIn(email: string, password: string) {
    return signInWithEmailAndPassword(this.auth, email.trim(), password);
  }
  
  signOut() { 
    return signOut(this.auth); 
  }
}
