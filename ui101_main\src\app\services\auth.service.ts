import { Injectable, inject } from '@angular/core';
import { Auth, authState, signInWithEmailAndPassword, signOut } from '@angular/fire/auth';
import { map } from 'rxjs/operators';

@Injectable({ providedIn: 'root' })
export class AuthService {
  private auth = inject(Auth);
  user$ = authState(this.auth);
  uid$ = this.user$.pipe(map(u => u?.uid ?? null));

  async signIn(email: string, password: string) {
    try {
      console.log('Attempting to sign in with email:', email);
      console.log('Firebase Auth instance:', this.auth);
      console.log('Firebase config:', this.auth.config);

      const result = await signInWithEmailAndPassword(this.auth, email.trim(), password);
      console.log('Sign in successful:', result);
      return result;
    } catch (error: any) {
      console.error('Sign in error details:', error);
      console.error('Error code:', error.code);
      console.error('Error message:', error.message);
      throw error;
    }
  }

  signOut() {
    return signOut(this.auth);
  }
}
