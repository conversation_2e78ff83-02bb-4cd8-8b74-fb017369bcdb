import {
  Auth,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  CommonModule,
  Component,
  Directive,
  ElementRef,
  EventEmitter,
  HostBinding,
  HostListener,
  Injectable,
  Input,
  NgClass,
  NgIf,
  NgModule,
  NgTemplateOutlet,
  NgZone,
  Output,
  Renderer2,
  TemplateRef,
  ViewChild,
  __objRest,
  __spreadProps,
  __spreadValues,
  authState,
  inject,
  map,
  setClassMetadata,
  signInWithEmailAndPassword,
  signOut,
  ɵɵInheritDefinitionFeature,
  ɵɵNgOnChangesFeature,
  ɵɵadvance,
  ɵɵattribute,
  ɵɵclassProp,
  ɵɵdefineComponent,
  ɵɵdefineDirective,
  ɵɵdefineInjectable,
  ɵɵdefineInjector,
  ɵɵdefineNgModule,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementContainerEnd,
  ɵɵelementContainerStart,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵlistener,
  ɵɵloadQuery,
  ɵɵnextContext,
  ɵɵprojection,
  ɵɵprojectionDef,
  ɵɵproperty,
  ɵɵpureFunction1,
  ɵɵqueryRefresh,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵviewQuery
} from "./chunk-BGMPDIEO.js";

// src/app/services/auth.service.ts
var AuthService = class _AuthService {
  auth = inject(Auth);
  user$ = authState(this.auth);
  uid$ = this.user$.pipe(map((u) => u?.uid ?? null));
  async signIn(email, password) {
    try {
      console.log("Attempting to sign in with email:", email);
      console.log("Firebase Auth instance:", this.auth);
      console.log("Firebase config:", this.auth.config);
      const result = await signInWithEmailAndPassword(this.auth, email.trim(), password);
      console.log("Sign in successful:", result);
      return result;
    } catch (error) {
      console.error("Sign in error details:", error);
      console.error("Error code:", error.code);
      console.error("Error message:", error.message);
      throw error;
    }
  }
  signOut() {
    return signOut(this.auth);
  }
  static \u0275fac = function AuthService_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _AuthService)();
  };
  static \u0275prov = /* @__PURE__ */ \u0275\u0275defineInjectable({ token: _AuthService, factory: _AuthService.\u0275fac, providedIn: "root" });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(AuthService, [{
    type: Injectable,
    args: [{ providedIn: "root" }]
  }], null, null);
})();

// node_modules/@floating-ui/utils/dist/floating-ui.utils.mjs
var min = Math.min;
var max = Math.max;
var round = Math.round;
var floor = Math.floor;
var createCoords = (v) => ({
  x: v,
  y: v
});
var oppositeSideMap = {
  left: "right",
  right: "left",
  bottom: "top",
  top: "bottom"
};
var oppositeAlignmentMap = {
  start: "end",
  end: "start"
};
function clamp(start, value, end) {
  return max(start, min(value, end));
}
function evaluate(value, param) {
  return typeof value === "function" ? value(param) : value;
}
function getSide(placement) {
  return placement.split("-")[0];
}
function getAlignment(placement) {
  return placement.split("-")[1];
}
function getOppositeAxis(axis) {
  return axis === "x" ? "y" : "x";
}
function getAxisLength(axis) {
  return axis === "y" ? "height" : "width";
}
var yAxisSides = /* @__PURE__ */ new Set(["top", "bottom"]);
function getSideAxis(placement) {
  return yAxisSides.has(getSide(placement)) ? "y" : "x";
}
function getAlignmentAxis(placement) {
  return getOppositeAxis(getSideAxis(placement));
}
function getAlignmentSides(placement, rects, rtl) {
  if (rtl === void 0) {
    rtl = false;
  }
  const alignment = getAlignment(placement);
  const alignmentAxis = getAlignmentAxis(placement);
  const length = getAxisLength(alignmentAxis);
  let mainAlignmentSide = alignmentAxis === "x" ? alignment === (rtl ? "end" : "start") ? "right" : "left" : alignment === "start" ? "bottom" : "top";
  if (rects.reference[length] > rects.floating[length]) {
    mainAlignmentSide = getOppositePlacement(mainAlignmentSide);
  }
  return [mainAlignmentSide, getOppositePlacement(mainAlignmentSide)];
}
function getExpandedPlacements(placement) {
  const oppositePlacement = getOppositePlacement(placement);
  return [getOppositeAlignmentPlacement(placement), oppositePlacement, getOppositeAlignmentPlacement(oppositePlacement)];
}
function getOppositeAlignmentPlacement(placement) {
  return placement.replace(/start|end/g, (alignment) => oppositeAlignmentMap[alignment]);
}
var lrPlacement = ["left", "right"];
var rlPlacement = ["right", "left"];
var tbPlacement = ["top", "bottom"];
var btPlacement = ["bottom", "top"];
function getSideList(side, isStart, rtl) {
  switch (side) {
    case "top":
    case "bottom":
      if (rtl) return isStart ? rlPlacement : lrPlacement;
      return isStart ? lrPlacement : rlPlacement;
    case "left":
    case "right":
      return isStart ? tbPlacement : btPlacement;
    default:
      return [];
  }
}
function getOppositeAxisPlacements(placement, flipAlignment, direction, rtl) {
  const alignment = getAlignment(placement);
  let list = getSideList(getSide(placement), direction === "start", rtl);
  if (alignment) {
    list = list.map((side) => side + "-" + alignment);
    if (flipAlignment) {
      list = list.concat(list.map(getOppositeAlignmentPlacement));
    }
  }
  return list;
}
function getOppositePlacement(placement) {
  return placement.replace(/left|right|bottom|top/g, (side) => oppositeSideMap[side]);
}
function expandPaddingObject(padding) {
  return __spreadValues({
    top: 0,
    right: 0,
    bottom: 0,
    left: 0
  }, padding);
}
function getPaddingObject(padding) {
  return typeof padding !== "number" ? expandPaddingObject(padding) : {
    top: padding,
    right: padding,
    bottom: padding,
    left: padding
  };
}
function rectToClientRect(rect) {
  const {
    x,
    y,
    width,
    height
  } = rect;
  return {
    width,
    height,
    top: y,
    left: x,
    right: x + width,
    bottom: y + height,
    x,
    y
  };
}

// node_modules/@floating-ui/core/dist/floating-ui.core.mjs
function computeCoordsFromPlacement(_ref, placement, rtl) {
  let {
    reference,
    floating
  } = _ref;
  const sideAxis = getSideAxis(placement);
  const alignmentAxis = getAlignmentAxis(placement);
  const alignLength = getAxisLength(alignmentAxis);
  const side = getSide(placement);
  const isVertical = sideAxis === "y";
  const commonX = reference.x + reference.width / 2 - floating.width / 2;
  const commonY = reference.y + reference.height / 2 - floating.height / 2;
  const commonAlign = reference[alignLength] / 2 - floating[alignLength] / 2;
  let coords;
  switch (side) {
    case "top":
      coords = {
        x: commonX,
        y: reference.y - floating.height
      };
      break;
    case "bottom":
      coords = {
        x: commonX,
        y: reference.y + reference.height
      };
      break;
    case "right":
      coords = {
        x: reference.x + reference.width,
        y: commonY
      };
      break;
    case "left":
      coords = {
        x: reference.x - floating.width,
        y: commonY
      };
      break;
    default:
      coords = {
        x: reference.x,
        y: reference.y
      };
  }
  switch (getAlignment(placement)) {
    case "start":
      coords[alignmentAxis] -= commonAlign * (rtl && isVertical ? -1 : 1);
      break;
    case "end":
      coords[alignmentAxis] += commonAlign * (rtl && isVertical ? -1 : 1);
      break;
  }
  return coords;
}
var computePosition = async (reference, floating, config) => {
  const {
    placement = "bottom",
    strategy = "absolute",
    middleware = [],
    platform: platform2
  } = config;
  const validMiddleware = middleware.filter(Boolean);
  const rtl = await (platform2.isRTL == null ? void 0 : platform2.isRTL(floating));
  let rects = await platform2.getElementRects({
    reference,
    floating,
    strategy
  });
  let {
    x,
    y
  } = computeCoordsFromPlacement(rects, placement, rtl);
  let statefulPlacement = placement;
  let middlewareData = {};
  let resetCount = 0;
  for (let i = 0; i < validMiddleware.length; i++) {
    const {
      name,
      fn
    } = validMiddleware[i];
    const {
      x: nextX,
      y: nextY,
      data,
      reset
    } = await fn({
      x,
      y,
      initialPlacement: placement,
      placement: statefulPlacement,
      strategy,
      middlewareData,
      rects,
      platform: platform2,
      elements: {
        reference,
        floating
      }
    });
    x = nextX != null ? nextX : x;
    y = nextY != null ? nextY : y;
    middlewareData = __spreadProps(__spreadValues({}, middlewareData), {
      [name]: __spreadValues(__spreadValues({}, middlewareData[name]), data)
    });
    if (reset && resetCount <= 50) {
      resetCount++;
      if (typeof reset === "object") {
        if (reset.placement) {
          statefulPlacement = reset.placement;
        }
        if (reset.rects) {
          rects = reset.rects === true ? await platform2.getElementRects({
            reference,
            floating,
            strategy
          }) : reset.rects;
        }
        ({
          x,
          y
        } = computeCoordsFromPlacement(rects, statefulPlacement, rtl));
      }
      i = -1;
    }
  }
  return {
    x,
    y,
    placement: statefulPlacement,
    strategy,
    middlewareData
  };
};
async function detectOverflow(state, options) {
  var _await$platform$isEle;
  if (options === void 0) {
    options = {};
  }
  const {
    x,
    y,
    platform: platform2,
    rects,
    elements,
    strategy
  } = state;
  const {
    boundary = "clippingAncestors",
    rootBoundary = "viewport",
    elementContext = "floating",
    altBoundary = false,
    padding = 0
  } = evaluate(options, state);
  const paddingObject = getPaddingObject(padding);
  const altContext = elementContext === "floating" ? "reference" : "floating";
  const element = elements[altBoundary ? altContext : elementContext];
  const clippingClientRect = rectToClientRect(await platform2.getClippingRect({
    element: ((_await$platform$isEle = await (platform2.isElement == null ? void 0 : platform2.isElement(element))) != null ? _await$platform$isEle : true) ? element : element.contextElement || await (platform2.getDocumentElement == null ? void 0 : platform2.getDocumentElement(elements.floating)),
    boundary,
    rootBoundary,
    strategy
  }));
  const rect = elementContext === "floating" ? {
    x,
    y,
    width: rects.floating.width,
    height: rects.floating.height
  } : rects.reference;
  const offsetParent = await (platform2.getOffsetParent == null ? void 0 : platform2.getOffsetParent(elements.floating));
  const offsetScale = await (platform2.isElement == null ? void 0 : platform2.isElement(offsetParent)) ? await (platform2.getScale == null ? void 0 : platform2.getScale(offsetParent)) || {
    x: 1,
    y: 1
  } : {
    x: 1,
    y: 1
  };
  const elementClientRect = rectToClientRect(platform2.convertOffsetParentRelativeRectToViewportRelativeRect ? await platform2.convertOffsetParentRelativeRectToViewportRelativeRect({
    elements,
    rect,
    offsetParent,
    strategy
  }) : rect);
  return {
    top: (clippingClientRect.top - elementClientRect.top + paddingObject.top) / offsetScale.y,
    bottom: (elementClientRect.bottom - clippingClientRect.bottom + paddingObject.bottom) / offsetScale.y,
    left: (clippingClientRect.left - elementClientRect.left + paddingObject.left) / offsetScale.x,
    right: (elementClientRect.right - clippingClientRect.right + paddingObject.right) / offsetScale.x
  };
}
var arrow = (options) => ({
  name: "arrow",
  options,
  async fn(state) {
    const {
      x,
      y,
      placement,
      rects,
      platform: platform2,
      elements,
      middlewareData
    } = state;
    const {
      element,
      padding = 0
    } = evaluate(options, state) || {};
    if (element == null) {
      return {};
    }
    const paddingObject = getPaddingObject(padding);
    const coords = {
      x,
      y
    };
    const axis = getAlignmentAxis(placement);
    const length = getAxisLength(axis);
    const arrowDimensions = await platform2.getDimensions(element);
    const isYAxis = axis === "y";
    const minProp = isYAxis ? "top" : "left";
    const maxProp = isYAxis ? "bottom" : "right";
    const clientProp = isYAxis ? "clientHeight" : "clientWidth";
    const endDiff = rects.reference[length] + rects.reference[axis] - coords[axis] - rects.floating[length];
    const startDiff = coords[axis] - rects.reference[axis];
    const arrowOffsetParent = await (platform2.getOffsetParent == null ? void 0 : platform2.getOffsetParent(element));
    let clientSize = arrowOffsetParent ? arrowOffsetParent[clientProp] : 0;
    if (!clientSize || !await (platform2.isElement == null ? void 0 : platform2.isElement(arrowOffsetParent))) {
      clientSize = elements.floating[clientProp] || rects.floating[length];
    }
    const centerToReference = endDiff / 2 - startDiff / 2;
    const largestPossiblePadding = clientSize / 2 - arrowDimensions[length] / 2 - 1;
    const minPadding = min(paddingObject[minProp], largestPossiblePadding);
    const maxPadding = min(paddingObject[maxProp], largestPossiblePadding);
    const min$1 = minPadding;
    const max2 = clientSize - arrowDimensions[length] - maxPadding;
    const center = clientSize / 2 - arrowDimensions[length] / 2 + centerToReference;
    const offset3 = clamp(min$1, center, max2);
    const shouldAddOffset = !middlewareData.arrow && getAlignment(placement) != null && center !== offset3 && rects.reference[length] / 2 - (center < min$1 ? minPadding : maxPadding) - arrowDimensions[length] / 2 < 0;
    const alignmentOffset = shouldAddOffset ? center < min$1 ? center - min$1 : center - max2 : 0;
    return {
      [axis]: coords[axis] + alignmentOffset,
      data: __spreadValues({
        [axis]: offset3,
        centerOffset: center - offset3 - alignmentOffset
      }, shouldAddOffset && {
        alignmentOffset
      }),
      reset: shouldAddOffset
    };
  }
});
var flip = function(options) {
  if (options === void 0) {
    options = {};
  }
  return {
    name: "flip",
    options,
    async fn(state) {
      var _middlewareData$arrow, _middlewareData$flip;
      const {
        placement,
        middlewareData,
        rects,
        initialPlacement,
        platform: platform2,
        elements
      } = state;
      const _a = evaluate(options, state), {
        mainAxis: checkMainAxis = true,
        crossAxis: checkCrossAxis = true,
        fallbackPlacements: specifiedFallbackPlacements,
        fallbackStrategy = "bestFit",
        fallbackAxisSideDirection = "none",
        flipAlignment = true
      } = _a, detectOverflowOptions = __objRest(_a, [
        "mainAxis",
        "crossAxis",
        "fallbackPlacements",
        "fallbackStrategy",
        "fallbackAxisSideDirection",
        "flipAlignment"
      ]);
      if ((_middlewareData$arrow = middlewareData.arrow) != null && _middlewareData$arrow.alignmentOffset) {
        return {};
      }
      const side = getSide(placement);
      const initialSideAxis = getSideAxis(initialPlacement);
      const isBasePlacement = getSide(initialPlacement) === initialPlacement;
      const rtl = await (platform2.isRTL == null ? void 0 : platform2.isRTL(elements.floating));
      const fallbackPlacements = specifiedFallbackPlacements || (isBasePlacement || !flipAlignment ? [getOppositePlacement(initialPlacement)] : getExpandedPlacements(initialPlacement));
      const hasFallbackAxisSideDirection = fallbackAxisSideDirection !== "none";
      if (!specifiedFallbackPlacements && hasFallbackAxisSideDirection) {
        fallbackPlacements.push(...getOppositeAxisPlacements(initialPlacement, flipAlignment, fallbackAxisSideDirection, rtl));
      }
      const placements2 = [initialPlacement, ...fallbackPlacements];
      const overflow = await detectOverflow(state, detectOverflowOptions);
      const overflows = [];
      let overflowsData = ((_middlewareData$flip = middlewareData.flip) == null ? void 0 : _middlewareData$flip.overflows) || [];
      if (checkMainAxis) {
        overflows.push(overflow[side]);
      }
      if (checkCrossAxis) {
        const sides2 = getAlignmentSides(placement, rects, rtl);
        overflows.push(overflow[sides2[0]], overflow[sides2[1]]);
      }
      overflowsData = [...overflowsData, {
        placement,
        overflows
      }];
      if (!overflows.every((side2) => side2 <= 0)) {
        var _middlewareData$flip2, _overflowsData$filter;
        const nextIndex = (((_middlewareData$flip2 = middlewareData.flip) == null ? void 0 : _middlewareData$flip2.index) || 0) + 1;
        const nextPlacement = placements2[nextIndex];
        if (nextPlacement) {
          const ignoreCrossAxisOverflow = checkCrossAxis === "alignment" ? initialSideAxis !== getSideAxis(nextPlacement) : false;
          if (!ignoreCrossAxisOverflow || // We leave the current main axis only if every placement on that axis
          // overflows the main axis.
          overflowsData.every((d) => getSideAxis(d.placement) === initialSideAxis ? d.overflows[0] > 0 : true)) {
            return {
              data: {
                index: nextIndex,
                overflows: overflowsData
              },
              reset: {
                placement: nextPlacement
              }
            };
          }
        }
        let resetPlacement = (_overflowsData$filter = overflowsData.filter((d) => d.overflows[0] <= 0).sort((a, b) => a.overflows[1] - b.overflows[1])[0]) == null ? void 0 : _overflowsData$filter.placement;
        if (!resetPlacement) {
          switch (fallbackStrategy) {
            case "bestFit": {
              var _overflowsData$filter2;
              const placement2 = (_overflowsData$filter2 = overflowsData.filter((d) => {
                if (hasFallbackAxisSideDirection) {
                  const currentSideAxis = getSideAxis(d.placement);
                  return currentSideAxis === initialSideAxis || // Create a bias to the `y` side axis due to horizontal
                  // reading directions favoring greater width.
                  currentSideAxis === "y";
                }
                return true;
              }).map((d) => [d.placement, d.overflows.filter((overflow2) => overflow2 > 0).reduce((acc, overflow2) => acc + overflow2, 0)]).sort((a, b) => a[1] - b[1])[0]) == null ? void 0 : _overflowsData$filter2[0];
              if (placement2) {
                resetPlacement = placement2;
              }
              break;
            }
            case "initialPlacement":
              resetPlacement = initialPlacement;
              break;
          }
        }
        if (placement !== resetPlacement) {
          return {
            reset: {
              placement: resetPlacement
            }
          };
        }
      }
      return {};
    }
  };
};
var originSides = /* @__PURE__ */ new Set(["left", "top"]);
async function convertValueToCoords(state, options) {
  const {
    placement,
    platform: platform2,
    elements
  } = state;
  const rtl = await (platform2.isRTL == null ? void 0 : platform2.isRTL(elements.floating));
  const side = getSide(placement);
  const alignment = getAlignment(placement);
  const isVertical = getSideAxis(placement) === "y";
  const mainAxisMulti = originSides.has(side) ? -1 : 1;
  const crossAxisMulti = rtl && isVertical ? -1 : 1;
  const rawValue = evaluate(options, state);
  let {
    mainAxis,
    crossAxis,
    alignmentAxis
  } = typeof rawValue === "number" ? {
    mainAxis: rawValue,
    crossAxis: 0,
    alignmentAxis: null
  } : {
    mainAxis: rawValue.mainAxis || 0,
    crossAxis: rawValue.crossAxis || 0,
    alignmentAxis: rawValue.alignmentAxis
  };
  if (alignment && typeof alignmentAxis === "number") {
    crossAxis = alignment === "end" ? alignmentAxis * -1 : alignmentAxis;
  }
  return isVertical ? {
    x: crossAxis * crossAxisMulti,
    y: mainAxis * mainAxisMulti
  } : {
    x: mainAxis * mainAxisMulti,
    y: crossAxis * crossAxisMulti
  };
}
var offset = function(options) {
  if (options === void 0) {
    options = 0;
  }
  return {
    name: "offset",
    options,
    async fn(state) {
      var _middlewareData$offse, _middlewareData$arrow;
      const {
        x,
        y,
        placement,
        middlewareData
      } = state;
      const diffCoords = await convertValueToCoords(state, options);
      if (placement === ((_middlewareData$offse = middlewareData.offset) == null ? void 0 : _middlewareData$offse.placement) && (_middlewareData$arrow = middlewareData.arrow) != null && _middlewareData$arrow.alignmentOffset) {
        return {};
      }
      return {
        x: x + diffCoords.x,
        y: y + diffCoords.y,
        data: __spreadProps(__spreadValues({}, diffCoords), {
          placement
        })
      };
    }
  };
};

// node_modules/@floating-ui/utils/dist/floating-ui.utils.dom.mjs
function hasWindow() {
  return typeof window !== "undefined";
}
function getNodeName(node) {
  if (isNode(node)) {
    return (node.nodeName || "").toLowerCase();
  }
  return "#document";
}
function getWindow(node) {
  var _node$ownerDocument;
  return (node == null || (_node$ownerDocument = node.ownerDocument) == null ? void 0 : _node$ownerDocument.defaultView) || window;
}
function getDocumentElement(node) {
  var _ref;
  return (_ref = (isNode(node) ? node.ownerDocument : node.document) || window.document) == null ? void 0 : _ref.documentElement;
}
function isNode(value) {
  if (!hasWindow()) {
    return false;
  }
  return value instanceof Node || value instanceof getWindow(value).Node;
}
function isElement(value) {
  if (!hasWindow()) {
    return false;
  }
  return value instanceof Element || value instanceof getWindow(value).Element;
}
function isHTMLElement(value) {
  if (!hasWindow()) {
    return false;
  }
  return value instanceof HTMLElement || value instanceof getWindow(value).HTMLElement;
}
function isShadowRoot(value) {
  if (!hasWindow() || typeof ShadowRoot === "undefined") {
    return false;
  }
  return value instanceof ShadowRoot || value instanceof getWindow(value).ShadowRoot;
}
var invalidOverflowDisplayValues = /* @__PURE__ */ new Set(["inline", "contents"]);
function isOverflowElement(element) {
  const {
    overflow,
    overflowX,
    overflowY,
    display
  } = getComputedStyle2(element);
  return /auto|scroll|overlay|hidden|clip/.test(overflow + overflowY + overflowX) && !invalidOverflowDisplayValues.has(display);
}
var tableElements = /* @__PURE__ */ new Set(["table", "td", "th"]);
function isTableElement(element) {
  return tableElements.has(getNodeName(element));
}
var topLayerSelectors = [":popover-open", ":modal"];
function isTopLayer(element) {
  return topLayerSelectors.some((selector) => {
    try {
      return element.matches(selector);
    } catch (_e) {
      return false;
    }
  });
}
var transformProperties = ["transform", "translate", "scale", "rotate", "perspective"];
var willChangeValues = ["transform", "translate", "scale", "rotate", "perspective", "filter"];
var containValues = ["paint", "layout", "strict", "content"];
function isContainingBlock(elementOrCss) {
  const webkit = isWebKit();
  const css = isElement(elementOrCss) ? getComputedStyle2(elementOrCss) : elementOrCss;
  return transformProperties.some((value) => css[value] ? css[value] !== "none" : false) || (css.containerType ? css.containerType !== "normal" : false) || !webkit && (css.backdropFilter ? css.backdropFilter !== "none" : false) || !webkit && (css.filter ? css.filter !== "none" : false) || willChangeValues.some((value) => (css.willChange || "").includes(value)) || containValues.some((value) => (css.contain || "").includes(value));
}
function getContainingBlock(element) {
  let currentNode = getParentNode(element);
  while (isHTMLElement(currentNode) && !isLastTraversableNode(currentNode)) {
    if (isContainingBlock(currentNode)) {
      return currentNode;
    } else if (isTopLayer(currentNode)) {
      return null;
    }
    currentNode = getParentNode(currentNode);
  }
  return null;
}
function isWebKit() {
  if (typeof CSS === "undefined" || !CSS.supports) return false;
  return CSS.supports("-webkit-backdrop-filter", "none");
}
var lastTraversableNodeNames = /* @__PURE__ */ new Set(["html", "body", "#document"]);
function isLastTraversableNode(node) {
  return lastTraversableNodeNames.has(getNodeName(node));
}
function getComputedStyle2(element) {
  return getWindow(element).getComputedStyle(element);
}
function getNodeScroll(element) {
  if (isElement(element)) {
    return {
      scrollLeft: element.scrollLeft,
      scrollTop: element.scrollTop
    };
  }
  return {
    scrollLeft: element.scrollX,
    scrollTop: element.scrollY
  };
}
function getParentNode(node) {
  if (getNodeName(node) === "html") {
    return node;
  }
  const result = (
    // Step into the shadow DOM of the parent of a slotted node.
    node.assignedSlot || // DOM Element detected.
    node.parentNode || // ShadowRoot detected.
    isShadowRoot(node) && node.host || // Fallback.
    getDocumentElement(node)
  );
  return isShadowRoot(result) ? result.host : result;
}
function getNearestOverflowAncestor(node) {
  const parentNode = getParentNode(node);
  if (isLastTraversableNode(parentNode)) {
    return node.ownerDocument ? node.ownerDocument.body : node.body;
  }
  if (isHTMLElement(parentNode) && isOverflowElement(parentNode)) {
    return parentNode;
  }
  return getNearestOverflowAncestor(parentNode);
}
function getOverflowAncestors(node, list, traverseIframes) {
  var _node$ownerDocument2;
  if (list === void 0) {
    list = [];
  }
  if (traverseIframes === void 0) {
    traverseIframes = true;
  }
  const scrollableAncestor = getNearestOverflowAncestor(node);
  const isBody = scrollableAncestor === ((_node$ownerDocument2 = node.ownerDocument) == null ? void 0 : _node$ownerDocument2.body);
  const win = getWindow(scrollableAncestor);
  if (isBody) {
    const frameElement = getFrameElement(win);
    return list.concat(win, win.visualViewport || [], isOverflowElement(scrollableAncestor) ? scrollableAncestor : [], frameElement && traverseIframes ? getOverflowAncestors(frameElement) : []);
  }
  return list.concat(scrollableAncestor, getOverflowAncestors(scrollableAncestor, [], traverseIframes));
}
function getFrameElement(win) {
  return win.parent && Object.getPrototypeOf(win.parent) ? win.frameElement : null;
}

// node_modules/@floating-ui/dom/dist/floating-ui.dom.mjs
function getCssDimensions(element) {
  const css = getComputedStyle2(element);
  let width = parseFloat(css.width) || 0;
  let height = parseFloat(css.height) || 0;
  const hasOffset = isHTMLElement(element);
  const offsetWidth = hasOffset ? element.offsetWidth : width;
  const offsetHeight = hasOffset ? element.offsetHeight : height;
  const shouldFallback = round(width) !== offsetWidth || round(height) !== offsetHeight;
  if (shouldFallback) {
    width = offsetWidth;
    height = offsetHeight;
  }
  return {
    width,
    height,
    $: shouldFallback
  };
}
function unwrapElement(element) {
  return !isElement(element) ? element.contextElement : element;
}
function getScale(element) {
  const domElement = unwrapElement(element);
  if (!isHTMLElement(domElement)) {
    return createCoords(1);
  }
  const rect = domElement.getBoundingClientRect();
  const {
    width,
    height,
    $
  } = getCssDimensions(domElement);
  let x = ($ ? round(rect.width) : rect.width) / width;
  let y = ($ ? round(rect.height) : rect.height) / height;
  if (!x || !Number.isFinite(x)) {
    x = 1;
  }
  if (!y || !Number.isFinite(y)) {
    y = 1;
  }
  return {
    x,
    y
  };
}
var noOffsets = /* @__PURE__ */ createCoords(0);
function getVisualOffsets(element) {
  const win = getWindow(element);
  if (!isWebKit() || !win.visualViewport) {
    return noOffsets;
  }
  return {
    x: win.visualViewport.offsetLeft,
    y: win.visualViewport.offsetTop
  };
}
function shouldAddVisualOffsets(element, isFixed, floatingOffsetParent) {
  if (isFixed === void 0) {
    isFixed = false;
  }
  if (!floatingOffsetParent || isFixed && floatingOffsetParent !== getWindow(element)) {
    return false;
  }
  return isFixed;
}
function getBoundingClientRect(element, includeScale, isFixedStrategy, offsetParent) {
  if (includeScale === void 0) {
    includeScale = false;
  }
  if (isFixedStrategy === void 0) {
    isFixedStrategy = false;
  }
  const clientRect = element.getBoundingClientRect();
  const domElement = unwrapElement(element);
  let scale = createCoords(1);
  if (includeScale) {
    if (offsetParent) {
      if (isElement(offsetParent)) {
        scale = getScale(offsetParent);
      }
    } else {
      scale = getScale(element);
    }
  }
  const visualOffsets = shouldAddVisualOffsets(domElement, isFixedStrategy, offsetParent) ? getVisualOffsets(domElement) : createCoords(0);
  let x = (clientRect.left + visualOffsets.x) / scale.x;
  let y = (clientRect.top + visualOffsets.y) / scale.y;
  let width = clientRect.width / scale.x;
  let height = clientRect.height / scale.y;
  if (domElement) {
    const win = getWindow(domElement);
    const offsetWin = offsetParent && isElement(offsetParent) ? getWindow(offsetParent) : offsetParent;
    let currentWin = win;
    let currentIFrame = getFrameElement(currentWin);
    while (currentIFrame && offsetParent && offsetWin !== currentWin) {
      const iframeScale = getScale(currentIFrame);
      const iframeRect = currentIFrame.getBoundingClientRect();
      const css = getComputedStyle2(currentIFrame);
      const left = iframeRect.left + (currentIFrame.clientLeft + parseFloat(css.paddingLeft)) * iframeScale.x;
      const top = iframeRect.top + (currentIFrame.clientTop + parseFloat(css.paddingTop)) * iframeScale.y;
      x *= iframeScale.x;
      y *= iframeScale.y;
      width *= iframeScale.x;
      height *= iframeScale.y;
      x += left;
      y += top;
      currentWin = getWindow(currentIFrame);
      currentIFrame = getFrameElement(currentWin);
    }
  }
  return rectToClientRect({
    width,
    height,
    x,
    y
  });
}
function getWindowScrollBarX(element, rect) {
  const leftScroll = getNodeScroll(element).scrollLeft;
  if (!rect) {
    return getBoundingClientRect(getDocumentElement(element)).left + leftScroll;
  }
  return rect.left + leftScroll;
}
function getHTMLOffset(documentElement, scroll, ignoreScrollbarX) {
  if (ignoreScrollbarX === void 0) {
    ignoreScrollbarX = false;
  }
  const htmlRect = documentElement.getBoundingClientRect();
  const x = htmlRect.left + scroll.scrollLeft - (ignoreScrollbarX ? 0 : (
    // RTL <body> scrollbar.
    getWindowScrollBarX(documentElement, htmlRect)
  ));
  const y = htmlRect.top + scroll.scrollTop;
  return {
    x,
    y
  };
}
function convertOffsetParentRelativeRectToViewportRelativeRect(_ref) {
  let {
    elements,
    rect,
    offsetParent,
    strategy
  } = _ref;
  const isFixed = strategy === "fixed";
  const documentElement = getDocumentElement(offsetParent);
  const topLayer = elements ? isTopLayer(elements.floating) : false;
  if (offsetParent === documentElement || topLayer && isFixed) {
    return rect;
  }
  let scroll = {
    scrollLeft: 0,
    scrollTop: 0
  };
  let scale = createCoords(1);
  const offsets = createCoords(0);
  const isOffsetParentAnElement = isHTMLElement(offsetParent);
  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {
    if (getNodeName(offsetParent) !== "body" || isOverflowElement(documentElement)) {
      scroll = getNodeScroll(offsetParent);
    }
    if (isHTMLElement(offsetParent)) {
      const offsetRect = getBoundingClientRect(offsetParent);
      scale = getScale(offsetParent);
      offsets.x = offsetRect.x + offsetParent.clientLeft;
      offsets.y = offsetRect.y + offsetParent.clientTop;
    }
  }
  const htmlOffset = documentElement && !isOffsetParentAnElement && !isFixed ? getHTMLOffset(documentElement, scroll, true) : createCoords(0);
  return {
    width: rect.width * scale.x,
    height: rect.height * scale.y,
    x: rect.x * scale.x - scroll.scrollLeft * scale.x + offsets.x + htmlOffset.x,
    y: rect.y * scale.y - scroll.scrollTop * scale.y + offsets.y + htmlOffset.y
  };
}
function getClientRects(element) {
  return Array.from(element.getClientRects());
}
function getDocumentRect(element) {
  const html = getDocumentElement(element);
  const scroll = getNodeScroll(element);
  const body = element.ownerDocument.body;
  const width = max(html.scrollWidth, html.clientWidth, body.scrollWidth, body.clientWidth);
  const height = max(html.scrollHeight, html.clientHeight, body.scrollHeight, body.clientHeight);
  let x = -scroll.scrollLeft + getWindowScrollBarX(element);
  const y = -scroll.scrollTop;
  if (getComputedStyle2(body).direction === "rtl") {
    x += max(html.clientWidth, body.clientWidth) - width;
  }
  return {
    width,
    height,
    x,
    y
  };
}
function getViewportRect(element, strategy) {
  const win = getWindow(element);
  const html = getDocumentElement(element);
  const visualViewport = win.visualViewport;
  let width = html.clientWidth;
  let height = html.clientHeight;
  let x = 0;
  let y = 0;
  if (visualViewport) {
    width = visualViewport.width;
    height = visualViewport.height;
    const visualViewportBased = isWebKit();
    if (!visualViewportBased || visualViewportBased && strategy === "fixed") {
      x = visualViewport.offsetLeft;
      y = visualViewport.offsetTop;
    }
  }
  return {
    width,
    height,
    x,
    y
  };
}
function getInnerBoundingClientRect(element, strategy) {
  const clientRect = getBoundingClientRect(element, true, strategy === "fixed");
  const top = clientRect.top + element.clientTop;
  const left = clientRect.left + element.clientLeft;
  const scale = isHTMLElement(element) ? getScale(element) : createCoords(1);
  const width = element.clientWidth * scale.x;
  const height = element.clientHeight * scale.y;
  const x = left * scale.x;
  const y = top * scale.y;
  return {
    width,
    height,
    x,
    y
  };
}
function getClientRectFromClippingAncestor(element, clippingAncestor, strategy) {
  let rect;
  if (clippingAncestor === "viewport") {
    rect = getViewportRect(element, strategy);
  } else if (clippingAncestor === "document") {
    rect = getDocumentRect(getDocumentElement(element));
  } else if (isElement(clippingAncestor)) {
    rect = getInnerBoundingClientRect(clippingAncestor, strategy);
  } else {
    const visualOffsets = getVisualOffsets(element);
    rect = {
      x: clippingAncestor.x - visualOffsets.x,
      y: clippingAncestor.y - visualOffsets.y,
      width: clippingAncestor.width,
      height: clippingAncestor.height
    };
  }
  return rectToClientRect(rect);
}
function hasFixedPositionAncestor(element, stopNode) {
  const parentNode = getParentNode(element);
  if (parentNode === stopNode || !isElement(parentNode) || isLastTraversableNode(parentNode)) {
    return false;
  }
  return getComputedStyle2(parentNode).position === "fixed" || hasFixedPositionAncestor(parentNode, stopNode);
}
function getClippingElementAncestors(element, cache) {
  const cachedResult = cache.get(element);
  if (cachedResult) {
    return cachedResult;
  }
  let result = getOverflowAncestors(element, [], false).filter((el) => isElement(el) && getNodeName(el) !== "body");
  let currentContainingBlockComputedStyle = null;
  const elementIsFixed = getComputedStyle2(element).position === "fixed";
  let currentNode = elementIsFixed ? getParentNode(element) : element;
  while (isElement(currentNode) && !isLastTraversableNode(currentNode)) {
    const computedStyle = getComputedStyle2(currentNode);
    const currentNodeIsContaining = isContainingBlock(currentNode);
    if (!currentNodeIsContaining && computedStyle.position === "fixed") {
      currentContainingBlockComputedStyle = null;
    }
    const shouldDropCurrentNode = elementIsFixed ? !currentNodeIsContaining && !currentContainingBlockComputedStyle : !currentNodeIsContaining && computedStyle.position === "static" && !!currentContainingBlockComputedStyle && ["absolute", "fixed"].includes(currentContainingBlockComputedStyle.position) || isOverflowElement(currentNode) && !currentNodeIsContaining && hasFixedPositionAncestor(element, currentNode);
    if (shouldDropCurrentNode) {
      result = result.filter((ancestor) => ancestor !== currentNode);
    } else {
      currentContainingBlockComputedStyle = computedStyle;
    }
    currentNode = getParentNode(currentNode);
  }
  cache.set(element, result);
  return result;
}
function getClippingRect(_ref) {
  let {
    element,
    boundary,
    rootBoundary,
    strategy
  } = _ref;
  const elementClippingAncestors = boundary === "clippingAncestors" ? isTopLayer(element) ? [] : getClippingElementAncestors(element, this._c) : [].concat(boundary);
  const clippingAncestors = [...elementClippingAncestors, rootBoundary];
  const firstClippingAncestor = clippingAncestors[0];
  const clippingRect = clippingAncestors.reduce((accRect, clippingAncestor) => {
    const rect = getClientRectFromClippingAncestor(element, clippingAncestor, strategy);
    accRect.top = max(rect.top, accRect.top);
    accRect.right = min(rect.right, accRect.right);
    accRect.bottom = min(rect.bottom, accRect.bottom);
    accRect.left = max(rect.left, accRect.left);
    return accRect;
  }, getClientRectFromClippingAncestor(element, firstClippingAncestor, strategy));
  return {
    width: clippingRect.right - clippingRect.left,
    height: clippingRect.bottom - clippingRect.top,
    x: clippingRect.left,
    y: clippingRect.top
  };
}
function getDimensions(element) {
  const {
    width,
    height
  } = getCssDimensions(element);
  return {
    width,
    height
  };
}
function getRectRelativeToOffsetParent(element, offsetParent, strategy) {
  const isOffsetParentAnElement = isHTMLElement(offsetParent);
  const documentElement = getDocumentElement(offsetParent);
  const isFixed = strategy === "fixed";
  const rect = getBoundingClientRect(element, true, isFixed, offsetParent);
  let scroll = {
    scrollLeft: 0,
    scrollTop: 0
  };
  const offsets = createCoords(0);
  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {
    if (getNodeName(offsetParent) !== "body" || isOverflowElement(documentElement)) {
      scroll = getNodeScroll(offsetParent);
    }
    if (isOffsetParentAnElement) {
      const offsetRect = getBoundingClientRect(offsetParent, true, isFixed, offsetParent);
      offsets.x = offsetRect.x + offsetParent.clientLeft;
      offsets.y = offsetRect.y + offsetParent.clientTop;
    } else if (documentElement) {
      offsets.x = getWindowScrollBarX(documentElement);
    }
  }
  const htmlOffset = documentElement && !isOffsetParentAnElement && !isFixed ? getHTMLOffset(documentElement, scroll) : createCoords(0);
  const x = rect.left + scroll.scrollLeft - offsets.x - htmlOffset.x;
  const y = rect.top + scroll.scrollTop - offsets.y - htmlOffset.y;
  return {
    x,
    y,
    width: rect.width,
    height: rect.height
  };
}
function isStaticPositioned(element) {
  return getComputedStyle2(element).position === "static";
}
function getTrueOffsetParent(element, polyfill) {
  if (!isHTMLElement(element) || getComputedStyle2(element).position === "fixed") {
    return null;
  }
  if (polyfill) {
    return polyfill(element);
  }
  let rawOffsetParent = element.offsetParent;
  if (getDocumentElement(element) === rawOffsetParent) {
    rawOffsetParent = rawOffsetParent.ownerDocument.body;
  }
  return rawOffsetParent;
}
function getOffsetParent(element, polyfill) {
  const win = getWindow(element);
  if (isTopLayer(element)) {
    return win;
  }
  if (!isHTMLElement(element)) {
    let svgOffsetParent = getParentNode(element);
    while (svgOffsetParent && !isLastTraversableNode(svgOffsetParent)) {
      if (isElement(svgOffsetParent) && !isStaticPositioned(svgOffsetParent)) {
        return svgOffsetParent;
      }
      svgOffsetParent = getParentNode(svgOffsetParent);
    }
    return win;
  }
  let offsetParent = getTrueOffsetParent(element, polyfill);
  while (offsetParent && isTableElement(offsetParent) && isStaticPositioned(offsetParent)) {
    offsetParent = getTrueOffsetParent(offsetParent, polyfill);
  }
  if (offsetParent && isLastTraversableNode(offsetParent) && isStaticPositioned(offsetParent) && !isContainingBlock(offsetParent)) {
    return win;
  }
  return offsetParent || getContainingBlock(element) || win;
}
var getElementRects = async function(data) {
  const getOffsetParentFn = this.getOffsetParent || getOffsetParent;
  const getDimensionsFn = this.getDimensions;
  const floatingDimensions = await getDimensionsFn(data.floating);
  return {
    reference: getRectRelativeToOffsetParent(data.reference, await getOffsetParentFn(data.floating), data.strategy),
    floating: {
      x: 0,
      y: 0,
      width: floatingDimensions.width,
      height: floatingDimensions.height
    }
  };
};
function isRTL(element) {
  return getComputedStyle2(element).direction === "rtl";
}
var platform = {
  convertOffsetParentRelativeRectToViewportRelativeRect,
  getDocumentElement,
  getClippingRect,
  getOffsetParent,
  getElementRects,
  getClientRects,
  getDimensions,
  getScale,
  isElement,
  isRTL
};
function rectsAreEqual(a, b) {
  return a.x === b.x && a.y === b.y && a.width === b.width && a.height === b.height;
}
function observeMove(element, onMove) {
  let io = null;
  let timeoutId;
  const root = getDocumentElement(element);
  function cleanup() {
    var _io;
    clearTimeout(timeoutId);
    (_io = io) == null || _io.disconnect();
    io = null;
  }
  function refresh(skip, threshold) {
    if (skip === void 0) {
      skip = false;
    }
    if (threshold === void 0) {
      threshold = 1;
    }
    cleanup();
    const elementRectForRootMargin = element.getBoundingClientRect();
    const {
      left,
      top,
      width,
      height
    } = elementRectForRootMargin;
    if (!skip) {
      onMove();
    }
    if (!width || !height) {
      return;
    }
    const insetTop = floor(top);
    const insetRight = floor(root.clientWidth - (left + width));
    const insetBottom = floor(root.clientHeight - (top + height));
    const insetLeft = floor(left);
    const rootMargin = -insetTop + "px " + -insetRight + "px " + -insetBottom + "px " + -insetLeft + "px";
    const options = {
      rootMargin,
      threshold: max(0, min(1, threshold)) || 1
    };
    let isFirstUpdate = true;
    function handleObserve(entries) {
      const ratio = entries[0].intersectionRatio;
      if (ratio !== threshold) {
        if (!isFirstUpdate) {
          return refresh();
        }
        if (!ratio) {
          timeoutId = setTimeout(() => {
            refresh(false, 1e-7);
          }, 1e3);
        } else {
          refresh(false, ratio);
        }
      }
      if (ratio === 1 && !rectsAreEqual(elementRectForRootMargin, element.getBoundingClientRect())) {
        refresh();
      }
      isFirstUpdate = false;
    }
    try {
      io = new IntersectionObserver(handleObserve, __spreadProps(__spreadValues({}, options), {
        // Handle <iframe>s
        root: root.ownerDocument
      }));
    } catch (e) {
      io = new IntersectionObserver(handleObserve, options);
    }
    io.observe(element);
  }
  refresh(true);
  return cleanup;
}
function autoUpdate(reference, floating, update, options) {
  if (options === void 0) {
    options = {};
  }
  const {
    ancestorScroll = true,
    ancestorResize = true,
    elementResize = typeof ResizeObserver === "function",
    layoutShift = typeof IntersectionObserver === "function",
    animationFrame = false
  } = options;
  const referenceEl = unwrapElement(reference);
  const ancestors = ancestorScroll || ancestorResize ? [...referenceEl ? getOverflowAncestors(referenceEl) : [], ...getOverflowAncestors(floating)] : [];
  ancestors.forEach((ancestor) => {
    ancestorScroll && ancestor.addEventListener("scroll", update, {
      passive: true
    });
    ancestorResize && ancestor.addEventListener("resize", update);
  });
  const cleanupIo = referenceEl && layoutShift ? observeMove(referenceEl, update) : null;
  let reobserveFrame = -1;
  let resizeObserver = null;
  if (elementResize) {
    resizeObserver = new ResizeObserver((_ref) => {
      let [firstEntry] = _ref;
      if (firstEntry && firstEntry.target === referenceEl && resizeObserver) {
        resizeObserver.unobserve(floating);
        cancelAnimationFrame(reobserveFrame);
        reobserveFrame = requestAnimationFrame(() => {
          var _resizeObserver;
          (_resizeObserver = resizeObserver) == null || _resizeObserver.observe(floating);
        });
      }
      update();
    });
    if (referenceEl && !animationFrame) {
      resizeObserver.observe(referenceEl);
    }
    resizeObserver.observe(floating);
  }
  let frameId;
  let prevRefRect = animationFrame ? getBoundingClientRect(reference) : null;
  if (animationFrame) {
    frameLoop();
  }
  function frameLoop() {
    const nextRefRect = getBoundingClientRect(reference);
    if (prevRefRect && !rectsAreEqual(prevRefRect, nextRefRect)) {
      update();
    }
    prevRefRect = nextRefRect;
    frameId = requestAnimationFrame(frameLoop);
  }
  update();
  return () => {
    var _resizeObserver2;
    ancestors.forEach((ancestor) => {
      ancestorScroll && ancestor.removeEventListener("scroll", update);
      ancestorResize && ancestor.removeEventListener("resize", update);
    });
    cleanupIo == null || cleanupIo();
    (_resizeObserver2 = resizeObserver) == null || _resizeObserver2.disconnect();
    resizeObserver = null;
    if (animationFrame) {
      cancelAnimationFrame(frameId);
    }
  };
}
var offset2 = offset;
var flip2 = flip;
var arrow2 = arrow;
var computePosition2 = (reference, floating, options) => {
  const cache = /* @__PURE__ */ new Map();
  const mergedOptions = __spreadValues({
    platform
  }, options);
  const platformWithCache = __spreadProps(__spreadValues({}, mergedOptions.platform), {
    _c: cache
  });
  return computePosition(reference, floating, __spreadProps(__spreadValues({}, mergedOptions), {
    platform: platformWithCache
  }));
};

// node_modules/carbon-components-angular/fesm2020/carbon-components-angular-popover.mjs
var _c0 = ["content"];
var _c1 = ["*"];
function PopoverContent_span_4_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275element(0, "span", 4);
  }
}
function PopoverContent_span_5_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275element(0, "span", 5);
  }
}
var PopoverContainer = class {
  constructor(elementRef, ngZone, renderer, changeDetectorRef) {
    this.elementRef = elementRef;
    this.ngZone = ngZone;
    this.renderer = renderer;
    this.changeDetectorRef = changeDetectorRef;
    this._align = "bottom";
    this.alignmentClassPrefix = "cds--popover--";
    this.onClose = new EventEmitter();
    this.onOpen = new EventEmitter();
    this.isOpenChange = new EventEmitter();
    this.caret = true;
    this.dropShadow = true;
    this.highContrast = false;
    this.autoAlign = false;
    this.containerClass = true;
    this.isOpen = false;
  }
  /**
   * Set alignment of popover
   * As of v5, `oldPlacements` are now deprecated in favor of Placements
   *
   * When `autoAlign` is set to `true`, alignment may change for best placement
   */
  set align(alignment) {
    if (!alignment) {
      return;
    }
    const previousAlignment = this._align;
    switch (alignment) {
      case "top-left":
        this._align = "top-start";
        break;
      case "top-right":
        this._align = "top-end";
        break;
      case "bottom-left":
        this._align = "bottom-start";
        break;
      case "bottom-right":
        this._align = "bottom-end";
        break;
      case "left-top":
        this._align = "left-start";
        break;
      case "left-bottom":
        this._align = "left-end";
        break;
      case "right-top":
        this._align = "right-start";
        break;
      case "right-bottom":
        this._align = "right-end";
        break;
      default:
        this._align = alignment;
        break;
    }
    this.updateAlignmentClass(this._align, previousAlignment);
  }
  /**
   * Handles emitting open/close event
   * @param open - Is the popover container open
   * @param event - Event
   */
  handleChange(open2, event) {
    if (this.isOpen !== open2 && event) {
      this.isOpenChange.emit(open2);
    }
    if (open2) {
      if (event) {
        this.onOpen.emit(event);
      }
      if (this.autoAlign) {
        if (this.caretRef) {
          const computedStyle = getComputedStyle(this.caretRef);
          const offset3 = computedStyle.getPropertyValue("--cds-popover-offset");
          const height = computedStyle.getPropertyValue("--cds-popover-caret-height");
          this.caretOffset = (offset3?.includes("px") ? Number(offset3.split("px", 1)[0]) : Number(offset3.split("rem", 1)[0]) * 16) || 10;
          this.caretHeight = (height?.includes("px") ? Number(height.split("px", 1)[0]) : Number(height.split("rem", 1)[0]) * 16) || 6;
        }
        if (this.elementRef.nativeElement && this.popoverContentRef) {
          this.unmountFloatingElement = autoUpdate(this.elementRef.nativeElement, this.popoverContentRef, this.recomputePosition.bind(this));
        }
      }
    } else {
      this.cleanUp();
      if (event) {
        this.onClose.emit(event);
      }
    }
    this.isOpen = open2;
    this.changeDetectorRef.markForCheck();
  }
  roundByDPR(value) {
    const dpr = window.devicePixelRatio || 1;
    return Math.round(value * dpr) / dpr;
  }
  /**
   * Compute position of tooltip when autoAlign is enabled
   */
  recomputePosition() {
    this.ngZone.runOutsideAngular(async () => {
      const {
        x,
        y,
        placement,
        middlewareData
      } = await computePosition2(this.elementRef.nativeElement, this.popoverContentRef, {
        placement: this._align,
        strategy: "fixed",
        middleware: [offset2(this.caretOffset), flip2({
          fallbackAxisSideDirection: "start"
        }), arrow2({
          element: this.caretRef
        })]
      });
      const previousAlignment = this._align;
      this._align = placement;
      this.updateAlignmentClass(this._align, previousAlignment);
      Object.assign(this.popoverContentRef.style, {
        position: "fixed",
        top: "0",
        left: "0",
        // Using transform instead of top/left position to improve performance
        transform: `translate(${this.roundByDPR(x)}px,${this.roundByDPR(y)}px)`
      });
      if (middlewareData.arrow) {
        const {
          x: arrowX,
          y: arrowY
        } = middlewareData.arrow;
        const staticSide = {
          top: "bottom",
          right: "left",
          bottom: "top",
          left: "right"
        }[placement.split("-")[0]];
        this.caretRef.style.left = arrowX != null ? `${arrowX}px` : "";
        this.caretRef.style.top = arrowY != null ? `${arrowY}px` : "";
        this.caretRef.style.right = "";
        this.caretRef.style.bottom = "";
        if (staticSide) {
          this.caretRef.style[staticSide] = `${-this.caretHeight}px`;
        }
      }
    });
  }
  /**
   * Close the popover and reopen it with updated values without emitting an event
   * @param changes
   */
  ngOnChanges(changes) {
    const originalState = this.isOpen;
    this.handleChange(false);
    if (changes.autoAlign && !changes.autoAlign.firstChange) {
      this.popoverContentRef = this.elementRef.nativeElement.querySelector(".cds--popover-content");
      this.popoverContentRef.setAttribute("style", "");
      this.caretRef = this.elementRef.nativeElement.querySelector("span.cds--popover-caret");
    }
    this.handleChange(originalState);
  }
  /**
   * Handle initialization of element
   */
  ngAfterViewInit() {
    this.initializeReferences();
  }
  initializeReferences() {
    this.updateAlignmentClass(this._align);
    this.popoverContentRef = this.elementRef.nativeElement.querySelector(".cds--popover-content");
    this.caretRef = this.elementRef.nativeElement.querySelector("span.cds--popover-caret");
    this.handleChange(this.isOpen);
  }
  /**
   * Clean up
   */
  ngOnDestroy() {
    this.cleanUp();
  }
  /**
   * Clean up `autoUpdate` if auto alignment is enabled
   */
  cleanUp() {
    if (this.unmountFloatingElement) {
      this.unmountFloatingElement();
    }
    this.unmountFloatingElement = void 0;
  }
  /**
   * Replace existing previous alignment class with new
   * @param previousAlignment
   */
  updateAlignmentClass(newAlignment, previousAlignment) {
    if (this.elementRef.nativeElement && previousAlignment !== newAlignment) {
      const regexp = new RegExp("right|top|left|bottom");
      this.elementRef.nativeElement.classList.forEach((className) => {
        if (regexp.test(className)) {
          this.renderer.removeClass(this.elementRef.nativeElement, `${className}`);
        }
      });
      this.renderer.addClass(this.elementRef.nativeElement, `${this.alignmentClassPrefix}${newAlignment}`);
    }
  }
};
PopoverContainer.\u0275fac = function PopoverContainer_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || PopoverContainer)(\u0275\u0275directiveInject(ElementRef), \u0275\u0275directiveInject(NgZone), \u0275\u0275directiveInject(Renderer2), \u0275\u0275directiveInject(ChangeDetectorRef));
};
PopoverContainer.\u0275dir = /* @__PURE__ */ \u0275\u0275defineDirective({
  type: PopoverContainer,
  selectors: [["", "cdsPopover", ""], ["", "ibmPopover", ""]],
  hostVars: 12,
  hostBindings: function PopoverContainer_HostBindings(rf, ctx) {
    if (rf & 2) {
      \u0275\u0275classProp("cds--popover--caret", ctx.caret)("cds--popover--drop-shadow", ctx.dropShadow)("cds--popover--high-contrast", ctx.highContrast)("cds--popover--auto-align", ctx.autoAlign)("cds--popover-container", ctx.containerClass)("cds--popover--open", ctx.isOpen);
    }
  },
  inputs: {
    align: "align",
    caret: "caret",
    dropShadow: "dropShadow",
    highContrast: "highContrast",
    autoAlign: "autoAlign",
    isOpen: "isOpen"
  },
  outputs: {
    onClose: "onClose",
    onOpen: "onOpen",
    isOpenChange: "isOpenChange"
  },
  standalone: false,
  features: [\u0275\u0275NgOnChangesFeature]
});
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(PopoverContainer, [{
    type: Directive,
    args: [{
      selector: "[cdsPopover], [ibmPopover]"
    }]
  }], function() {
    return [{
      type: ElementRef
    }, {
      type: NgZone
    }, {
      type: Renderer2
    }, {
      type: ChangeDetectorRef
    }];
  }, {
    align: [{
      type: Input
    }],
    onClose: [{
      type: Output
    }],
    onOpen: [{
      type: Output
    }],
    isOpenChange: [{
      type: Output
    }],
    caret: [{
      type: HostBinding,
      args: ["class.cds--popover--caret"]
    }, {
      type: Input
    }],
    dropShadow: [{
      type: HostBinding,
      args: ["class.cds--popover--drop-shadow"]
    }, {
      type: Input
    }],
    highContrast: [{
      type: HostBinding,
      args: ["class.cds--popover--high-contrast"]
    }, {
      type: Input
    }],
    autoAlign: [{
      type: HostBinding,
      args: ["class.cds--popover--auto-align"]
    }, {
      type: Input
    }],
    containerClass: [{
      type: HostBinding,
      args: ["class.cds--popover-container"]
    }],
    isOpen: [{
      type: Input
    }, {
      type: HostBinding,
      args: ["class.cds--popover--open"]
    }]
  });
})();
var PopoverContent = class {
  constructor(changeDetectorRef) {
    this.changeDetectorRef = changeDetectorRef;
    this.popoverClass = true;
    this.autoAlign = false;
  }
  ngAfterViewInit() {
    if (this.popoverContent) {
      this.autoAlign = !!this.popoverContent.nativeElement.closest(".cds--popover--auto-align");
      this.changeDetectorRef.detectChanges();
    }
  }
};
PopoverContent.\u0275fac = function PopoverContent_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || PopoverContent)(\u0275\u0275directiveInject(ChangeDetectorRef));
};
PopoverContent.\u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({
  type: PopoverContent,
  selectors: [["cds-popover-content"], ["ibm-popover-content"]],
  viewQuery: function PopoverContent_Query(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275viewQuery(_c0, 5);
    }
    if (rf & 2) {
      let _t;
      \u0275\u0275queryRefresh(_t = \u0275\u0275loadQuery()) && (ctx.popoverContent = _t.first);
    }
  },
  hostVars: 2,
  hostBindings: function PopoverContent_HostBindings(rf, ctx) {
    if (rf & 2) {
      \u0275\u0275classProp("cds--popover", ctx.popoverClass);
    }
  },
  standalone: false,
  ngContentSelectors: _c1,
  decls: 6,
  vars: 2,
  consts: [["content", ""], [1, "cds--popover-content"], ["class", "cds--popover-caret cds--popover--auto-align", 4, "ngIf"], ["class", "cds--popover-caret", 4, "ngIf"], [1, "cds--popover-caret", "cds--popover--auto-align"], [1, "cds--popover-caret"]],
  template: function PopoverContent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275projectionDef();
      \u0275\u0275elementStart(0, "span", 1, 0)(2, "div");
      \u0275\u0275projection(3);
      \u0275\u0275elementEnd();
      \u0275\u0275template(4, PopoverContent_span_4_Template, 1, 0, "span", 2);
      \u0275\u0275elementEnd();
      \u0275\u0275template(5, PopoverContent_span_5_Template, 1, 0, "span", 3);
    }
    if (rf & 2) {
      \u0275\u0275advance(4);
      \u0275\u0275property("ngIf", ctx.autoAlign);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", !ctx.autoAlign);
    }
  },
  dependencies: [NgIf],
  encapsulation: 2
});
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(PopoverContent, [{
    type: Component,
    args: [{
      selector: "cds-popover-content, ibm-popover-content",
      template: `
		<span class="cds--popover-content" #content>
			<div>
				<ng-content></ng-content>
			</div>
			<span *ngIf="autoAlign" class="cds--popover-caret cds--popover--auto-align"></span>
		</span>
		<span *ngIf="!autoAlign" class="cds--popover-caret"></span>
	`
    }]
  }], function() {
    return [{
      type: ChangeDetectorRef
    }];
  }, {
    popoverClass: [{
      type: HostBinding,
      args: ["class.cds--popover"]
    }],
    popoverContent: [{
      type: ViewChild,
      args: ["content"]
    }]
  });
})();
var PopoverModule = class {
};
PopoverModule.\u0275fac = function PopoverModule_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || PopoverModule)();
};
PopoverModule.\u0275mod = /* @__PURE__ */ \u0275\u0275defineNgModule({
  type: PopoverModule,
  declarations: [PopoverContainer, PopoverContent],
  imports: [CommonModule],
  exports: [PopoverContainer, PopoverContent]
});
PopoverModule.\u0275inj = /* @__PURE__ */ \u0275\u0275defineInjector({
  imports: [CommonModule]
});
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(PopoverModule, [{
    type: NgModule,
    args: [{
      declarations: [PopoverContainer, PopoverContent],
      exports: [PopoverContainer, PopoverContent],
      imports: [CommonModule]
    }]
  }], null, null);
})();

// node_modules/carbon-components-angular/fesm2020/carbon-components-angular-tooltip.mjs
var _c02 = ["contentWrapper"];
var _c12 = ["*"];
var _c2 = (a0) => ({
  $implicit: a0
});
function Tooltip_span_3_ng_container_1_ng_container_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementContainerStart(0);
    \u0275\u0275text(1);
    \u0275\u0275elementContainerEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(3);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(ctx_r0.description);
  }
}
function Tooltip_span_3_ng_container_1_3_ng_template_0_Template(rf, ctx) {
}
function Tooltip_span_3_ng_container_1_3_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275template(0, Tooltip_span_3_ng_container_1_3_ng_template_0_Template, 0, 0, "ng-template", 7);
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(3);
    \u0275\u0275property("ngTemplateOutlet", ctx_r0.description)("ngTemplateOutletContext", \u0275\u0275pureFunction1(2, _c2, ctx_r0.templateContext));
  }
}
function Tooltip_span_3_ng_container_1_span_4_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275element(0, "span", 8);
  }
}
function Tooltip_span_3_ng_container_1_span_5_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275element(0, "span", 9);
  }
}
function Tooltip_span_3_ng_container_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementContainerStart(0);
    \u0275\u0275elementStart(1, "span", 4);
    \u0275\u0275template(2, Tooltip_span_3_ng_container_1_ng_container_2_Template, 2, 1, "ng-container", 3)(3, Tooltip_span_3_ng_container_1_3_Template, 1, 4, null, 3)(4, Tooltip_span_3_ng_container_1_span_4_Template, 1, 0, "span", 5);
    \u0275\u0275elementEnd();
    \u0275\u0275template(5, Tooltip_span_3_ng_container_1_span_5_Template, 1, 0, "span", 6);
    \u0275\u0275elementContainerEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(2);
    \u0275\u0275property("ngIf", !ctx_r0.isTemplate(ctx_r0.description));
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.isTemplate(ctx_r0.description));
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.autoAlign);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", !ctx_r0.autoAlign);
  }
}
function Tooltip_span_3_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span", 2);
    \u0275\u0275template(1, Tooltip_span_3_ng_container_1_Template, 6, 4, "ng-container", 3);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275property("id", ctx_r0.id);
    \u0275\u0275attribute("aria-hidden", !ctx_r0.isOpen);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", !ctx_r0.disabled);
  }
}
function TooltipDefinition_span_2_ng_container_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementContainerStart(0);
    \u0275\u0275text(1);
    \u0275\u0275elementContainerEnd();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext(2);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(ctx_r1.description);
  }
}
function TooltipDefinition_span_2_3_ng_template_0_Template(rf, ctx) {
}
function TooltipDefinition_span_2_3_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275template(0, TooltipDefinition_span_2_3_ng_template_0_Template, 0, 0, "ng-template", 7);
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext(2);
    \u0275\u0275property("ngTemplateOutlet", ctx_r1.description)("ngTemplateOutletContext", \u0275\u0275pureFunction1(2, _c2, ctx_r1.templateContext));
  }
}
function TooltipDefinition_span_2_span_4_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275element(0, "span", 8);
  }
}
function TooltipDefinition_span_2_span_5_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275element(0, "span", 9);
  }
}
function TooltipDefinition_span_2_Template(rf, ctx) {
  if (rf & 1) {
    const _r1 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "span", 2);
    \u0275\u0275listener("mousedown", function TooltipDefinition_span_2_Template_span_mousedown_0_listener() {
      \u0275\u0275restoreView(_r1);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.onPopoverMouseDown());
    })("mouseup", function TooltipDefinition_span_2_Template_span_mouseup_0_listener() {
      \u0275\u0275restoreView(_r1);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.onPopoverMouseUp());
    });
    \u0275\u0275elementStart(1, "span", 3);
    \u0275\u0275template(2, TooltipDefinition_span_2_ng_container_2_Template, 2, 1, "ng-container", 4)(3, TooltipDefinition_span_2_3_Template, 1, 4, null, 4)(4, TooltipDefinition_span_2_span_4_Template, 1, 0, "span", 5);
    \u0275\u0275elementEnd();
    \u0275\u0275template(5, TooltipDefinition_span_2_span_5_Template, 1, 0, "span", 6);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext();
    \u0275\u0275property("id", ctx_r1.id);
    \u0275\u0275attribute("aria-hidden", !ctx_r1.isOpen);
    \u0275\u0275advance(2);
    \u0275\u0275property("ngIf", !ctx_r1.isTemplate(ctx_r1.description));
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r1.isTemplate(ctx_r1.description));
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r1.autoAlign);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", !ctx_r1.autoAlign);
  }
}
var Tooltip = class _Tooltip extends PopoverContainer {
  constructor(elementRef, ngZone, renderer, changeDetectorRef) {
    super(elementRef, ngZone, renderer, changeDetectorRef);
    this.elementRef = elementRef;
    this.ngZone = ngZone;
    this.renderer = renderer;
    this.changeDetectorRef = changeDetectorRef;
    this.tooltipClass = true;
    this.id = `tooltip-${_Tooltip.tooltipCount++}`;
    this.enterDelayMs = 100;
    this.leaveDelayMs = 300;
    this.disabled = false;
    this.highContrast = true;
    this.dropShadow = false;
  }
  mouseenter(event) {
    clearTimeout(this.timeoutId);
    this.timeoutId = setTimeout(() => {
      this.handleChange(true, event);
    }, this.enterDelayMs);
  }
  mouseleave(event) {
    clearTimeout(this.timeoutId);
    this.timeoutId = setTimeout(() => {
      this.handleChange(false, event);
    }, this.leaveDelayMs);
  }
  hostkeys(event) {
    if (open && event.key === "Escape") {
      event.stopPropagation();
      this.handleChange(false, event);
    }
  }
  // We are not focusing on entire popover, only the trigger
  handleFocus(event) {
    this.handleChange(true, event);
  }
  handleFocusOut(event) {
    this.handleChange(false, event);
  }
  isTemplate(value) {
    return value instanceof TemplateRef;
  }
  /**
   * Close the popover and reopen it with updated values without emitting an event
   * @param changes
   */
  ngOnChanges(changes) {
    const originalState = this.isOpen;
    this.handleChange(false);
    if (changes.autoAlign && !changes.autoAlign.firstChange || changes.disabled && !changes.disabled.firstChange && !changes.disabled.currentValue || changes.description) {
      this.changeDetectorRef.detectChanges();
      this.popoverContentRef = this.elementRef.nativeElement.querySelector(".cds--popover-content");
      this.popoverContentRef?.setAttribute("style", "");
      this.caretRef = this.elementRef.nativeElement.querySelector("span.cds--popover-caret");
    }
    this.handleChange(originalState);
  }
  /**
   * Check for any changes in the projected content & apply accessibility attribute if needed
   */
  ngAfterContentChecked() {
    if (this.wrapper) {
      const buttonElement = this.wrapper.nativeElement.querySelector("button");
      if (buttonElement && !buttonElement.getAttribute("aria-labelledby")) {
        buttonElement.setAttribute("aria-labelledby", this.id);
      }
    }
  }
};
Tooltip.tooltipCount = 0;
Tooltip.\u0275fac = function Tooltip_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || Tooltip)(\u0275\u0275directiveInject(ElementRef), \u0275\u0275directiveInject(NgZone), \u0275\u0275directiveInject(Renderer2), \u0275\u0275directiveInject(ChangeDetectorRef));
};
Tooltip.\u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({
  type: Tooltip,
  selectors: [["cds-tooltip"], ["ibm-tooltip"]],
  viewQuery: function Tooltip_Query(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275viewQuery(_c02, 5);
    }
    if (rf & 2) {
      let _t;
      \u0275\u0275queryRefresh(_t = \u0275\u0275loadQuery()) && (ctx.wrapper = _t.first);
    }
  },
  hostVars: 2,
  hostBindings: function Tooltip_HostBindings(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275listener("mouseenter", function Tooltip_mouseenter_HostBindingHandler($event) {
        return ctx.mouseenter($event);
      })("mouseleave", function Tooltip_mouseleave_HostBindingHandler($event) {
        return ctx.mouseleave($event);
      })("keyup", function Tooltip_keyup_HostBindingHandler($event) {
        return ctx.hostkeys($event);
      })("focusin", function Tooltip_focusin_HostBindingHandler($event) {
        return ctx.handleFocus($event);
      })("focusout", function Tooltip_focusout_HostBindingHandler($event) {
        return ctx.handleFocusOut($event);
      });
    }
    if (rf & 2) {
      \u0275\u0275classProp("cds--tooltip", ctx.tooltipClass);
    }
  },
  inputs: {
    id: "id",
    enterDelayMs: "enterDelayMs",
    leaveDelayMs: "leaveDelayMs",
    disabled: "disabled",
    description: "description",
    templateContext: "templateContext"
  },
  standalone: false,
  features: [\u0275\u0275InheritDefinitionFeature, \u0275\u0275NgOnChangesFeature],
  ngContentSelectors: _c12,
  decls: 4,
  vars: 1,
  consts: [["contentWrapper", ""], ["class", "cds--popover", "role", "tooltip", 3, "id", 4, "ngIf"], ["role", "tooltip", 1, "cds--popover", 3, "id"], [4, "ngIf"], [1, "cds--popover-content", "cds--tooltip-content"], ["class", "cds--popover-caret cds--popover--auto-align", 4, "ngIf"], ["class", "cds--popover-caret", 4, "ngIf"], [3, "ngTemplateOutlet", "ngTemplateOutletContext"], [1, "cds--popover-caret", "cds--popover--auto-align"], [1, "cds--popover-caret"]],
  template: function Tooltip_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275projectionDef();
      \u0275\u0275elementStart(0, "span", null, 0);
      \u0275\u0275projection(2);
      \u0275\u0275elementEnd();
      \u0275\u0275template(3, Tooltip_span_3_Template, 2, 3, "span", 1);
    }
    if (rf & 2) {
      \u0275\u0275advance(3);
      \u0275\u0275property("ngIf", ctx.description);
    }
  },
  dependencies: [NgIf, NgTemplateOutlet],
  encapsulation: 2,
  changeDetection: 0
});
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(Tooltip, [{
    type: Component,
    args: [{
      selector: "cds-tooltip, ibm-tooltip",
      changeDetection: ChangeDetectionStrategy.OnPush,
      template: `
		<span #contentWrapper>
			<ng-content></ng-content>
		</span>
		<span
			*ngIf="description"
			class="cds--popover"
			[id]="id"
			[attr.aria-hidden]="!isOpen"
			role="tooltip">
			<ng-container *ngIf="!disabled">
				<span class="cds--popover-content cds--tooltip-content">
					<ng-container *ngIf="!isTemplate(description)">{{description}}</ng-container>
					<ng-template *ngIf="isTemplate(description)" [ngTemplateOutlet]="description" [ngTemplateOutletContext]="{ $implicit: templateContext }"></ng-template>
					<span *ngIf="autoAlign" class="cds--popover-caret cds--popover--auto-align"></span>
				</span>
				<span *ngIf="!autoAlign" class="cds--popover-caret"></span>
			</ng-container>
		</span>
	`
    }]
  }], function() {
    return [{
      type: ElementRef
    }, {
      type: NgZone
    }, {
      type: Renderer2
    }, {
      type: ChangeDetectorRef
    }];
  }, {
    tooltipClass: [{
      type: HostBinding,
      args: ["class.cds--tooltip"]
    }],
    id: [{
      type: Input
    }],
    enterDelayMs: [{
      type: Input
    }],
    leaveDelayMs: [{
      type: Input
    }],
    disabled: [{
      type: Input
    }],
    description: [{
      type: Input
    }],
    templateContext: [{
      type: Input
    }],
    wrapper: [{
      type: ViewChild,
      args: ["contentWrapper"]
    }],
    mouseenter: [{
      type: HostListener,
      args: ["mouseenter", ["$event"]]
    }],
    mouseleave: [{
      type: HostListener,
      args: ["mouseleave", ["$event"]]
    }],
    hostkeys: [{
      type: HostListener,
      args: ["keyup", ["$event"]]
    }],
    handleFocus: [{
      type: HostListener,
      args: ["focusin", ["$event"]]
    }],
    handleFocusOut: [{
      type: HostListener,
      args: ["focusout", ["$event"]]
    }]
  });
})();
var TooltipDefinition = class _TooltipDefinition extends PopoverContainer {
  constructor(elementRef, ngZone, renderer, changeDetectorRef) {
    super(elementRef, ngZone, renderer, changeDetectorRef);
    this.elementRef = elementRef;
    this.ngZone = ngZone;
    this.renderer = renderer;
    this.changeDetectorRef = changeDetectorRef;
    this.id = `tooltip-definition-${_TooltipDefinition.tooltipCount++}`;
    this.openOnHover = false;
    this.isInteractingWithPopover = false;
    this.highContrast = true;
    this.dropShadow = false;
  }
  onBlur(event) {
    if (!this.isInteractingWithPopover) {
      this.handleChange(false, event);
    }
  }
  onClick(event) {
    if (event.button === 0) {
      this.handleChange(!this.isOpen, event);
    }
  }
  onPopoverMouseDown() {
    this.isInteractingWithPopover = true;
  }
  onPopoverMouseUp() {
    this.isInteractingWithPopover = false;
  }
  hostkeys(event) {
    if (this.isOpen && event.key === "Escape") {
      event.stopPropagation();
      this.handleChange(false, event);
    }
  }
  mouseleave(event) {
    this.handleChange(false, event);
  }
  mouseenter(event) {
    if (this.openOnHover) {
      this.handleChange(true, event);
    }
  }
  onFocus(event) {
    this.handleChange(true, event);
  }
  isTemplate(value) {
    return value instanceof TemplateRef;
  }
};
TooltipDefinition.tooltipCount = 0;
TooltipDefinition.\u0275fac = function TooltipDefinition_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || TooltipDefinition)(\u0275\u0275directiveInject(ElementRef), \u0275\u0275directiveInject(NgZone), \u0275\u0275directiveInject(Renderer2), \u0275\u0275directiveInject(ChangeDetectorRef));
};
TooltipDefinition.\u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({
  type: TooltipDefinition,
  selectors: [["cds-tooltip-definition"], ["ibm-tooltip-definition"]],
  hostBindings: function TooltipDefinition_HostBindings(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275listener("keyup", function TooltipDefinition_keyup_HostBindingHandler($event) {
        return ctx.hostkeys($event);
      })("mouseleave", function TooltipDefinition_mouseleave_HostBindingHandler($event) {
        return ctx.mouseleave($event);
      })("mouseenter", function TooltipDefinition_mouseenter_HostBindingHandler($event) {
        return ctx.mouseenter($event);
      })("focusin", function TooltipDefinition_focusin_HostBindingHandler($event) {
        return ctx.onFocus($event);
      });
    }
  },
  inputs: {
    id: "id",
    description: "description",
    templateContext: "templateContext",
    openOnHover: "openOnHover"
  },
  standalone: false,
  features: [\u0275\u0275InheritDefinitionFeature],
  ngContentSelectors: _c12,
  decls: 3,
  vars: 4,
  consts: [["type", "button", 1, "cds--definition-term", 3, "blur", "mousedown"], ["class", "cds--popover", "role", "tooltip", 3, "id", "mousedown", "mouseup", 4, "ngIf"], ["role", "tooltip", 1, "cds--popover", 3, "mousedown", "mouseup", "id"], ["aria-live", "polite", 1, "cds--popover-content", "cds--definition-tooltip"], [4, "ngIf"], ["class", "cds--popover-caret cds--popover--auto-align", 4, "ngIf"], ["class", "cds--popover-caret", 4, "ngIf"], [3, "ngTemplateOutlet", "ngTemplateOutletContext"], [1, "cds--popover-caret", "cds--popover--auto-align"], [1, "cds--popover-caret"]],
  template: function TooltipDefinition_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275projectionDef();
      \u0275\u0275elementStart(0, "button", 0);
      \u0275\u0275listener("blur", function TooltipDefinition_Template_button_blur_0_listener($event) {
        return ctx.onBlur($event);
      })("mousedown", function TooltipDefinition_Template_button_mousedown_0_listener($event) {
        return ctx.onClick($event);
      });
      \u0275\u0275projection(1);
      \u0275\u0275elementEnd();
      \u0275\u0275template(2, TooltipDefinition_span_2_Template, 6, 6, "span", 1);
    }
    if (rf & 2) {
      \u0275\u0275attribute("aria-controls", ctx.id)("aria-expanded", ctx.isOpen)("aria-describedby", ctx.isOpen ? ctx.id : null);
      \u0275\u0275advance(2);
      \u0275\u0275property("ngIf", ctx.description);
    }
  },
  dependencies: [NgIf, NgTemplateOutlet],
  encapsulation: 2,
  changeDetection: 0
});
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TooltipDefinition, [{
    type: Component,
    args: [{
      selector: "cds-tooltip-definition, ibm-tooltip-definition",
      changeDetection: ChangeDetectionStrategy.OnPush,
      template: `
		<button
			class="cds--definition-term"
			[attr.aria-controls]="id"
			[attr.aria-expanded]="isOpen"
			[attr.aria-describedby]="isOpen ? id : null"
			(blur)="onBlur($event)"
			(mousedown)="onClick($event)"
			type="button">
			<ng-content></ng-content>
		</button>
		<span
			*ngIf="description"
			class="cds--popover"
			[id]="id"
			[attr.aria-hidden]="!isOpen"
			role="tooltip"
			(mousedown)="onPopoverMouseDown()"
			(mouseup)="onPopoverMouseUp()">
			<span class="cds--popover-content cds--definition-tooltip" aria-live="polite">
				<ng-container *ngIf="!isTemplate(description)">{{description}}</ng-container>
				<ng-template *ngIf="isTemplate(description)" [ngTemplateOutlet]="description" [ngTemplateOutletContext]="{ $implicit: templateContext }"></ng-template>
				<span *ngIf="autoAlign" class="cds--popover-caret cds--popover--auto-align"></span>
			</span>
			<span *ngIf="!autoAlign" class="cds--popover-caret"></span>
		</span>
	`
    }]
  }], function() {
    return [{
      type: ElementRef
    }, {
      type: NgZone
    }, {
      type: Renderer2
    }, {
      type: ChangeDetectorRef
    }];
  }, {
    id: [{
      type: Input
    }],
    description: [{
      type: Input
    }],
    templateContext: [{
      type: Input
    }],
    openOnHover: [{
      type: Input
    }],
    hostkeys: [{
      type: HostListener,
      args: ["keyup", ["$event"]]
    }],
    mouseleave: [{
      type: HostListener,
      args: ["mouseleave", ["$event"]]
    }],
    mouseenter: [{
      type: HostListener,
      args: ["mouseenter", ["$event"]]
    }],
    onFocus: [{
      type: HostListener,
      args: ["focusin", ["$event"]]
    }]
  });
})();
var TooltipModule = class {
};
TooltipModule.\u0275fac = function TooltipModule_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || TooltipModule)();
};
TooltipModule.\u0275mod = /* @__PURE__ */ \u0275\u0275defineNgModule({
  type: TooltipModule,
  declarations: [Tooltip, TooltipDefinition],
  imports: [CommonModule, PopoverModule],
  exports: [Tooltip, TooltipDefinition]
});
TooltipModule.\u0275inj = /* @__PURE__ */ \u0275\u0275defineInjector({
  imports: [CommonModule, PopoverModule]
});
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TooltipModule, [{
    type: NgModule,
    args: [{
      declarations: [Tooltip, TooltipDefinition],
      exports: [Tooltip, TooltipDefinition],
      imports: [CommonModule, PopoverModule]
    }]
  }], null, null);
})();

// node_modules/carbon-components-angular/fesm2020/carbon-components-angular-button.mjs
var _c03 = ["*"];
var _c13 = ["button"];
var Button = class {
  constructor() {
    this.cdsButton = "primary";
    this.skeleton = false;
    this.iconOnly = false;
    this.isExpressive = false;
    this.baseClass = true;
  }
  /**
   * @deprecated as of v5 - Use `cdsButton` input property instead
   */
  set ibmButton(type) {
    this.cdsButton = type;
  }
  get primaryButton() {
    return this.cdsButton === "primary" || !this.cdsButton;
  }
  get secondaryButton() {
    return this.cdsButton === "secondary";
  }
  get tertiaryButton() {
    return this.cdsButton === "tertiary";
  }
  get ghostButton() {
    return this.cdsButton === "ghost";
  }
  get dangerButton() {
    return this.cdsButton === "danger" || this.cdsButton === "danger--primary";
  }
  get dangerTertiary() {
    return this.cdsButton === "danger--tertiary";
  }
  get dangerGhost() {
    return this.cdsButton === "danger--ghost";
  }
  /**
   * @todo remove `cds--btn--${size}` classes in v12
   */
  get smallSize() {
    return this.size === "sm" && !this.isExpressive;
  }
  get mediumSize() {
    return this.size === "md" && !this.isExpressive;
  }
  get largeSize() {
    return this.size === "lg";
  }
  get extraLargeSize() {
    return this.size === "xl";
  }
  get twoExtraLargeSize() {
    return this.size === "2xl";
  }
  // Size classes
  get smallLayoutSize() {
    return this.size === "sm" && !this.isExpressive;
  }
  get mediumLayoutSize() {
    return this.size === "md" && !this.isExpressive;
  }
  get largeLayoutSize() {
    return this.size === "lg";
  }
  get extraLargeLayoutSize() {
    return this.size === "xl";
  }
  get twoExtraLargeLayoutSize() {
    return this.size === "2xl";
  }
};
Button.\u0275fac = function Button_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || Button)();
};
Button.\u0275dir = /* @__PURE__ */ \u0275\u0275defineDirective({
  type: Button,
  selectors: [["", "cdsButton", ""], ["", "ibmButton", ""]],
  hostVars: 42,
  hostBindings: function Button_HostBindings(rf, ctx) {
    if (rf & 2) {
      \u0275\u0275classProp("cds--skeleton", ctx.skeleton)("cds--btn--icon-only", ctx.iconOnly)("cds--btn--expressive", ctx.isExpressive)("cds--btn", ctx.baseClass)("cds--btn--primary", ctx.primaryButton)("cds--btn--secondary", ctx.secondaryButton)("cds--btn--tertiary", ctx.tertiaryButton)("cds--btn--ghost", ctx.ghostButton)("cds--btn--danger", ctx.dangerButton)("cds--btn--danger--tertiary", ctx.dangerTertiary)("cds--btn--danger--ghost", ctx.dangerGhost)("cds--btn--sm", ctx.smallSize)("cds--btn--md", ctx.mediumSize)("cds--btn--lg", ctx.largeSize)("cds--btn--xl", ctx.extraLargeSize)("cds--btn--2xl", ctx.twoExtraLargeSize)("cds--layout--size-sm", ctx.smallLayoutSize)("cds--layout--size-md", ctx.mediumLayoutSize)("cds--layout--size-lg", ctx.largeLayoutSize)("cds--layout--size-xl", ctx.extraLargeLayoutSize)("cds--layout--size-2xl", ctx.twoExtraLargeLayoutSize);
    }
  },
  inputs: {
    ibmButton: "ibmButton",
    cdsButton: "cdsButton",
    size: "size",
    skeleton: "skeleton",
    iconOnly: "iconOnly",
    isExpressive: "isExpressive"
  },
  standalone: false
});
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(Button, [{
    type: Directive,
    args: [{
      selector: "[cdsButton], [ibmButton]"
    }]
  }], null, {
    ibmButton: [{
      type: Input
    }],
    cdsButton: [{
      type: Input
    }],
    size: [{
      type: Input
    }],
    skeleton: [{
      type: HostBinding,
      args: ["class.cds--skeleton"]
    }, {
      type: Input
    }],
    iconOnly: [{
      type: HostBinding,
      args: ["class.cds--btn--icon-only"]
    }, {
      type: Input
    }],
    isExpressive: [{
      type: HostBinding,
      args: ["class.cds--btn--expressive"]
    }, {
      type: Input
    }],
    baseClass: [{
      type: HostBinding,
      args: ["class.cds--btn"]
    }],
    primaryButton: [{
      type: HostBinding,
      args: ["class.cds--btn--primary"]
    }],
    secondaryButton: [{
      type: HostBinding,
      args: ["class.cds--btn--secondary"]
    }],
    tertiaryButton: [{
      type: HostBinding,
      args: ["class.cds--btn--tertiary"]
    }],
    ghostButton: [{
      type: HostBinding,
      args: ["class.cds--btn--ghost"]
    }],
    dangerButton: [{
      type: HostBinding,
      args: ["class.cds--btn--danger"]
    }],
    dangerTertiary: [{
      type: HostBinding,
      args: ["class.cds--btn--danger--tertiary"]
    }],
    dangerGhost: [{
      type: HostBinding,
      args: ["class.cds--btn--danger--ghost"]
    }],
    smallSize: [{
      type: HostBinding,
      args: ["class.cds--btn--sm"]
    }],
    mediumSize: [{
      type: HostBinding,
      args: ["class.cds--btn--md"]
    }],
    largeSize: [{
      type: HostBinding,
      args: ["class.cds--btn--lg"]
    }],
    extraLargeSize: [{
      type: HostBinding,
      args: ["class.cds--btn--xl"]
    }],
    twoExtraLargeSize: [{
      type: HostBinding,
      args: ["class.cds--btn--2xl"]
    }],
    smallLayoutSize: [{
      type: HostBinding,
      args: ["class.cds--layout--size-sm"]
    }],
    mediumLayoutSize: [{
      type: HostBinding,
      args: ["class.cds--layout--size-md"]
    }],
    largeLayoutSize: [{
      type: HostBinding,
      args: ["class.cds--layout--size-lg"]
    }],
    extraLargeLayoutSize: [{
      type: HostBinding,
      args: ["class.cds--layout--size-xl"]
    }],
    twoExtraLargeLayoutSize: [{
      type: HostBinding,
      args: ["class.cds--layout--size-2xl"]
    }]
  });
})();
var ButtonSet = class {
  constructor() {
    this.buttonSetClass = true;
  }
};
ButtonSet.\u0275fac = function ButtonSet_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || ButtonSet)();
};
ButtonSet.\u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({
  type: ButtonSet,
  selectors: [["cds-button-set"], ["ibm-button-set"]],
  hostVars: 2,
  hostBindings: function ButtonSet_HostBindings(rf, ctx) {
    if (rf & 2) {
      \u0275\u0275classProp("cds--btn-set", ctx.buttonSetClass);
    }
  },
  standalone: false,
  ngContentSelectors: _c03,
  decls: 1,
  vars: 0,
  template: function ButtonSet_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275projectionDef();
      \u0275\u0275projection(0);
    }
  },
  encapsulation: 2
});
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ButtonSet, [{
    type: Component,
    args: [{
      selector: "cds-button-set, ibm-button-set",
      template: "<ng-content></ng-content>"
    }]
  }], null, {
    buttonSetClass: [{
      type: HostBinding,
      args: ["class.cds--btn-set"]
    }]
  });
})();
var BaseIconButton = class {
  constructor() {
    this.caret = true;
    this.dropShadow = true;
    this.highContrast = true;
    this.isOpen = false;
    this.align = "bottom";
    this.autoAlign = false;
    this.enterDelayMs = 100;
    this.leaveDelayMs = 300;
  }
};
BaseIconButton.\u0275fac = function BaseIconButton_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || BaseIconButton)();
};
BaseIconButton.\u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({
  type: BaseIconButton,
  selectors: [["ng-component"]],
  inputs: {
    caret: "caret",
    dropShadow: "dropShadow",
    highContrast: "highContrast",
    isOpen: "isOpen",
    align: "align",
    autoAlign: "autoAlign",
    enterDelayMs: "enterDelayMs",
    leaveDelayMs: "leaveDelayMs"
  },
  standalone: false,
  decls: 0,
  vars: 0,
  template: function BaseIconButton_Template(rf, ctx) {
  },
  encapsulation: 2
});
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(BaseIconButton, [{
    type: Component,
    args: [{
      template: ""
    }]
  }], null, {
    caret: [{
      type: Input
    }],
    dropShadow: [{
      type: Input
    }],
    highContrast: [{
      type: Input
    }],
    isOpen: [{
      type: Input
    }],
    align: [{
      type: Input
    }],
    autoAlign: [{
      type: Input
    }],
    enterDelayMs: [{
      type: Input
    }],
    leaveDelayMs: [{
      type: Input
    }]
  });
})();
var IconButton = class _IconButton extends BaseIconButton {
  constructor(renderer) {
    super();
    this.renderer = renderer;
    this.buttonId = `icon-btn-${_IconButton.iconButtonCounter++}`;
    this.kind = "primary";
    this.size = "lg";
    this.type = "button";
    this.isExpressive = false;
    this.disabled = false;
    this.showTooltipWhenDisabled = false;
    this.click = new EventEmitter();
    this.focus = new EventEmitter();
    this.blur = new EventEmitter();
    this.tooltipClick = new EventEmitter();
    this.classList = {};
    this.attributeList = {};
  }
  /**
   * Pass global carbon classes to icon button
   */
  set buttonNgClass(obj) {
    this.classList = Object.assign({
      "cds--btn--disabled": this.disabled
    }, obj);
  }
  get buttonNgClass() {
    return this.classList;
  }
  /**
   * @param obj: { [key: string]: string
   * User can pass additional button attributes if component property does not already exist
   * Key is the attribute name & value is the attribute value for the button
   */
  set buttonAttributes(obj) {
    if (this.button) {
      Object.keys(this.attributeList).forEach((key) => {
        this.renderer.removeAttribute(this.button.nativeElement, key);
      });
      Object.keys(obj).forEach((key) => {
        this.renderer.setAttribute(this.button.nativeElement, key, obj[key]);
      });
    }
    this.attributeList = obj;
  }
  get buttonAttributes() {
    return this.buttonAttributes;
  }
  ngAfterViewInit() {
    this.buttonAttributes = this.attributeList;
  }
  /**
   * Stop propogation of click event
   * Else double fires (click) event
   */
  emitClickEvent(event, element = "button") {
    event.preventDefault();
    event.stopPropagation();
    if (element === "tooltip") {
      this.tooltipClick.emit(event);
      return;
    }
    this.click.emit(event);
  }
};
IconButton.iconButtonCounter = 0;
IconButton.\u0275fac = function IconButton_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || IconButton)(\u0275\u0275directiveInject(Renderer2));
};
IconButton.\u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({
  type: IconButton,
  selectors: [["cds-icon-button"], ["ibm-icon-button"]],
  viewQuery: function IconButton_Query(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275viewQuery(_c13, 5);
    }
    if (rf & 2) {
      let _t;
      \u0275\u0275queryRefresh(_t = \u0275\u0275loadQuery()) && (ctx.button = _t.first);
    }
  },
  inputs: {
    buttonNgClass: "buttonNgClass",
    buttonAttributes: "buttonAttributes",
    buttonId: "buttonId",
    kind: "kind",
    size: "size",
    type: "type",
    isExpressive: "isExpressive",
    disabled: "disabled",
    description: "description",
    showTooltipWhenDisabled: "showTooltipWhenDisabled"
  },
  outputs: {
    click: "click",
    focus: "focus",
    blur: "blur",
    tooltipClick: "tooltipClick"
  },
  standalone: false,
  features: [\u0275\u0275InheritDefinitionFeature],
  ngContentSelectors: _c03,
  decls: 4,
  vars: 18,
  consts: [["button", ""], [1, "cds--icon-tooltip", 3, "click", "description", "disabled", "caret", "dropShadow", "highContrast", "isOpen", "align", "autoAlign", "enterDelayMs", "leaveDelayMs"], [3, "click", "focus", "blur", "id", "disabled", "iconOnly", "ngClass", "cdsButton", "size", "isExpressive"]],
  template: function IconButton_Template(rf, ctx) {
    if (rf & 1) {
      const _r1 = \u0275\u0275getCurrentView();
      \u0275\u0275projectionDef();
      \u0275\u0275elementStart(0, "cds-tooltip", 1);
      \u0275\u0275listener("click", function IconButton_Template_cds_tooltip_click_0_listener($event) {
        \u0275\u0275restoreView(_r1);
        return \u0275\u0275resetView(ctx.emitClickEvent($event, "tooltip"));
      });
      \u0275\u0275elementStart(1, "button", 2, 0);
      \u0275\u0275listener("click", function IconButton_Template_button_click_1_listener($event) {
        \u0275\u0275restoreView(_r1);
        return \u0275\u0275resetView(ctx.emitClickEvent($event));
      })("focus", function IconButton_Template_button_focus_1_listener($event) {
        \u0275\u0275restoreView(_r1);
        return \u0275\u0275resetView(ctx.focus.emit($event));
      })("blur", function IconButton_Template_button_blur_1_listener($event) {
        \u0275\u0275restoreView(_r1);
        return \u0275\u0275resetView(ctx.blur.emit($event));
      });
      \u0275\u0275projection(3);
      \u0275\u0275elementEnd()();
    }
    if (rf & 2) {
      \u0275\u0275property("description", ctx.description)("disabled", ctx.showTooltipWhenDisabled ? false : ctx.disabled)("caret", ctx.caret)("dropShadow", ctx.dropShadow)("highContrast", ctx.highContrast)("isOpen", ctx.isOpen)("align", ctx.align)("autoAlign", ctx.autoAlign)("enterDelayMs", ctx.enterDelayMs)("leaveDelayMs", ctx.leaveDelayMs);
      \u0275\u0275advance();
      \u0275\u0275property("id", ctx.buttonId)("disabled", ctx.disabled)("iconOnly", true)("ngClass", ctx.buttonNgClass)("cdsButton", ctx.kind)("size", ctx.size)("isExpressive", ctx.isExpressive);
      \u0275\u0275attribute("type", ctx.type);
    }
  },
  dependencies: [NgClass, Tooltip, Button],
  encapsulation: 2
});
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(IconButton, [{
    type: Component,
    args: [{
      selector: "cds-icon-button, ibm-icon-button",
      template: `
	<cds-tooltip
		class="cds--icon-tooltip"
		[description]="description"
		[disabled]="showTooltipWhenDisabled ? false : disabled"
		[caret]="caret"
		[dropShadow]="dropShadow"
		[highContrast]="highContrast"
		[isOpen]="isOpen"
		[align]="align"
		[autoAlign]="autoAlign"
		[enterDelayMs]="enterDelayMs"
		[leaveDelayMs]="leaveDelayMs"
		(click)="emitClickEvent($event, 'tooltip')">
		<button
			#button
			[id]="buttonId"
			[disabled]="disabled"
			[attr.type]="type"
			[iconOnly]="true"
			[ngClass]="buttonNgClass"
			[cdsButton]="kind"
			[size]="size"
			[isExpressive]="isExpressive"
			(click)="emitClickEvent($event)"
			(focus)="focus.emit($event)"
			(blur)="blur.emit($event)">
			<ng-content></ng-content>
		</button>
	</cds-tooltip>
	`
    }]
  }], function() {
    return [{
      type: Renderer2
    }];
  }, {
    buttonNgClass: [{
      type: Input
    }],
    buttonAttributes: [{
      type: Input
    }],
    button: [{
      type: ViewChild,
      args: ["button"]
    }],
    buttonId: [{
      type: Input
    }],
    kind: [{
      type: Input
    }],
    size: [{
      type: Input
    }],
    type: [{
      type: Input
    }],
    isExpressive: [{
      type: Input
    }],
    disabled: [{
      type: Input
    }],
    description: [{
      type: Input
    }],
    showTooltipWhenDisabled: [{
      type: Input
    }],
    click: [{
      type: Output
    }],
    focus: [{
      type: Output
    }],
    blur: [{
      type: Output
    }],
    tooltipClick: [{
      type: Output
    }]
  });
})();
var ButtonModule = class {
};
ButtonModule.\u0275fac = function ButtonModule_Factory(__ngFactoryType__) {
  return new (__ngFactoryType__ || ButtonModule)();
};
ButtonModule.\u0275mod = /* @__PURE__ */ \u0275\u0275defineNgModule({
  type: ButtonModule,
  declarations: [Button, ButtonSet, BaseIconButton, IconButton],
  imports: [CommonModule, TooltipModule],
  exports: [Button, ButtonSet, IconButton]
});
ButtonModule.\u0275inj = /* @__PURE__ */ \u0275\u0275defineInjector({
  imports: [CommonModule, TooltipModule]
});
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ButtonModule, [{
    type: NgModule,
    args: [{
      declarations: [Button, ButtonSet, BaseIconButton, IconButton],
      exports: [Button, ButtonSet, IconButton],
      imports: [CommonModule, TooltipModule]
    }]
  }], null, null);
})();

export {
  AuthService,
  Tooltip,
  TooltipModule,
  Button,
  BaseIconButton,
  ButtonModule
};
//# sourceMappingURL=chunk-HYPWDU4T.js.map
