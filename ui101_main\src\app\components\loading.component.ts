import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';

// Carbon Design System imports
import { LoadingModule } from 'carbon-components-angular/loading';

@Component({
  selector: 'app-loading',
  standalone: true,
  imports: [
    CommonModule,
    LoadingModule
  ],
  template: `
    <div class="loading-container">
      <div class="loading-content">
        <ibm-loading size="normal"></ibm-loading>
        <p class="loading-text">Loading...</p>
      </div>
    </div>
  `,
  styles: [`
    .loading-container {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(255, 255, 255, 0.9);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 9999;
    }
    
    .loading-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 1rem;
    }
    
    .loading-text {
      margin: 0;
      font-size: 1rem;
      color: #525252;
    }
  `]
})
export class LoadingComponent {}
