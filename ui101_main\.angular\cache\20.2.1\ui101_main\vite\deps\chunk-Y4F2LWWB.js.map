{"version": 3, "sources": ["../../../../../../node_modules/@angular/fire/node_modules/@firebase/util/dist/postinstall.mjs", "../../../../../../node_modules/@angular/fire/node_modules/@firebase/util/dist/index.esm2017.js", "../../../../../../node_modules/@angular/fire/node_modules/@firebase/component/dist/esm/index.esm2017.js", "../../../../../../node_modules/@angular/fire/node_modules/@firebase/logger/dist/esm/index.esm2017.js", "../../../../../../node_modules/idb/build/wrap-idb-value.js", "../../../../../../node_modules/idb/build/index.js", "../../../../../../node_modules/@angular/fire/node_modules/@firebase/app/dist/esm/index.esm2017.js", "../../../../../../node_modules/@angular/fire/node_modules/firebase/app/dist/esm/index.esm.js", "../../../../../../node_modules/@angular/core/fesm2022/rxjs-interop.mjs", "../../../../../../node_modules/@angular/fire/fesm2022/angular-fire.mjs", "../../../../../../node_modules/@angular/fire/fesm2022/angular-fire-app.mjs"], "sourcesContent": ["const getDefaultsFromPostinstall = () => (undefined);\nexport { getDefaultsFromPostinstall };", "import { getDefaultsFromPostinstall } from './postinstall.mjs';\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @fileoverview Firebase constants.  Some of these (@defines) can be overridden at compile-time.\n */\nconst CONSTANTS = {\n    /**\n     * @define {boolean} Whether this is the client Node.js SDK.\n     */\n    NODE_CLIENT: false,\n    /**\n     * @define {boolean} Whether this is the Admin Node.js SDK.\n     */\n    NODE_ADMIN: false,\n    /**\n     * Firebase SDK Version\n     */\n    SDK_VERSION: '${JSCORE_VERSION}'\n};\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Throws an error if the provided assertion is falsy\n */\nconst assert = function (assertion, message) {\n    if (!assertion) {\n        throw assertionError(message);\n    }\n};\n/**\n * Returns an Error object suitable for throwing.\n */\nconst assertionError = function (message) {\n    return new Error('Firebase Database (' +\n        CONSTANTS.SDK_VERSION +\n        ') INTERNAL ASSERT FAILED: ' +\n        message);\n};\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst stringToByteArray$1 = function (str) {\n    // TODO(user): Use native implementations if/when available\n    const out = [];\n    let p = 0;\n    for (let i = 0; i < str.length; i++) {\n        let c = str.charCodeAt(i);\n        if (c < 128) {\n            out[p++] = c;\n        }\n        else if (c < 2048) {\n            out[p++] = (c >> 6) | 192;\n            out[p++] = (c & 63) | 128;\n        }\n        else if ((c & 0xfc00) === 0xd800 &&\n            i + 1 < str.length &&\n            (str.charCodeAt(i + 1) & 0xfc00) === 0xdc00) {\n            // Surrogate Pair\n            c = 0x10000 + ((c & 0x03ff) << 10) + (str.charCodeAt(++i) & 0x03ff);\n            out[p++] = (c >> 18) | 240;\n            out[p++] = ((c >> 12) & 63) | 128;\n            out[p++] = ((c >> 6) & 63) | 128;\n            out[p++] = (c & 63) | 128;\n        }\n        else {\n            out[p++] = (c >> 12) | 224;\n            out[p++] = ((c >> 6) & 63) | 128;\n            out[p++] = (c & 63) | 128;\n        }\n    }\n    return out;\n};\n/**\n * Turns an array of numbers into the string given by the concatenation of the\n * characters to which the numbers correspond.\n * @param bytes Array of numbers representing characters.\n * @return Stringification of the array.\n */\nconst byteArrayToString = function (bytes) {\n    // TODO(user): Use native implementations if/when available\n    const out = [];\n    let pos = 0, c = 0;\n    while (pos < bytes.length) {\n        const c1 = bytes[pos++];\n        if (c1 < 128) {\n            out[c++] = String.fromCharCode(c1);\n        }\n        else if (c1 > 191 && c1 < 224) {\n            const c2 = bytes[pos++];\n            out[c++] = String.fromCharCode(((c1 & 31) << 6) | (c2 & 63));\n        }\n        else if (c1 > 239 && c1 < 365) {\n            // Surrogate Pair\n            const c2 = bytes[pos++];\n            const c3 = bytes[pos++];\n            const c4 = bytes[pos++];\n            const u = (((c1 & 7) << 18) | ((c2 & 63) << 12) | ((c3 & 63) << 6) | (c4 & 63)) -\n                0x10000;\n            out[c++] = String.fromCharCode(0xd800 + (u >> 10));\n            out[c++] = String.fromCharCode(0xdc00 + (u & 1023));\n        }\n        else {\n            const c2 = bytes[pos++];\n            const c3 = bytes[pos++];\n            out[c++] = String.fromCharCode(((c1 & 15) << 12) | ((c2 & 63) << 6) | (c3 & 63));\n        }\n    }\n    return out.join('');\n};\n// We define it as an object literal instead of a class because a class compiled down to es5 can't\n// be treeshaked. https://github.com/rollup/rollup/issues/1691\n// Static lookup maps, lazily populated by init_()\n// TODO(dlarocque): Define this as a class, since we no longer target ES5.\nconst base64 = {\n    /**\n     * Maps bytes to characters.\n     */\n    byteToCharMap_: null,\n    /**\n     * Maps characters to bytes.\n     */\n    charToByteMap_: null,\n    /**\n     * Maps bytes to websafe characters.\n     * @private\n     */\n    byteToCharMapWebSafe_: null,\n    /**\n     * Maps websafe characters to bytes.\n     * @private\n     */\n    charToByteMapWebSafe_: null,\n    /**\n     * Our default alphabet, shared between\n     * ENCODED_VALS and ENCODED_VALS_WEBSAFE\n     */\n    ENCODED_VALS_BASE: 'ABCDEFGHIJKLMNOPQRSTUVWXYZ' + 'abcdefghijklmnopqrstuvwxyz' + '0123456789',\n    /**\n     * Our default alphabet. Value 64 (=) is special; it means \"nothing.\"\n     */\n    get ENCODED_VALS() {\n        return this.ENCODED_VALS_BASE + '+/=';\n    },\n    /**\n     * Our websafe alphabet.\n     */\n    get ENCODED_VALS_WEBSAFE() {\n        return this.ENCODED_VALS_BASE + '-_.';\n    },\n    /**\n     * Whether this browser supports the atob and btoa functions. This extension\n     * started at Mozilla but is now implemented by many browsers. We use the\n     * ASSUME_* variables to avoid pulling in the full useragent detection library\n     * but still allowing the standard per-browser compilations.\n     *\n     */\n    HAS_NATIVE_SUPPORT: typeof atob === 'function',\n    /**\n     * Base64-encode an array of bytes.\n     *\n     * @param input An array of bytes (numbers with\n     *     value in [0, 255]) to encode.\n     * @param webSafe Boolean indicating we should use the\n     *     alternative alphabet.\n     * @return The base64 encoded string.\n     */\n    encodeByteArray(input, webSafe) {\n        if (!Array.isArray(input)) {\n            throw Error('encodeByteArray takes an array as a parameter');\n        }\n        this.init_();\n        const byteToCharMap = webSafe\n            ? this.byteToCharMapWebSafe_\n            : this.byteToCharMap_;\n        const output = [];\n        for (let i = 0; i < input.length; i += 3) {\n            const byte1 = input[i];\n            const haveByte2 = i + 1 < input.length;\n            const byte2 = haveByte2 ? input[i + 1] : 0;\n            const haveByte3 = i + 2 < input.length;\n            const byte3 = haveByte3 ? input[i + 2] : 0;\n            const outByte1 = byte1 >> 2;\n            const outByte2 = ((byte1 & 0x03) << 4) | (byte2 >> 4);\n            let outByte3 = ((byte2 & 0x0f) << 2) | (byte3 >> 6);\n            let outByte4 = byte3 & 0x3f;\n            if (!haveByte3) {\n                outByte4 = 64;\n                if (!haveByte2) {\n                    outByte3 = 64;\n                }\n            }\n            output.push(byteToCharMap[outByte1], byteToCharMap[outByte2], byteToCharMap[outByte3], byteToCharMap[outByte4]);\n        }\n        return output.join('');\n    },\n    /**\n     * Base64-encode a string.\n     *\n     * @param input A string to encode.\n     * @param webSafe If true, we should use the\n     *     alternative alphabet.\n     * @return The base64 encoded string.\n     */\n    encodeString(input, webSafe) {\n        // Shortcut for Mozilla browsers that implement\n        // a native base64 encoder in the form of \"btoa/atob\"\n        if (this.HAS_NATIVE_SUPPORT && !webSafe) {\n            return btoa(input);\n        }\n        return this.encodeByteArray(stringToByteArray$1(input), webSafe);\n    },\n    /**\n     * Base64-decode a string.\n     *\n     * @param input to decode.\n     * @param webSafe True if we should use the\n     *     alternative alphabet.\n     * @return string representing the decoded value.\n     */\n    decodeString(input, webSafe) {\n        // Shortcut for Mozilla browsers that implement\n        // a native base64 encoder in the form of \"btoa/atob\"\n        if (this.HAS_NATIVE_SUPPORT && !webSafe) {\n            return atob(input);\n        }\n        return byteArrayToString(this.decodeStringToByteArray(input, webSafe));\n    },\n    /**\n     * Base64-decode a string.\n     *\n     * In base-64 decoding, groups of four characters are converted into three\n     * bytes.  If the encoder did not apply padding, the input length may not\n     * be a multiple of 4.\n     *\n     * In this case, the last group will have fewer than 4 characters, and\n     * padding will be inferred.  If the group has one or two characters, it decodes\n     * to one byte.  If the group has three characters, it decodes to two bytes.\n     *\n     * @param input Input to decode.\n     * @param webSafe True if we should use the web-safe alphabet.\n     * @return bytes representing the decoded value.\n     */\n    decodeStringToByteArray(input, webSafe) {\n        this.init_();\n        const charToByteMap = webSafe\n            ? this.charToByteMapWebSafe_\n            : this.charToByteMap_;\n        const output = [];\n        for (let i = 0; i < input.length;) {\n            const byte1 = charToByteMap[input.charAt(i++)];\n            const haveByte2 = i < input.length;\n            const byte2 = haveByte2 ? charToByteMap[input.charAt(i)] : 0;\n            ++i;\n            const haveByte3 = i < input.length;\n            const byte3 = haveByte3 ? charToByteMap[input.charAt(i)] : 64;\n            ++i;\n            const haveByte4 = i < input.length;\n            const byte4 = haveByte4 ? charToByteMap[input.charAt(i)] : 64;\n            ++i;\n            if (byte1 == null || byte2 == null || byte3 == null || byte4 == null) {\n                throw new DecodeBase64StringError();\n            }\n            const outByte1 = (byte1 << 2) | (byte2 >> 4);\n            output.push(outByte1);\n            if (byte3 !== 64) {\n                const outByte2 = ((byte2 << 4) & 0xf0) | (byte3 >> 2);\n                output.push(outByte2);\n                if (byte4 !== 64) {\n                    const outByte3 = ((byte3 << 6) & 0xc0) | byte4;\n                    output.push(outByte3);\n                }\n            }\n        }\n        return output;\n    },\n    /**\n     * Lazy static initialization function. Called before\n     * accessing any of the static map variables.\n     * @private\n     */\n    init_() {\n        if (!this.byteToCharMap_) {\n            this.byteToCharMap_ = {};\n            this.charToByteMap_ = {};\n            this.byteToCharMapWebSafe_ = {};\n            this.charToByteMapWebSafe_ = {};\n            // We want quick mappings back and forth, so we precompute two maps.\n            for (let i = 0; i < this.ENCODED_VALS.length; i++) {\n                this.byteToCharMap_[i] = this.ENCODED_VALS.charAt(i);\n                this.charToByteMap_[this.byteToCharMap_[i]] = i;\n                this.byteToCharMapWebSafe_[i] = this.ENCODED_VALS_WEBSAFE.charAt(i);\n                this.charToByteMapWebSafe_[this.byteToCharMapWebSafe_[i]] = i;\n                // Be forgiving when decoding and correctly decode both encodings.\n                if (i >= this.ENCODED_VALS_BASE.length) {\n                    this.charToByteMap_[this.ENCODED_VALS_WEBSAFE.charAt(i)] = i;\n                    this.charToByteMapWebSafe_[this.ENCODED_VALS.charAt(i)] = i;\n                }\n            }\n        }\n    }\n};\n/**\n * An error encountered while decoding base64 string.\n */\nclass DecodeBase64StringError extends Error {\n    constructor() {\n        super(...arguments);\n        this.name = 'DecodeBase64StringError';\n    }\n}\n/**\n * URL-safe base64 encoding\n */\nconst base64Encode = function (str) {\n    const utf8Bytes = stringToByteArray$1(str);\n    return base64.encodeByteArray(utf8Bytes, true);\n};\n/**\n * URL-safe base64 encoding (without \".\" padding in the end).\n * e.g. Used in JSON Web Token (JWT) parts.\n */\nconst base64urlEncodeWithoutPadding = function (str) {\n    // Use base64url encoding and remove padding in the end (dot characters).\n    return base64Encode(str).replace(/\\./g, '');\n};\n/**\n * URL-safe base64 decoding\n *\n * NOTE: DO NOT use the global atob() function - it does NOT support the\n * base64Url variant encoding.\n *\n * @param str To be decoded\n * @return Decoded result, if possible\n */\nconst base64Decode = function (str) {\n    try {\n        return base64.decodeString(str, true);\n    }\n    catch (e) {\n        console.error('base64Decode failed: ', e);\n    }\n    return null;\n};\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Do a deep-copy of basic JavaScript Objects or Arrays.\n */\nfunction deepCopy(value) {\n    return deepExtend(undefined, value);\n}\n/**\n * Copy properties from source to target (recursively allows extension\n * of Objects and Arrays).  Scalar values in the target are over-written.\n * If target is undefined, an object of the appropriate type will be created\n * (and returned).\n *\n * We recursively copy all child properties of plain Objects in the source- so\n * that namespace- like dictionaries are merged.\n *\n * Note that the target can be a function, in which case the properties in\n * the source Object are copied onto it as static properties of the Function.\n *\n * Note: we don't merge __proto__ to prevent prototype pollution\n */\nfunction deepExtend(target, source) {\n    if (!(source instanceof Object)) {\n        return source;\n    }\n    switch (source.constructor) {\n        case Date:\n            // Treat Dates like scalars; if the target date object had any child\n            // properties - they will be lost!\n            const dateValue = source;\n            return new Date(dateValue.getTime());\n        case Object:\n            if (target === undefined) {\n                target = {};\n            }\n            break;\n        case Array:\n            // Always copy the array source and overwrite the target.\n            target = [];\n            break;\n        default:\n            // Not a plain Object - treat it as a scalar.\n            return source;\n    }\n    for (const prop in source) {\n        // use isValidKey to guard against prototype pollution. See https://snyk.io/vuln/SNYK-JS-LODASH-450202\n        if (!source.hasOwnProperty(prop) || !isValidKey(prop)) {\n            continue;\n        }\n        target[prop] = deepExtend(target[prop], source[prop]);\n    }\n    return target;\n}\nfunction isValidKey(key) {\n    return key !== '__proto__';\n}\n\n/**\n * @license\n * Copyright 2022 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Polyfill for `globalThis` object.\n * @returns the `globalThis` object for the given environment.\n * @public\n */\nfunction getGlobal() {\n    if (typeof self !== 'undefined') {\n        return self;\n    }\n    if (typeof window !== 'undefined') {\n        return window;\n    }\n    if (typeof global !== 'undefined') {\n        return global;\n    }\n    throw new Error('Unable to locate global object.');\n}\n\n/**\n * @license\n * Copyright 2022 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst getDefaultsFromGlobal = () => getGlobal().__FIREBASE_DEFAULTS__;\n/**\n * Attempt to read defaults from a JSON string provided to\n * process(.)env(.)__FIREBASE_DEFAULTS__ or a JSON file whose path is in\n * process(.)env(.)__FIREBASE_DEFAULTS_PATH__\n * The dots are in parens because certain compilers (Vite?) cannot\n * handle seeing that variable in comments.\n * See https://github.com/firebase/firebase-js-sdk/issues/6838\n */\nconst getDefaultsFromEnvVariable = () => {\n    if (typeof process === 'undefined' || typeof process.env === 'undefined') {\n        return;\n    }\n    const defaultsJsonString = process.env.__FIREBASE_DEFAULTS__;\n    if (defaultsJsonString) {\n        return JSON.parse(defaultsJsonString);\n    }\n};\nconst getDefaultsFromCookie = () => {\n    if (typeof document === 'undefined') {\n        return;\n    }\n    let match;\n    try {\n        match = document.cookie.match(/__FIREBASE_DEFAULTS__=([^;]+)/);\n    }\n    catch (e) {\n        // Some environments such as Angular Universal SSR have a\n        // `document` object but error on accessing `document.cookie`.\n        return;\n    }\n    const decoded = match && base64Decode(match[1]);\n    return decoded && JSON.parse(decoded);\n};\n/**\n * Get the __FIREBASE_DEFAULTS__ object. It checks in order:\n * (1) if such an object exists as a property of `globalThis`\n * (2) if such an object was provided on a shell environment variable\n * (3) if such an object exists in a cookie\n * @public\n */\nconst getDefaults = () => {\n    try {\n        return (getDefaultsFromPostinstall() ||\n            getDefaultsFromGlobal() ||\n            getDefaultsFromEnvVariable() ||\n            getDefaultsFromCookie());\n    }\n    catch (e) {\n        /**\n         * Catch-all for being unable to get __FIREBASE_DEFAULTS__ due\n         * to any environment case we have not accounted for. Log to\n         * info instead of swallowing so we can find these unknown cases\n         * and add paths for them if needed.\n         */\n        console.info(`Unable to get __FIREBASE_DEFAULTS__ due to: ${e}`);\n        return;\n    }\n};\n/**\n * Returns emulator host stored in the __FIREBASE_DEFAULTS__ object\n * for the given product.\n * @returns a URL host formatted like `127.0.0.1:9999` or `[::1]:4000` if available\n * @public\n */\nconst getDefaultEmulatorHost = (productName) => { var _a, _b; return (_b = (_a = getDefaults()) === null || _a === void 0 ? void 0 : _a.emulatorHosts) === null || _b === void 0 ? void 0 : _b[productName]; };\n/**\n * Returns emulator hostname and port stored in the __FIREBASE_DEFAULTS__ object\n * for the given product.\n * @returns a pair of hostname and port like `[\"::1\", 4000]` if available\n * @public\n */\nconst getDefaultEmulatorHostnameAndPort = (productName) => {\n    const host = getDefaultEmulatorHost(productName);\n    if (!host) {\n        return undefined;\n    }\n    const separatorIndex = host.lastIndexOf(':'); // Finding the last since IPv6 addr also has colons.\n    if (separatorIndex <= 0 || separatorIndex + 1 === host.length) {\n        throw new Error(`Invalid host ${host} with no separate hostname and port!`);\n    }\n    // eslint-disable-next-line no-restricted-globals\n    const port = parseInt(host.substring(separatorIndex + 1), 10);\n    if (host[0] === '[') {\n        // Bracket-quoted `[ipv6addr]:port` => return \"ipv6addr\" (without brackets).\n        return [host.substring(1, separatorIndex - 1), port];\n    }\n    else {\n        return [host.substring(0, separatorIndex), port];\n    }\n};\n/**\n * Returns Firebase app config stored in the __FIREBASE_DEFAULTS__ object.\n * @public\n */\nconst getDefaultAppConfig = () => { var _a; return (_a = getDefaults()) === null || _a === void 0 ? void 0 : _a.config; };\n/**\n * Returns an experimental setting on the __FIREBASE_DEFAULTS__ object (properties\n * prefixed by \"_\")\n * @public\n */\nconst getExperimentalSetting = (name) => { var _a; return (_a = getDefaults()) === null || _a === void 0 ? void 0 : _a[`_${name}`]; };\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nclass Deferred {\n    constructor() {\n        this.reject = () => { };\n        this.resolve = () => { };\n        this.promise = new Promise((resolve, reject) => {\n            this.resolve = resolve;\n            this.reject = reject;\n        });\n    }\n    /**\n     * Our API internals are not promisified and cannot because our callback APIs have subtle expectations around\n     * invoking promises inline, which Promises are forbidden to do. This method accepts an optional node-style callback\n     * and returns a node-style callback which will resolve or reject the Deferred's promise.\n     */\n    wrapCallback(callback) {\n        return (error, value) => {\n            if (error) {\n                this.reject(error);\n            }\n            else {\n                this.resolve(value);\n            }\n            if (typeof callback === 'function') {\n                // Attaching noop handler just in case developer wasn't expecting\n                // promises\n                this.promise.catch(() => { });\n                // Some of our callbacks don't expect a value and our own tests\n                // assert that the parameter length is 1\n                if (callback.length === 1) {\n                    callback(error);\n                }\n                else {\n                    callback(error, value);\n                }\n            }\n        };\n    }\n}\n\n/**\n * @license\n * Copyright 2025 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Checks whether host is a cloud workstation or not.\n * @public\n */\nfunction isCloudWorkstation(url) {\n    // `isCloudWorkstation` is called without protocol in certain connect*Emulator functions\n    // In HTTP request builders, it's called with the protocol.\n    // If called with protocol prefix, it's a valid URL, so we extract the hostname\n    // If called without, we assume the string is the hostname.\n    try {\n        const host = url.startsWith('http://') || url.startsWith('https://')\n            ? new URL(url).hostname\n            : url;\n        return host.endsWith('.cloudworkstations.dev');\n    }\n    catch (_a) {\n        return false;\n    }\n}\n/**\n * Makes a fetch request to the given server.\n * Mostly used for forwarding cookies in Firebase Studio.\n * @public\n */\nasync function pingServer(endpoint) {\n    const result = await fetch(endpoint, {\n        credentials: 'include'\n    });\n    return result.ok;\n}\n\n/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nfunction createMockUserToken(token, projectId) {\n    if (token.uid) {\n        throw new Error('The \"uid\" field is no longer supported by mockUserToken. Please use \"sub\" instead for Firebase Auth User ID.');\n    }\n    // Unsecured JWTs use \"none\" as the algorithm.\n    const header = {\n        alg: 'none',\n        type: 'JWT'\n    };\n    const project = projectId || 'demo-project';\n    const iat = token.iat || 0;\n    const sub = token.sub || token.user_id;\n    if (!sub) {\n        throw new Error(\"mockUserToken must contain 'sub' or 'user_id' field!\");\n    }\n    const payload = Object.assign({ \n        // Set all required fields to decent defaults\n        iss: `https://securetoken.google.com/${project}`, aud: project, iat, exp: iat + 3600, auth_time: iat, sub, user_id: sub, firebase: {\n            sign_in_provider: 'custom',\n            identities: {}\n        } }, token);\n    // Unsecured JWTs use the empty string as a signature.\n    const signature = '';\n    return [\n        base64urlEncodeWithoutPadding(JSON.stringify(header)),\n        base64urlEncodeWithoutPadding(JSON.stringify(payload)),\n        signature\n    ].join('.');\n}\nconst emulatorStatus = {};\n// Checks whether any products are running on an emulator\nfunction getEmulatorSummary() {\n    const summary = {\n        prod: [],\n        emulator: []\n    };\n    for (const key of Object.keys(emulatorStatus)) {\n        if (emulatorStatus[key]) {\n            summary.emulator.push(key);\n        }\n        else {\n            summary.prod.push(key);\n        }\n    }\n    return summary;\n}\nfunction getOrCreateEl(id) {\n    let parentDiv = document.getElementById(id);\n    let created = false;\n    if (!parentDiv) {\n        parentDiv = document.createElement('div');\n        parentDiv.setAttribute('id', id);\n        created = true;\n    }\n    return { created, element: parentDiv };\n}\nlet previouslyDismissed = false;\n/**\n * Updates Emulator Banner. Primarily used for Firebase Studio\n * @param name\n * @param isRunningEmulator\n * @public\n */\nfunction updateEmulatorBanner(name, isRunningEmulator) {\n    if (typeof window === 'undefined' ||\n        typeof document === 'undefined' ||\n        !isCloudWorkstation(window.location.host) ||\n        emulatorStatus[name] === isRunningEmulator ||\n        emulatorStatus[name] || // If already set to use emulator, can't go back to prod.\n        previouslyDismissed) {\n        return;\n    }\n    emulatorStatus[name] = isRunningEmulator;\n    function prefixedId(id) {\n        return `__firebase__banner__${id}`;\n    }\n    const bannerId = '__firebase__banner';\n    const summary = getEmulatorSummary();\n    const showError = summary.prod.length > 0;\n    function tearDown() {\n        const element = document.getElementById(bannerId);\n        if (element) {\n            element.remove();\n        }\n    }\n    function setupBannerStyles(bannerEl) {\n        bannerEl.style.display = 'flex';\n        bannerEl.style.background = '#7faaf0';\n        bannerEl.style.position = 'fixed';\n        bannerEl.style.bottom = '5px';\n        bannerEl.style.left = '5px';\n        bannerEl.style.padding = '.5em';\n        bannerEl.style.borderRadius = '5px';\n        bannerEl.style.alignItems = 'center';\n    }\n    function setupIconStyles(prependIcon, iconId) {\n        prependIcon.setAttribute('width', '24');\n        prependIcon.setAttribute('id', iconId);\n        prependIcon.setAttribute('height', '24');\n        prependIcon.setAttribute('viewBox', '0 0 24 24');\n        prependIcon.setAttribute('fill', 'none');\n        prependIcon.style.marginLeft = '-6px';\n    }\n    function setupCloseBtn() {\n        const closeBtn = document.createElement('span');\n        closeBtn.style.cursor = 'pointer';\n        closeBtn.style.marginLeft = '16px';\n        closeBtn.style.fontSize = '24px';\n        closeBtn.innerHTML = ' &times;';\n        closeBtn.onclick = () => {\n            previouslyDismissed = true;\n            tearDown();\n        };\n        return closeBtn;\n    }\n    function setupLinkStyles(learnMoreLink, learnMoreId) {\n        learnMoreLink.setAttribute('id', learnMoreId);\n        learnMoreLink.innerText = 'Learn more';\n        learnMoreLink.href =\n            'https://firebase.google.com/docs/studio/preview-apps#preview-backend';\n        learnMoreLink.setAttribute('target', '__blank');\n        learnMoreLink.style.paddingLeft = '5px';\n        learnMoreLink.style.textDecoration = 'underline';\n    }\n    function setupDom() {\n        const banner = getOrCreateEl(bannerId);\n        const firebaseTextId = prefixedId('text');\n        const firebaseText = document.getElementById(firebaseTextId) || document.createElement('span');\n        const learnMoreId = prefixedId('learnmore');\n        const learnMoreLink = document.getElementById(learnMoreId) ||\n            document.createElement('a');\n        const prependIconId = prefixedId('preprendIcon');\n        const prependIcon = document.getElementById(prependIconId) ||\n            document.createElementNS('http://www.w3.org/2000/svg', 'svg');\n        if (banner.created) {\n            // update styles\n            const bannerEl = banner.element;\n            setupBannerStyles(bannerEl);\n            setupLinkStyles(learnMoreLink, learnMoreId);\n            const closeBtn = setupCloseBtn();\n            setupIconStyles(prependIcon, prependIconId);\n            bannerEl.append(prependIcon, firebaseText, learnMoreLink, closeBtn);\n            document.body.appendChild(bannerEl);\n        }\n        if (showError) {\n            firebaseText.innerText = `Preview backend disconnected.`;\n            prependIcon.innerHTML = `<g clip-path=\"url(#clip0_6013_33858)\">\n<path d=\"M4.8 17.6L12 5.6L19.2 17.6H4.8ZM6.91667 16.4H17.0833L12 7.93333L6.91667 16.4ZM12 15.6C12.1667 15.6 12.3056 15.5444 12.4167 15.4333C12.5389 15.3111 12.6 15.1667 12.6 15C12.6 14.8333 12.5389 14.6944 12.4167 14.5833C12.3056 14.4611 12.1667 14.4 12 14.4C11.8333 14.4 11.6889 14.4611 11.5667 14.5833C11.4556 14.6944 11.4 14.8333 11.4 15C11.4 15.1667 11.4556 15.3111 11.5667 15.4333C11.6889 15.5444 11.8333 15.6 12 15.6ZM11.4 13.6H12.6V10.4H11.4V13.6Z\" fill=\"#212121\"/>\n</g>\n<defs>\n<clipPath id=\"clip0_6013_33858\">\n<rect width=\"24\" height=\"24\" fill=\"white\"/>\n</clipPath>\n</defs>`;\n        }\n        else {\n            prependIcon.innerHTML = `<g clip-path=\"url(#clip0_6083_34804)\">\n<path d=\"M11.4 15.2H12.6V11.2H11.4V15.2ZM12 10C12.1667 10 12.3056 9.94444 12.4167 9.83333C12.5389 9.71111 12.6 9.56667 12.6 9.4C12.6 9.23333 12.5389 9.09444 12.4167 8.98333C12.3056 8.86111 12.1667 8.8 12 8.8C11.8333 8.8 11.6889 8.86111 11.5667 8.98333C11.4556 9.09444 11.4 9.23333 11.4 9.4C11.4 9.56667 11.4556 9.71111 11.5667 9.83333C11.6889 9.94444 11.8333 10 12 10ZM12 18.4C11.1222 18.4 10.2944 18.2333 9.51667 17.9C8.73889 17.5667 8.05556 17.1111 7.46667 16.5333C6.88889 15.9444 6.43333 15.2611 6.1 14.4833C5.76667 13.7056 5.6 12.8778 5.6 12C5.6 11.1111 5.76667 10.2833 6.1 9.51667C6.43333 8.73889 6.88889 8.06111 7.46667 7.48333C8.05556 6.89444 8.73889 6.43333 9.51667 6.1C10.2944 5.76667 11.1222 5.6 12 5.6C12.8889 5.6 13.7167 5.76667 14.4833 6.1C15.2611 6.43333 15.9389 6.89444 16.5167 7.48333C17.1056 8.06111 17.5667 8.73889 17.9 9.51667C18.2333 10.2833 18.4 11.1111 18.4 12C18.4 12.8778 18.2333 13.7056 17.9 14.4833C17.5667 15.2611 17.1056 15.9444 16.5167 16.5333C15.9389 17.1111 15.2611 17.5667 14.4833 17.9C13.7167 18.2333 12.8889 18.4 12 18.4ZM12 17.2C13.4444 17.2 14.6722 16.6944 15.6833 15.6833C16.6944 14.6722 17.2 13.4444 17.2 12C17.2 10.5556 16.6944 9.32778 15.6833 8.31667C14.6722 7.30555 13.4444 6.8 12 6.8C10.5556 6.8 9.32778 7.30555 8.31667 8.31667C7.30556 9.32778 6.8 10.5556 6.8 12C6.8 13.4444 7.30556 14.6722 8.31667 15.6833C9.32778 16.6944 10.5556 17.2 12 17.2Z\" fill=\"#212121\"/>\n</g>\n<defs>\n<clipPath id=\"clip0_6083_34804\">\n<rect width=\"24\" height=\"24\" fill=\"white\"/>\n</clipPath>\n</defs>`;\n            firebaseText.innerText = 'Preview backend running in this workspace.';\n        }\n        firebaseText.setAttribute('id', firebaseTextId);\n    }\n    if (document.readyState === 'loading') {\n        window.addEventListener('DOMContentLoaded', setupDom);\n    }\n    else {\n        setupDom();\n    }\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Returns navigator.userAgent string or '' if it's not defined.\n * @return user agent string\n */\nfunction getUA() {\n    if (typeof navigator !== 'undefined' &&\n        typeof navigator['userAgent'] === 'string') {\n        return navigator['userAgent'];\n    }\n    else {\n        return '';\n    }\n}\n/**\n * Detect Cordova / PhoneGap / Ionic frameworks on a mobile device.\n *\n * Deliberately does not rely on checking `file://` URLs (as this fails PhoneGap\n * in the Ripple emulator) nor Cordova `onDeviceReady`, which would normally\n * wait for a callback.\n */\nfunction isMobileCordova() {\n    return (typeof window !== 'undefined' &&\n        // @ts-ignore Setting up an broadly applicable index signature for Window\n        // just to deal with this case would probably be a bad idea.\n        !!(window['cordova'] || window['phonegap'] || window['PhoneGap']) &&\n        /ios|iphone|ipod|ipad|android|blackberry|iemobile/i.test(getUA()));\n}\n/**\n * Detect Node.js.\n *\n * @return true if Node.js environment is detected or specified.\n */\n// Node detection logic from: https://github.com/iliakan/detect-node/\nfunction isNode() {\n    var _a;\n    const forceEnvironment = (_a = getDefaults()) === null || _a === void 0 ? void 0 : _a.forceEnvironment;\n    if (forceEnvironment === 'node') {\n        return true;\n    }\n    else if (forceEnvironment === 'browser') {\n        return false;\n    }\n    try {\n        return (Object.prototype.toString.call(global.process) === '[object process]');\n    }\n    catch (e) {\n        return false;\n    }\n}\n/**\n * Detect Browser Environment.\n * Note: This will return true for certain test frameworks that are incompletely\n * mimicking a browser, and should not lead to assuming all browser APIs are\n * available.\n */\nfunction isBrowser() {\n    return typeof window !== 'undefined' || isWebWorker();\n}\n/**\n * Detect Web Worker context.\n */\nfunction isWebWorker() {\n    return (typeof WorkerGlobalScope !== 'undefined' &&\n        typeof self !== 'undefined' &&\n        self instanceof WorkerGlobalScope);\n}\n/**\n * Detect Cloudflare Worker context.\n */\nfunction isCloudflareWorker() {\n    return (typeof navigator !== 'undefined' &&\n        navigator.userAgent === 'Cloudflare-Workers');\n}\nfunction isBrowserExtension() {\n    const runtime = typeof chrome === 'object'\n        ? chrome.runtime\n        : typeof browser === 'object'\n            ? browser.runtime\n            : undefined;\n    return typeof runtime === 'object' && runtime.id !== undefined;\n}\n/**\n * Detect React Native.\n *\n * @return true if ReactNative environment is detected.\n */\nfunction isReactNative() {\n    return (typeof navigator === 'object' && navigator['product'] === 'ReactNative');\n}\n/** Detects Electron apps. */\nfunction isElectron() {\n    return getUA().indexOf('Electron/') >= 0;\n}\n/** Detects Internet Explorer. */\nfunction isIE() {\n    const ua = getUA();\n    return ua.indexOf('MSIE ') >= 0 || ua.indexOf('Trident/') >= 0;\n}\n/** Detects Universal Windows Platform apps. */\nfunction isUWP() {\n    return getUA().indexOf('MSAppHost/') >= 0;\n}\n/**\n * Detect whether the current SDK build is the Node version.\n *\n * @return true if it's the Node SDK build.\n */\nfunction isNodeSdk() {\n    return CONSTANTS.NODE_CLIENT === true || CONSTANTS.NODE_ADMIN === true;\n}\n/** Returns true if we are running in Safari. */\nfunction isSafari() {\n    return (!isNode() &&\n        !!navigator.userAgent &&\n        navigator.userAgent.includes('Safari') &&\n        !navigator.userAgent.includes('Chrome'));\n}\n/** Returns true if we are running in Safari or WebKit */\nfunction isSafariOrWebkit() {\n    return (!isNode() &&\n        !!navigator.userAgent &&\n        (navigator.userAgent.includes('Safari') ||\n            navigator.userAgent.includes('WebKit')) &&\n        !navigator.userAgent.includes('Chrome'));\n}\n/**\n * This method checks if indexedDB is supported by current browser/service worker context\n * @return true if indexedDB is supported by current browser/service worker context\n */\nfunction isIndexedDBAvailable() {\n    try {\n        return typeof indexedDB === 'object';\n    }\n    catch (e) {\n        return false;\n    }\n}\n/**\n * This method validates browser/sw context for indexedDB by opening a dummy indexedDB database and reject\n * if errors occur during the database open operation.\n *\n * @throws exception if current browser/sw context can't run idb.open (ex: Safari iframe, Firefox\n * private browsing)\n */\nfunction validateIndexedDBOpenable() {\n    return new Promise((resolve, reject) => {\n        try {\n            let preExist = true;\n            const DB_CHECK_NAME = 'validate-browser-context-for-indexeddb-analytics-module';\n            const request = self.indexedDB.open(DB_CHECK_NAME);\n            request.onsuccess = () => {\n                request.result.close();\n                // delete database only when it doesn't pre-exist\n                if (!preExist) {\n                    self.indexedDB.deleteDatabase(DB_CHECK_NAME);\n                }\n                resolve(true);\n            };\n            request.onupgradeneeded = () => {\n                preExist = false;\n            };\n            request.onerror = () => {\n                var _a;\n                reject(((_a = request.error) === null || _a === void 0 ? void 0 : _a.message) || '');\n            };\n        }\n        catch (error) {\n            reject(error);\n        }\n    });\n}\n/**\n *\n * This method checks whether cookie is enabled within current browser\n * @return true if cookie is enabled within current browser\n */\nfunction areCookiesEnabled() {\n    if (typeof navigator === 'undefined' || !navigator.cookieEnabled) {\n        return false;\n    }\n    return true;\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @fileoverview Standardized Firebase Error.\n *\n * Usage:\n *\n *   // TypeScript string literals for type-safe codes\n *   type Err =\n *     'unknown' |\n *     'object-not-found'\n *     ;\n *\n *   // Closure enum for type-safe error codes\n *   // at-enum {string}\n *   var Err = {\n *     UNKNOWN: 'unknown',\n *     OBJECT_NOT_FOUND: 'object-not-found',\n *   }\n *\n *   let errors: Map<Err, string> = {\n *     'generic-error': \"Unknown error\",\n *     'file-not-found': \"Could not find file: {$file}\",\n *   };\n *\n *   // Type-safe function - must pass a valid error code as param.\n *   let error = new ErrorFactory<Err>('service', 'Service', errors);\n *\n *   ...\n *   throw error.create(Err.GENERIC);\n *   ...\n *   throw error.create(Err.FILE_NOT_FOUND, {'file': fileName});\n *   ...\n *   // Service: Could not file file: foo.txt (service/file-not-found).\n *\n *   catch (e) {\n *     assert(e.message === \"Could not find file: foo.txt.\");\n *     if ((e as FirebaseError)?.code === 'service/file-not-found') {\n *       console.log(\"Could not read file: \" + e['file']);\n *     }\n *   }\n */\nconst ERROR_NAME = 'FirebaseError';\n// Based on code from:\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Error#Custom_Error_Types\nclass FirebaseError extends Error {\n    constructor(\n    /** The error code for this error. */\n    code, message, \n    /** Custom data for this error. */\n    customData) {\n        super(message);\n        this.code = code;\n        this.customData = customData;\n        /** The custom name for all FirebaseErrors. */\n        this.name = ERROR_NAME;\n        // Fix For ES5\n        // https://github.com/Microsoft/TypeScript-wiki/blob/master/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\n        // TODO(dlarocque): Replace this with `new.target`: https://www.typescriptlang.org/docs/handbook/release-notes/typescript-2-2.html#support-for-newtarget\n        //                   which we can now use since we no longer target ES5.\n        Object.setPrototypeOf(this, FirebaseError.prototype);\n        // Maintains proper stack trace for where our error was thrown.\n        // Only available on V8.\n        if (Error.captureStackTrace) {\n            Error.captureStackTrace(this, ErrorFactory.prototype.create);\n        }\n    }\n}\nclass ErrorFactory {\n    constructor(service, serviceName, errors) {\n        this.service = service;\n        this.serviceName = serviceName;\n        this.errors = errors;\n    }\n    create(code, ...data) {\n        const customData = data[0] || {};\n        const fullCode = `${this.service}/${code}`;\n        const template = this.errors[code];\n        const message = template ? replaceTemplate(template, customData) : 'Error';\n        // Service Name: Error message (service/code).\n        const fullMessage = `${this.serviceName}: ${message} (${fullCode}).`;\n        const error = new FirebaseError(fullCode, fullMessage, customData);\n        return error;\n    }\n}\nfunction replaceTemplate(template, data) {\n    return template.replace(PATTERN, (_, key) => {\n        const value = data[key];\n        return value != null ? String(value) : `<${key}?>`;\n    });\n}\nconst PATTERN = /\\{\\$([^}]+)}/g;\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Evaluates a JSON string into a javascript object.\n *\n * @param {string} str A string containing JSON.\n * @return {*} The javascript object representing the specified JSON.\n */\nfunction jsonEval(str) {\n    return JSON.parse(str);\n}\n/**\n * Returns JSON representing a javascript object.\n * @param {*} data JavaScript object to be stringified.\n * @return {string} The JSON contents of the object.\n */\nfunction stringify(data) {\n    return JSON.stringify(data);\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Decodes a Firebase auth. token into constituent parts.\n *\n * Notes:\n * - May return with invalid / incomplete claims if there's no native base64 decoding support.\n * - Doesn't check if the token is actually valid.\n */\nconst decode = function (token) {\n    let header = {}, claims = {}, data = {}, signature = '';\n    try {\n        const parts = token.split('.');\n        header = jsonEval(base64Decode(parts[0]) || '');\n        claims = jsonEval(base64Decode(parts[1]) || '');\n        signature = parts[2];\n        data = claims['d'] || {};\n        delete claims['d'];\n    }\n    catch (e) { }\n    return {\n        header,\n        claims,\n        data,\n        signature\n    };\n};\n/**\n * Decodes a Firebase auth. token and checks the validity of its time-based claims. Will return true if the\n * token is within the time window authorized by the 'nbf' (not-before) and 'iat' (issued-at) claims.\n *\n * Notes:\n * - May return a false negative if there's no native base64 decoding support.\n * - Doesn't check if the token is actually valid.\n */\nconst isValidTimestamp = function (token) {\n    const claims = decode(token).claims;\n    const now = Math.floor(new Date().getTime() / 1000);\n    let validSince = 0, validUntil = 0;\n    if (typeof claims === 'object') {\n        if (claims.hasOwnProperty('nbf')) {\n            validSince = claims['nbf'];\n        }\n        else if (claims.hasOwnProperty('iat')) {\n            validSince = claims['iat'];\n        }\n        if (claims.hasOwnProperty('exp')) {\n            validUntil = claims['exp'];\n        }\n        else {\n            // token will expire after 24h by default\n            validUntil = validSince + 86400;\n        }\n    }\n    return (!!now &&\n        !!validSince &&\n        !!validUntil &&\n        now >= validSince &&\n        now <= validUntil);\n};\n/**\n * Decodes a Firebase auth. token and returns its issued at time if valid, null otherwise.\n *\n * Notes:\n * - May return null if there's no native base64 decoding support.\n * - Doesn't check if the token is actually valid.\n */\nconst issuedAtTime = function (token) {\n    const claims = decode(token).claims;\n    if (typeof claims === 'object' && claims.hasOwnProperty('iat')) {\n        return claims['iat'];\n    }\n    return null;\n};\n/**\n * Decodes a Firebase auth. token and checks the validity of its format. Expects a valid issued-at time.\n *\n * Notes:\n * - May return a false negative if there's no native base64 decoding support.\n * - Doesn't check if the token is actually valid.\n */\nconst isValidFormat = function (token) {\n    const decoded = decode(token), claims = decoded.claims;\n    return !!claims && typeof claims === 'object' && claims.hasOwnProperty('iat');\n};\n/**\n * Attempts to peer into an auth token and determine if it's an admin auth token by looking at the claims portion.\n *\n * Notes:\n * - May return a false negative if there's no native base64 decoding support.\n * - Doesn't check if the token is actually valid.\n */\nconst isAdmin = function (token) {\n    const claims = decode(token).claims;\n    return typeof claims === 'object' && claims['admin'] === true;\n};\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nfunction contains(obj, key) {\n    return Object.prototype.hasOwnProperty.call(obj, key);\n}\nfunction safeGet(obj, key) {\n    if (Object.prototype.hasOwnProperty.call(obj, key)) {\n        return obj[key];\n    }\n    else {\n        return undefined;\n    }\n}\nfunction isEmpty(obj) {\n    for (const key in obj) {\n        if (Object.prototype.hasOwnProperty.call(obj, key)) {\n            return false;\n        }\n    }\n    return true;\n}\nfunction map(obj, fn, contextObj) {\n    const res = {};\n    for (const key in obj) {\n        if (Object.prototype.hasOwnProperty.call(obj, key)) {\n            res[key] = fn.call(contextObj, obj[key], key, obj);\n        }\n    }\n    return res;\n}\n/**\n * Deep equal two objects. Support Arrays and Objects.\n */\nfunction deepEqual(a, b) {\n    if (a === b) {\n        return true;\n    }\n    const aKeys = Object.keys(a);\n    const bKeys = Object.keys(b);\n    for (const k of aKeys) {\n        if (!bKeys.includes(k)) {\n            return false;\n        }\n        const aProp = a[k];\n        const bProp = b[k];\n        if (isObject(aProp) && isObject(bProp)) {\n            if (!deepEqual(aProp, bProp)) {\n                return false;\n            }\n        }\n        else if (aProp !== bProp) {\n            return false;\n        }\n    }\n    for (const k of bKeys) {\n        if (!aKeys.includes(k)) {\n            return false;\n        }\n    }\n    return true;\n}\nfunction isObject(thing) {\n    return thing !== null && typeof thing === 'object';\n}\n\n/**\n * @license\n * Copyright 2022 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Rejects if the given promise doesn't resolve in timeInMS milliseconds.\n * @internal\n */\nfunction promiseWithTimeout(promise, timeInMS = 2000) {\n    const deferredPromise = new Deferred();\n    setTimeout(() => deferredPromise.reject('timeout!'), timeInMS);\n    promise.then(deferredPromise.resolve, deferredPromise.reject);\n    return deferredPromise.promise;\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Returns a querystring-formatted string (e.g. &arg=val&arg2=val2) from a\n * params object (e.g. {arg: 'val', arg2: 'val2'})\n * Note: You must prepend it with ? when adding it to a URL.\n */\nfunction querystring(querystringParams) {\n    const params = [];\n    for (const [key, value] of Object.entries(querystringParams)) {\n        if (Array.isArray(value)) {\n            value.forEach(arrayVal => {\n                params.push(encodeURIComponent(key) + '=' + encodeURIComponent(arrayVal));\n            });\n        }\n        else {\n            params.push(encodeURIComponent(key) + '=' + encodeURIComponent(value));\n        }\n    }\n    return params.length ? '&' + params.join('&') : '';\n}\n/**\n * Decodes a querystring (e.g. ?arg=val&arg2=val2) into a params object\n * (e.g. {arg: 'val', arg2: 'val2'})\n */\nfunction querystringDecode(querystring) {\n    const obj = {};\n    const tokens = querystring.replace(/^\\?/, '').split('&');\n    tokens.forEach(token => {\n        if (token) {\n            const [key, value] = token.split('=');\n            obj[decodeURIComponent(key)] = decodeURIComponent(value);\n        }\n    });\n    return obj;\n}\n/**\n * Extract the query string part of a URL, including the leading question mark (if present).\n */\nfunction extractQuerystring(url) {\n    const queryStart = url.indexOf('?');\n    if (!queryStart) {\n        return '';\n    }\n    const fragmentStart = url.indexOf('#', queryStart);\n    return url.substring(queryStart, fragmentStart > 0 ? fragmentStart : undefined);\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @fileoverview SHA-1 cryptographic hash.\n * Variable names follow the notation in FIPS PUB 180-3:\n * http://csrc.nist.gov/publications/fips/fips180-3/fips180-3_final.pdf.\n *\n * Usage:\n *   var sha1 = new sha1();\n *   sha1.update(bytes);\n *   var hash = sha1.digest();\n *\n * Performance:\n *   Chrome 23:   ~400 Mbit/s\n *   Firefox 16:  ~250 Mbit/s\n *\n */\n/**\n * SHA-1 cryptographic hash constructor.\n *\n * The properties declared here are discussed in the above algorithm document.\n * @constructor\n * @final\n * @struct\n */\nclass Sha1 {\n    constructor() {\n        /**\n         * Holds the previous values of accumulated variables a-e in the compress_\n         * function.\n         * @private\n         */\n        this.chain_ = [];\n        /**\n         * A buffer holding the partially computed hash result.\n         * @private\n         */\n        this.buf_ = [];\n        /**\n         * An array of 80 bytes, each a part of the message to be hashed.  Referred to\n         * as the message schedule in the docs.\n         * @private\n         */\n        this.W_ = [];\n        /**\n         * Contains data needed to pad messages less than 64 bytes.\n         * @private\n         */\n        this.pad_ = [];\n        /**\n         * @private {number}\n         */\n        this.inbuf_ = 0;\n        /**\n         * @private {number}\n         */\n        this.total_ = 0;\n        this.blockSize = 512 / 8;\n        this.pad_[0] = 128;\n        for (let i = 1; i < this.blockSize; ++i) {\n            this.pad_[i] = 0;\n        }\n        this.reset();\n    }\n    reset() {\n        this.chain_[0] = 0x67452301;\n        this.chain_[1] = 0xefcdab89;\n        this.chain_[2] = 0x98badcfe;\n        this.chain_[3] = 0x10325476;\n        this.chain_[4] = 0xc3d2e1f0;\n        this.inbuf_ = 0;\n        this.total_ = 0;\n    }\n    /**\n     * Internal compress helper function.\n     * @param buf Block to compress.\n     * @param offset Offset of the block in the buffer.\n     * @private\n     */\n    compress_(buf, offset) {\n        if (!offset) {\n            offset = 0;\n        }\n        const W = this.W_;\n        // get 16 big endian words\n        if (typeof buf === 'string') {\n            for (let i = 0; i < 16; i++) {\n                // TODO(user): [bug 8140122] Recent versions of Safari for Mac OS and iOS\n                // have a bug that turns the post-increment ++ operator into pre-increment\n                // during JIT compilation.  We have code that depends heavily on SHA-1 for\n                // correctness and which is affected by this bug, so I've removed all uses\n                // of post-increment ++ in which the result value is used.  We can revert\n                // this change once the Safari bug\n                // (https://bugs.webkit.org/show_bug.cgi?id=109036) has been fixed and\n                // most clients have been updated.\n                W[i] =\n                    (buf.charCodeAt(offset) << 24) |\n                        (buf.charCodeAt(offset + 1) << 16) |\n                        (buf.charCodeAt(offset + 2) << 8) |\n                        buf.charCodeAt(offset + 3);\n                offset += 4;\n            }\n        }\n        else {\n            for (let i = 0; i < 16; i++) {\n                W[i] =\n                    (buf[offset] << 24) |\n                        (buf[offset + 1] << 16) |\n                        (buf[offset + 2] << 8) |\n                        buf[offset + 3];\n                offset += 4;\n            }\n        }\n        // expand to 80 words\n        for (let i = 16; i < 80; i++) {\n            const t = W[i - 3] ^ W[i - 8] ^ W[i - 14] ^ W[i - 16];\n            W[i] = ((t << 1) | (t >>> 31)) & 0xffffffff;\n        }\n        let a = this.chain_[0];\n        let b = this.chain_[1];\n        let c = this.chain_[2];\n        let d = this.chain_[3];\n        let e = this.chain_[4];\n        let f, k;\n        // TODO(user): Try to unroll this loop to speed up the computation.\n        for (let i = 0; i < 80; i++) {\n            if (i < 40) {\n                if (i < 20) {\n                    f = d ^ (b & (c ^ d));\n                    k = 0x5a827999;\n                }\n                else {\n                    f = b ^ c ^ d;\n                    k = 0x6ed9eba1;\n                }\n            }\n            else {\n                if (i < 60) {\n                    f = (b & c) | (d & (b | c));\n                    k = 0x8f1bbcdc;\n                }\n                else {\n                    f = b ^ c ^ d;\n                    k = 0xca62c1d6;\n                }\n            }\n            const t = (((a << 5) | (a >>> 27)) + f + e + k + W[i]) & 0xffffffff;\n            e = d;\n            d = c;\n            c = ((b << 30) | (b >>> 2)) & 0xffffffff;\n            b = a;\n            a = t;\n        }\n        this.chain_[0] = (this.chain_[0] + a) & 0xffffffff;\n        this.chain_[1] = (this.chain_[1] + b) & 0xffffffff;\n        this.chain_[2] = (this.chain_[2] + c) & 0xffffffff;\n        this.chain_[3] = (this.chain_[3] + d) & 0xffffffff;\n        this.chain_[4] = (this.chain_[4] + e) & 0xffffffff;\n    }\n    update(bytes, length) {\n        // TODO(johnlenz): tighten the function signature and remove this check\n        if (bytes == null) {\n            return;\n        }\n        if (length === undefined) {\n            length = bytes.length;\n        }\n        const lengthMinusBlock = length - this.blockSize;\n        let n = 0;\n        // Using local instead of member variables gives ~5% speedup on Firefox 16.\n        const buf = this.buf_;\n        let inbuf = this.inbuf_;\n        // The outer while loop should execute at most twice.\n        while (n < length) {\n            // When we have no data in the block to top up, we can directly process the\n            // input buffer (assuming it contains sufficient data). This gives ~25%\n            // speedup on Chrome 23 and ~15% speedup on Firefox 16, but requires that\n            // the data is provided in large chunks (or in multiples of 64 bytes).\n            if (inbuf === 0) {\n                while (n <= lengthMinusBlock) {\n                    this.compress_(bytes, n);\n                    n += this.blockSize;\n                }\n            }\n            if (typeof bytes === 'string') {\n                while (n < length) {\n                    buf[inbuf] = bytes.charCodeAt(n);\n                    ++inbuf;\n                    ++n;\n                    if (inbuf === this.blockSize) {\n                        this.compress_(buf);\n                        inbuf = 0;\n                        // Jump to the outer loop so we use the full-block optimization.\n                        break;\n                    }\n                }\n            }\n            else {\n                while (n < length) {\n                    buf[inbuf] = bytes[n];\n                    ++inbuf;\n                    ++n;\n                    if (inbuf === this.blockSize) {\n                        this.compress_(buf);\n                        inbuf = 0;\n                        // Jump to the outer loop so we use the full-block optimization.\n                        break;\n                    }\n                }\n            }\n        }\n        this.inbuf_ = inbuf;\n        this.total_ += length;\n    }\n    /** @override */\n    digest() {\n        const digest = [];\n        let totalBits = this.total_ * 8;\n        // Add pad 0x80 0x00*.\n        if (this.inbuf_ < 56) {\n            this.update(this.pad_, 56 - this.inbuf_);\n        }\n        else {\n            this.update(this.pad_, this.blockSize - (this.inbuf_ - 56));\n        }\n        // Add # bits.\n        for (let i = this.blockSize - 1; i >= 56; i--) {\n            this.buf_[i] = totalBits & 255;\n            totalBits /= 256; // Don't use bit-shifting here!\n        }\n        this.compress_(this.buf_);\n        let n = 0;\n        for (let i = 0; i < 5; i++) {\n            for (let j = 24; j >= 0; j -= 8) {\n                digest[n] = (this.chain_[i] >> j) & 255;\n                ++n;\n            }\n        }\n        return digest;\n    }\n}\n\n/**\n * Helper to make a Subscribe function (just like Promise helps make a\n * Thenable).\n *\n * @param executor Function which can make calls to a single Observer\n *     as a proxy.\n * @param onNoObservers Callback when count of Observers goes to zero.\n */\nfunction createSubscribe(executor, onNoObservers) {\n    const proxy = new ObserverProxy(executor, onNoObservers);\n    return proxy.subscribe.bind(proxy);\n}\n/**\n * Implement fan-out for any number of Observers attached via a subscribe\n * function.\n */\nclass ObserverProxy {\n    /**\n     * @param executor Function which can make calls to a single Observer\n     *     as a proxy.\n     * @param onNoObservers Callback when count of Observers goes to zero.\n     */\n    constructor(executor, onNoObservers) {\n        this.observers = [];\n        this.unsubscribes = [];\n        this.observerCount = 0;\n        // Micro-task scheduling by calling task.then().\n        this.task = Promise.resolve();\n        this.finalized = false;\n        this.onNoObservers = onNoObservers;\n        // Call the executor asynchronously so subscribers that are called\n        // synchronously after the creation of the subscribe function\n        // can still receive the very first value generated in the executor.\n        this.task\n            .then(() => {\n            executor(this);\n        })\n            .catch(e => {\n            this.error(e);\n        });\n    }\n    next(value) {\n        this.forEachObserver((observer) => {\n            observer.next(value);\n        });\n    }\n    error(error) {\n        this.forEachObserver((observer) => {\n            observer.error(error);\n        });\n        this.close(error);\n    }\n    complete() {\n        this.forEachObserver((observer) => {\n            observer.complete();\n        });\n        this.close();\n    }\n    /**\n     * Subscribe function that can be used to add an Observer to the fan-out list.\n     *\n     * - We require that no event is sent to a subscriber synchronously to their\n     *   call to subscribe().\n     */\n    subscribe(nextOrObserver, error, complete) {\n        let observer;\n        if (nextOrObserver === undefined &&\n            error === undefined &&\n            complete === undefined) {\n            throw new Error('Missing Observer.');\n        }\n        // Assemble an Observer object when passed as callback functions.\n        if (implementsAnyMethods(nextOrObserver, [\n            'next',\n            'error',\n            'complete'\n        ])) {\n            observer = nextOrObserver;\n        }\n        else {\n            observer = {\n                next: nextOrObserver,\n                error,\n                complete\n            };\n        }\n        if (observer.next === undefined) {\n            observer.next = noop;\n        }\n        if (observer.error === undefined) {\n            observer.error = noop;\n        }\n        if (observer.complete === undefined) {\n            observer.complete = noop;\n        }\n        const unsub = this.unsubscribeOne.bind(this, this.observers.length);\n        // Attempt to subscribe to a terminated Observable - we\n        // just respond to the Observer with the final error or complete\n        // event.\n        if (this.finalized) {\n            // eslint-disable-next-line @typescript-eslint/no-floating-promises\n            this.task.then(() => {\n                try {\n                    if (this.finalError) {\n                        observer.error(this.finalError);\n                    }\n                    else {\n                        observer.complete();\n                    }\n                }\n                catch (e) {\n                    // nothing\n                }\n                return;\n            });\n        }\n        this.observers.push(observer);\n        return unsub;\n    }\n    // Unsubscribe is synchronous - we guarantee that no events are sent to\n    // any unsubscribed Observer.\n    unsubscribeOne(i) {\n        if (this.observers === undefined || this.observers[i] === undefined) {\n            return;\n        }\n        delete this.observers[i];\n        this.observerCount -= 1;\n        if (this.observerCount === 0 && this.onNoObservers !== undefined) {\n            this.onNoObservers(this);\n        }\n    }\n    forEachObserver(fn) {\n        if (this.finalized) {\n            // Already closed by previous event....just eat the additional values.\n            return;\n        }\n        // Since sendOne calls asynchronously - there is no chance that\n        // this.observers will become undefined.\n        for (let i = 0; i < this.observers.length; i++) {\n            this.sendOne(i, fn);\n        }\n    }\n    // Call the Observer via one of it's callback function. We are careful to\n    // confirm that the observe has not been unsubscribed since this asynchronous\n    // function had been queued.\n    sendOne(i, fn) {\n        // Execute the callback asynchronously\n        // eslint-disable-next-line @typescript-eslint/no-floating-promises\n        this.task.then(() => {\n            if (this.observers !== undefined && this.observers[i] !== undefined) {\n                try {\n                    fn(this.observers[i]);\n                }\n                catch (e) {\n                    // Ignore exceptions raised in Observers or missing methods of an\n                    // Observer.\n                    // Log error to console. b/31404806\n                    if (typeof console !== 'undefined' && console.error) {\n                        console.error(e);\n                    }\n                }\n            }\n        });\n    }\n    close(err) {\n        if (this.finalized) {\n            return;\n        }\n        this.finalized = true;\n        if (err !== undefined) {\n            this.finalError = err;\n        }\n        // Proxy is no longer needed - garbage collect references\n        // eslint-disable-next-line @typescript-eslint/no-floating-promises\n        this.task.then(() => {\n            this.observers = undefined;\n            this.onNoObservers = undefined;\n        });\n    }\n}\n/** Turn synchronous function into one called asynchronously. */\n// eslint-disable-next-line @typescript-eslint/ban-types\nfunction async(fn, onError) {\n    return (...args) => {\n        Promise.resolve(true)\n            .then(() => {\n            fn(...args);\n        })\n            .catch((error) => {\n            if (onError) {\n                onError(error);\n            }\n        });\n    };\n}\n/**\n * Return true if the object passed in implements any of the named methods.\n */\nfunction implementsAnyMethods(obj, methods) {\n    if (typeof obj !== 'object' || obj === null) {\n        return false;\n    }\n    for (const method of methods) {\n        if (method in obj && typeof obj[method] === 'function') {\n            return true;\n        }\n    }\n    return false;\n}\nfunction noop() {\n    // do nothing\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Check to make sure the appropriate number of arguments are provided for a public function.\n * Throws an error if it fails.\n *\n * @param fnName The function name\n * @param minCount The minimum number of arguments to allow for the function call\n * @param maxCount The maximum number of argument to allow for the function call\n * @param argCount The actual number of arguments provided.\n */\nconst validateArgCount = function (fnName, minCount, maxCount, argCount) {\n    let argError;\n    if (argCount < minCount) {\n        argError = 'at least ' + minCount;\n    }\n    else if (argCount > maxCount) {\n        argError = maxCount === 0 ? 'none' : 'no more than ' + maxCount;\n    }\n    if (argError) {\n        const error = fnName +\n            ' failed: Was called with ' +\n            argCount +\n            (argCount === 1 ? ' argument.' : ' arguments.') +\n            ' Expects ' +\n            argError +\n            '.';\n        throw new Error(error);\n    }\n};\n/**\n * Generates a string to prefix an error message about failed argument validation\n *\n * @param fnName The function name\n * @param argName The name of the argument\n * @return The prefix to add to the error thrown for validation.\n */\nfunction errorPrefix(fnName, argName) {\n    return `${fnName} failed: ${argName} argument `;\n}\n/**\n * @param fnName\n * @param argumentNumber\n * @param namespace\n * @param optional\n */\nfunction validateNamespace(fnName, namespace, optional) {\n    if (optional && !namespace) {\n        return;\n    }\n    if (typeof namespace !== 'string') {\n        //TODO: I should do more validation here. We only allow certain chars in namespaces.\n        throw new Error(errorPrefix(fnName, 'namespace') + 'must be a valid firebase namespace.');\n    }\n}\nfunction validateCallback(fnName, argumentName, \n// eslint-disable-next-line @typescript-eslint/ban-types\ncallback, optional) {\n    if (optional && !callback) {\n        return;\n    }\n    if (typeof callback !== 'function') {\n        throw new Error(errorPrefix(fnName, argumentName) + 'must be a valid function.');\n    }\n}\nfunction validateContextObject(fnName, argumentName, context, optional) {\n    if (optional && !context) {\n        return;\n    }\n    if (typeof context !== 'object' || context === null) {\n        throw new Error(errorPrefix(fnName, argumentName) + 'must be a valid context object.');\n    }\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// Code originally came from goog.crypt.stringToUtf8ByteArray, but for some reason they\n// automatically replaced '\\r\\n' with '\\n', and they didn't handle surrogate pairs,\n// so it's been modified.\n// Note that not all Unicode characters appear as single characters in JavaScript strings.\n// fromCharCode returns the UTF-16 encoding of a character - so some Unicode characters\n// use 2 characters in JavaScript.  All 4-byte UTF-8 characters begin with a first\n// character in the range 0xD800 - 0xDBFF (the first character of a so-called surrogate\n// pair).\n// See http://www.ecma-international.org/ecma-262/5.1/#sec-15.1.3\n/**\n * @param {string} str\n * @return {Array}\n */\nconst stringToByteArray = function (str) {\n    const out = [];\n    let p = 0;\n    for (let i = 0; i < str.length; i++) {\n        let c = str.charCodeAt(i);\n        // Is this the lead surrogate in a surrogate pair?\n        if (c >= 0xd800 && c <= 0xdbff) {\n            const high = c - 0xd800; // the high 10 bits.\n            i++;\n            assert(i < str.length, 'Surrogate pair missing trail surrogate.');\n            const low = str.charCodeAt(i) - 0xdc00; // the low 10 bits.\n            c = 0x10000 + (high << 10) + low;\n        }\n        if (c < 128) {\n            out[p++] = c;\n        }\n        else if (c < 2048) {\n            out[p++] = (c >> 6) | 192;\n            out[p++] = (c & 63) | 128;\n        }\n        else if (c < 65536) {\n            out[p++] = (c >> 12) | 224;\n            out[p++] = ((c >> 6) & 63) | 128;\n            out[p++] = (c & 63) | 128;\n        }\n        else {\n            out[p++] = (c >> 18) | 240;\n            out[p++] = ((c >> 12) & 63) | 128;\n            out[p++] = ((c >> 6) & 63) | 128;\n            out[p++] = (c & 63) | 128;\n        }\n    }\n    return out;\n};\n/**\n * Calculate length without actually converting; useful for doing cheaper validation.\n * @param {string} str\n * @return {number}\n */\nconst stringLength = function (str) {\n    let p = 0;\n    for (let i = 0; i < str.length; i++) {\n        const c = str.charCodeAt(i);\n        if (c < 128) {\n            p++;\n        }\n        else if (c < 2048) {\n            p += 2;\n        }\n        else if (c >= 0xd800 && c <= 0xdbff) {\n            // Lead surrogate of a surrogate pair.  The pair together will take 4 bytes to represent.\n            p += 4;\n            i++; // skip trail surrogate.\n        }\n        else {\n            p += 3;\n        }\n    }\n    return p;\n};\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * The amount of milliseconds to exponentially increase.\n */\nconst DEFAULT_INTERVAL_MILLIS = 1000;\n/**\n * The factor to backoff by.\n * Should be a number greater than 1.\n */\nconst DEFAULT_BACKOFF_FACTOR = 2;\n/**\n * The maximum milliseconds to increase to.\n *\n * <p>Visible for testing\n */\nconst MAX_VALUE_MILLIS = 4 * 60 * 60 * 1000; // Four hours, like iOS and Android.\n/**\n * The percentage of backoff time to randomize by.\n * See\n * http://go/safe-client-behavior#step-1-determine-the-appropriate-retry-interval-to-handle-spike-traffic\n * for context.\n *\n * <p>Visible for testing\n */\nconst RANDOM_FACTOR = 0.5;\n/**\n * Based on the backoff method from\n * https://github.com/google/closure-library/blob/master/closure/goog/math/exponentialbackoff.js.\n * Extracted here so we don't need to pass metadata and a stateful ExponentialBackoff object around.\n */\nfunction calculateBackoffMillis(backoffCount, intervalMillis = DEFAULT_INTERVAL_MILLIS, backoffFactor = DEFAULT_BACKOFF_FACTOR) {\n    // Calculates an exponentially increasing value.\n    // Deviation: calculates value from count and a constant interval, so we only need to save value\n    // and count to restore state.\n    const currBaseValue = intervalMillis * Math.pow(backoffFactor, backoffCount);\n    // A random \"fuzz\" to avoid waves of retries.\n    // Deviation: randomFactor is required.\n    const randomWait = Math.round(\n    // A fraction of the backoff value to add/subtract.\n    // Deviation: changes multiplication order to improve readability.\n    RANDOM_FACTOR *\n        currBaseValue *\n        // A random float (rounded to int by Math.round above) in the range [-1, 1]. Determines\n        // if we add or subtract.\n        (Math.random() - 0.5) *\n        2);\n    // Limits backoff to max to avoid effectively permanent backoff.\n    return Math.min(MAX_VALUE_MILLIS, currBaseValue + randomWait);\n}\n\n/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Provide English ordinal letters after a number\n */\nfunction ordinal(i) {\n    if (!Number.isFinite(i)) {\n        return `${i}`;\n    }\n    return i + indicator(i);\n}\nfunction indicator(i) {\n    i = Math.abs(i);\n    const cent = i % 100;\n    if (cent >= 10 && cent <= 20) {\n        return 'th';\n    }\n    const dec = i % 10;\n    if (dec === 1) {\n        return 'st';\n    }\n    if (dec === 2) {\n        return 'nd';\n    }\n    if (dec === 3) {\n        return 'rd';\n    }\n    return 'th';\n}\n\n/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nfunction getModularInstance(service) {\n    if (service && service._delegate) {\n        return service._delegate;\n    }\n    else {\n        return service;\n    }\n}\n\nexport { CONSTANTS, DecodeBase64StringError, Deferred, ErrorFactory, FirebaseError, MAX_VALUE_MILLIS, RANDOM_FACTOR, Sha1, areCookiesEnabled, assert, assertionError, async, base64, base64Decode, base64Encode, base64urlEncodeWithoutPadding, calculateBackoffMillis, contains, createMockUserToken, createSubscribe, decode, deepCopy, deepEqual, deepExtend, errorPrefix, extractQuerystring, getDefaultAppConfig, getDefaultEmulatorHost, getDefaultEmulatorHostnameAndPort, getDefaults, getExperimentalSetting, getGlobal, getModularInstance, getUA, isAdmin, isBrowser, isBrowserExtension, isCloudWorkstation, isCloudflareWorker, isElectron, isEmpty, isIE, isIndexedDBAvailable, isMobileCordova, isNode, isNodeSdk, isReactNative, isSafari, isSafariOrWebkit, isUWP, isValidFormat, isValidTimestamp, isWebWorker, issuedAtTime, jsonEval, map, ordinal, pingServer, promiseWithTimeout, querystring, querystringDecode, safeGet, stringLength, stringToByteArray, stringify, updateEmulatorBanner, validateArgCount, validateCallback, validateContextObject, validateIndexedDBOpenable, validateNamespace };\n\n", "import { Deferred } from '@firebase/util';\n\n/**\n * Component for service name T, e.g. `auth`, `auth-internal`\n */\nclass Component {\n    /**\n     *\n     * @param name The public service name, e.g. app, auth, firestore, database\n     * @param instanceFactory Service factory responsible for creating the public interface\n     * @param type whether the service provided by the component is public or private\n     */\n    constructor(name, instanceFactory, type) {\n        this.name = name;\n        this.instanceFactory = instanceFactory;\n        this.type = type;\n        this.multipleInstances = false;\n        /**\n         * Properties to be added to the service namespace\n         */\n        this.serviceProps = {};\n        this.instantiationMode = \"LAZY\" /* InstantiationMode.LAZY */;\n        this.onInstanceCreated = null;\n    }\n    setInstantiationMode(mode) {\n        this.instantiationMode = mode;\n        return this;\n    }\n    setMultipleInstances(multipleInstances) {\n        this.multipleInstances = multipleInstances;\n        return this;\n    }\n    setServiceProps(props) {\n        this.serviceProps = props;\n        return this;\n    }\n    setInstanceCreatedCallback(callback) {\n        this.onInstanceCreated = callback;\n        return this;\n    }\n}\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst DEFAULT_ENTRY_NAME = '[DEFAULT]';\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Provider for instance for service name T, e.g. 'auth', 'auth-internal'\n * NameServiceMapping[T] is an alias for the type of the instance\n */\nclass Provider {\n    constructor(name, container) {\n        this.name = name;\n        this.container = container;\n        this.component = null;\n        this.instances = new Map();\n        this.instancesDeferred = new Map();\n        this.instancesOptions = new Map();\n        this.onInitCallbacks = new Map();\n    }\n    /**\n     * @param identifier A provider can provide multiple instances of a service\n     * if this.component.multipleInstances is true.\n     */\n    get(identifier) {\n        // if multipleInstances is not supported, use the default name\n        const normalizedIdentifier = this.normalizeInstanceIdentifier(identifier);\n        if (!this.instancesDeferred.has(normalizedIdentifier)) {\n            const deferred = new Deferred();\n            this.instancesDeferred.set(normalizedIdentifier, deferred);\n            if (this.isInitialized(normalizedIdentifier) ||\n                this.shouldAutoInitialize()) {\n                // initialize the service if it can be auto-initialized\n                try {\n                    const instance = this.getOrInitializeService({\n                        instanceIdentifier: normalizedIdentifier\n                    });\n                    if (instance) {\n                        deferred.resolve(instance);\n                    }\n                }\n                catch (e) {\n                    // when the instance factory throws an exception during get(), it should not cause\n                    // a fatal error. We just return the unresolved promise in this case.\n                }\n            }\n        }\n        return this.instancesDeferred.get(normalizedIdentifier).promise;\n    }\n    getImmediate(options) {\n        var _a;\n        // if multipleInstances is not supported, use the default name\n        const normalizedIdentifier = this.normalizeInstanceIdentifier(options === null || options === void 0 ? void 0 : options.identifier);\n        const optional = (_a = options === null || options === void 0 ? void 0 : options.optional) !== null && _a !== void 0 ? _a : false;\n        if (this.isInitialized(normalizedIdentifier) ||\n            this.shouldAutoInitialize()) {\n            try {\n                return this.getOrInitializeService({\n                    instanceIdentifier: normalizedIdentifier\n                });\n            }\n            catch (e) {\n                if (optional) {\n                    return null;\n                }\n                else {\n                    throw e;\n                }\n            }\n        }\n        else {\n            // In case a component is not initialized and should/cannot be auto-initialized at the moment, return null if the optional flag is set, or throw\n            if (optional) {\n                return null;\n            }\n            else {\n                throw Error(`Service ${this.name} is not available`);\n            }\n        }\n    }\n    getComponent() {\n        return this.component;\n    }\n    setComponent(component) {\n        if (component.name !== this.name) {\n            throw Error(`Mismatching Component ${component.name} for Provider ${this.name}.`);\n        }\n        if (this.component) {\n            throw Error(`Component for ${this.name} has already been provided`);\n        }\n        this.component = component;\n        // return early without attempting to initialize the component if the component requires explicit initialization (calling `Provider.initialize()`)\n        if (!this.shouldAutoInitialize()) {\n            return;\n        }\n        // if the service is eager, initialize the default instance\n        if (isComponentEager(component)) {\n            try {\n                this.getOrInitializeService({ instanceIdentifier: DEFAULT_ENTRY_NAME });\n            }\n            catch (e) {\n                // when the instance factory for an eager Component throws an exception during the eager\n                // initialization, it should not cause a fatal error.\n                // TODO: Investigate if we need to make it configurable, because some component may want to cause\n                // a fatal error in this case?\n            }\n        }\n        // Create service instances for the pending promises and resolve them\n        // NOTE: if this.multipleInstances is false, only the default instance will be created\n        // and all promises with resolve with it regardless of the identifier.\n        for (const [instanceIdentifier, instanceDeferred] of this.instancesDeferred.entries()) {\n            const normalizedIdentifier = this.normalizeInstanceIdentifier(instanceIdentifier);\n            try {\n                // `getOrInitializeService()` should always return a valid instance since a component is guaranteed. use ! to make typescript happy.\n                const instance = this.getOrInitializeService({\n                    instanceIdentifier: normalizedIdentifier\n                });\n                instanceDeferred.resolve(instance);\n            }\n            catch (e) {\n                // when the instance factory throws an exception, it should not cause\n                // a fatal error. We just leave the promise unresolved.\n            }\n        }\n    }\n    clearInstance(identifier = DEFAULT_ENTRY_NAME) {\n        this.instancesDeferred.delete(identifier);\n        this.instancesOptions.delete(identifier);\n        this.instances.delete(identifier);\n    }\n    // app.delete() will call this method on every provider to delete the services\n    // TODO: should we mark the provider as deleted?\n    async delete() {\n        const services = Array.from(this.instances.values());\n        await Promise.all([\n            ...services\n                .filter(service => 'INTERNAL' in service) // legacy services\n                // eslint-disable-next-line @typescript-eslint/no-explicit-any\n                .map(service => service.INTERNAL.delete()),\n            ...services\n                .filter(service => '_delete' in service) // modularized services\n                // eslint-disable-next-line @typescript-eslint/no-explicit-any\n                .map(service => service._delete())\n        ]);\n    }\n    isComponentSet() {\n        return this.component != null;\n    }\n    isInitialized(identifier = DEFAULT_ENTRY_NAME) {\n        return this.instances.has(identifier);\n    }\n    getOptions(identifier = DEFAULT_ENTRY_NAME) {\n        return this.instancesOptions.get(identifier) || {};\n    }\n    initialize(opts = {}) {\n        const { options = {} } = opts;\n        const normalizedIdentifier = this.normalizeInstanceIdentifier(opts.instanceIdentifier);\n        if (this.isInitialized(normalizedIdentifier)) {\n            throw Error(`${this.name}(${normalizedIdentifier}) has already been initialized`);\n        }\n        if (!this.isComponentSet()) {\n            throw Error(`Component ${this.name} has not been registered yet`);\n        }\n        const instance = this.getOrInitializeService({\n            instanceIdentifier: normalizedIdentifier,\n            options\n        });\n        // resolve any pending promise waiting for the service instance\n        for (const [instanceIdentifier, instanceDeferred] of this.instancesDeferred.entries()) {\n            const normalizedDeferredIdentifier = this.normalizeInstanceIdentifier(instanceIdentifier);\n            if (normalizedIdentifier === normalizedDeferredIdentifier) {\n                instanceDeferred.resolve(instance);\n            }\n        }\n        return instance;\n    }\n    /**\n     *\n     * @param callback - a function that will be invoked  after the provider has been initialized by calling provider.initialize().\n     * The function is invoked SYNCHRONOUSLY, so it should not execute any longrunning tasks in order to not block the program.\n     *\n     * @param identifier An optional instance identifier\n     * @returns a function to unregister the callback\n     */\n    onInit(callback, identifier) {\n        var _a;\n        const normalizedIdentifier = this.normalizeInstanceIdentifier(identifier);\n        const existingCallbacks = (_a = this.onInitCallbacks.get(normalizedIdentifier)) !== null && _a !== void 0 ? _a : new Set();\n        existingCallbacks.add(callback);\n        this.onInitCallbacks.set(normalizedIdentifier, existingCallbacks);\n        const existingInstance = this.instances.get(normalizedIdentifier);\n        if (existingInstance) {\n            callback(existingInstance, normalizedIdentifier);\n        }\n        return () => {\n            existingCallbacks.delete(callback);\n        };\n    }\n    /**\n     * Invoke onInit callbacks synchronously\n     * @param instance the service instance`\n     */\n    invokeOnInitCallbacks(instance, identifier) {\n        const callbacks = this.onInitCallbacks.get(identifier);\n        if (!callbacks) {\n            return;\n        }\n        for (const callback of callbacks) {\n            try {\n                callback(instance, identifier);\n            }\n            catch (_a) {\n                // ignore errors in the onInit callback\n            }\n        }\n    }\n    getOrInitializeService({ instanceIdentifier, options = {} }) {\n        let instance = this.instances.get(instanceIdentifier);\n        if (!instance && this.component) {\n            instance = this.component.instanceFactory(this.container, {\n                instanceIdentifier: normalizeIdentifierForFactory(instanceIdentifier),\n                options\n            });\n            this.instances.set(instanceIdentifier, instance);\n            this.instancesOptions.set(instanceIdentifier, options);\n            /**\n             * Invoke onInit listeners.\n             * Note this.component.onInstanceCreated is different, which is used by the component creator,\n             * while onInit listeners are registered by consumers of the provider.\n             */\n            this.invokeOnInitCallbacks(instance, instanceIdentifier);\n            /**\n             * Order is important\n             * onInstanceCreated() should be called after this.instances.set(instanceIdentifier, instance); which\n             * makes `isInitialized()` return true.\n             */\n            if (this.component.onInstanceCreated) {\n                try {\n                    this.component.onInstanceCreated(this.container, instanceIdentifier, instance);\n                }\n                catch (_a) {\n                    // ignore errors in the onInstanceCreatedCallback\n                }\n            }\n        }\n        return instance || null;\n    }\n    normalizeInstanceIdentifier(identifier = DEFAULT_ENTRY_NAME) {\n        if (this.component) {\n            return this.component.multipleInstances ? identifier : DEFAULT_ENTRY_NAME;\n        }\n        else {\n            return identifier; // assume multiple instances are supported before the component is provided.\n        }\n    }\n    shouldAutoInitialize() {\n        return (!!this.component &&\n            this.component.instantiationMode !== \"EXPLICIT\" /* InstantiationMode.EXPLICIT */);\n    }\n}\n// undefined should be passed to the service factory for the default instance\nfunction normalizeIdentifierForFactory(identifier) {\n    return identifier === DEFAULT_ENTRY_NAME ? undefined : identifier;\n}\nfunction isComponentEager(component) {\n    return component.instantiationMode === \"EAGER\" /* InstantiationMode.EAGER */;\n}\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * ComponentContainer that provides Providers for service name T, e.g. `auth`, `auth-internal`\n */\nclass ComponentContainer {\n    constructor(name) {\n        this.name = name;\n        this.providers = new Map();\n    }\n    /**\n     *\n     * @param component Component being added\n     * @param overwrite When a component with the same name has already been registered,\n     * if overwrite is true: overwrite the existing component with the new component and create a new\n     * provider with the new component. It can be useful in tests where you want to use different mocks\n     * for different tests.\n     * if overwrite is false: throw an exception\n     */\n    addComponent(component) {\n        const provider = this.getProvider(component.name);\n        if (provider.isComponentSet()) {\n            throw new Error(`Component ${component.name} has already been registered with ${this.name}`);\n        }\n        provider.setComponent(component);\n    }\n    addOrOverwriteComponent(component) {\n        const provider = this.getProvider(component.name);\n        if (provider.isComponentSet()) {\n            // delete the existing provider from the container, so we can register the new component\n            this.providers.delete(component.name);\n        }\n        this.addComponent(component);\n    }\n    /**\n     * getProvider provides a type safe interface where it can only be called with a field name\n     * present in NameServiceMapping interface.\n     *\n     * Firebase SDKs providing services should extend NameServiceMapping interface to register\n     * themselves.\n     */\n    getProvider(name) {\n        if (this.providers.has(name)) {\n            return this.providers.get(name);\n        }\n        // create a Provider for a service that hasn't registered with Firebase\n        const provider = new Provider(name, this);\n        this.providers.set(name, provider);\n        return provider;\n    }\n    getProviders() {\n        return Array.from(this.providers.values());\n    }\n}\n\nexport { Component, ComponentContainer, Provider };\n\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * A container for all of the Logger instances\n */\nconst instances = [];\n/**\n * The JS SDK supports 5 log levels and also allows a user the ability to\n * silence the logs altogether.\n *\n * The order is a follows:\n * DEBUG < VERBOSE < INFO < WARN < ERROR\n *\n * All of the log types above the current log level will be captured (i.e. if\n * you set the log level to `INFO`, errors will still be logged, but `DEBUG` and\n * `VERBOSE` logs will not)\n */\nvar LogLevel;\n(function (LogLevel) {\n    LogLevel[LogLevel[\"DEBUG\"] = 0] = \"DEBUG\";\n    LogLevel[LogLevel[\"VERBOSE\"] = 1] = \"VERBOSE\";\n    LogLevel[LogLevel[\"INFO\"] = 2] = \"INFO\";\n    LogLevel[LogLevel[\"WARN\"] = 3] = \"WARN\";\n    LogLevel[LogLevel[\"ERROR\"] = 4] = \"ERROR\";\n    LogLevel[LogLevel[\"SILENT\"] = 5] = \"SILENT\";\n})(LogLevel || (LogLevel = {}));\nconst levelStringToEnum = {\n    'debug': LogLevel.DEBUG,\n    'verbose': LogLevel.VERBOSE,\n    'info': LogLevel.INFO,\n    'warn': LogLevel.WARN,\n    'error': LogLevel.ERROR,\n    'silent': LogLevel.SILENT\n};\n/**\n * The default log level\n */\nconst defaultLogLevel = LogLevel.INFO;\n/**\n * By default, `console.debug` is not displayed in the developer console (in\n * chrome). To avoid forcing users to have to opt-in to these logs twice\n * (i.e. once for firebase, and once in the console), we are sending `DEBUG`\n * logs to the `console.log` function.\n */\nconst ConsoleMethod = {\n    [LogLevel.DEBUG]: 'log',\n    [LogLevel.VERBOSE]: 'log',\n    [LogLevel.INFO]: 'info',\n    [LogLevel.WARN]: 'warn',\n    [LogLevel.ERROR]: 'error'\n};\n/**\n * The default log handler will forward DEBUG, VERBOSE, INFO, WARN, and ERROR\n * messages on to their corresponding console counterparts (if the log method\n * is supported by the current log level)\n */\nconst defaultLogHandler = (instance, logType, ...args) => {\n    if (logType < instance.logLevel) {\n        return;\n    }\n    const now = new Date().toISOString();\n    const method = ConsoleMethod[logType];\n    if (method) {\n        console[method](`[${now}]  ${instance.name}:`, ...args);\n    }\n    else {\n        throw new Error(`Attempted to log a message with an invalid logType (value: ${logType})`);\n    }\n};\nclass Logger {\n    /**\n     * Gives you an instance of a Logger to capture messages according to\n     * Firebase's logging scheme.\n     *\n     * @param name The name that the logs will be associated with\n     */\n    constructor(name) {\n        this.name = name;\n        /**\n         * The log level of the given Logger instance.\n         */\n        this._logLevel = defaultLogLevel;\n        /**\n         * The main (internal) log handler for the Logger instance.\n         * Can be set to a new function in internal package code but not by user.\n         */\n        this._logHandler = defaultLogHandler;\n        /**\n         * The optional, additional, user-defined log handler for the Logger instance.\n         */\n        this._userLogHandler = null;\n        /**\n         * Capture the current instance for later use\n         */\n        instances.push(this);\n    }\n    get logLevel() {\n        return this._logLevel;\n    }\n    set logLevel(val) {\n        if (!(val in LogLevel)) {\n            throw new TypeError(`Invalid value \"${val}\" assigned to \\`logLevel\\``);\n        }\n        this._logLevel = val;\n    }\n    // Workaround for setter/getter having to be the same type.\n    setLogLevel(val) {\n        this._logLevel = typeof val === 'string' ? levelStringToEnum[val] : val;\n    }\n    get logHandler() {\n        return this._logHandler;\n    }\n    set logHandler(val) {\n        if (typeof val !== 'function') {\n            throw new TypeError('Value assigned to `logHandler` must be a function');\n        }\n        this._logHandler = val;\n    }\n    get userLogHandler() {\n        return this._userLogHandler;\n    }\n    set userLogHandler(val) {\n        this._userLogHandler = val;\n    }\n    /**\n     * The functions below are all based on the `console` interface\n     */\n    debug(...args) {\n        this._userLogHandler && this._userLogHandler(this, LogLevel.DEBUG, ...args);\n        this._logHandler(this, LogLevel.DEBUG, ...args);\n    }\n    log(...args) {\n        this._userLogHandler &&\n            this._userLogHandler(this, LogLevel.VERBOSE, ...args);\n        this._logHandler(this, LogLevel.VERBOSE, ...args);\n    }\n    info(...args) {\n        this._userLogHandler && this._userLogHandler(this, LogLevel.INFO, ...args);\n        this._logHandler(this, LogLevel.INFO, ...args);\n    }\n    warn(...args) {\n        this._userLogHandler && this._userLogHandler(this, LogLevel.WARN, ...args);\n        this._logHandler(this, LogLevel.WARN, ...args);\n    }\n    error(...args) {\n        this._userLogHandler && this._userLogHandler(this, LogLevel.ERROR, ...args);\n        this._logHandler(this, LogLevel.ERROR, ...args);\n    }\n}\nfunction setLogLevel(level) {\n    instances.forEach(inst => {\n        inst.setLogLevel(level);\n    });\n}\nfunction setUserLogHandler(logCallback, options) {\n    for (const instance of instances) {\n        let customLogLevel = null;\n        if (options && options.level) {\n            customLogLevel = levelStringToEnum[options.level];\n        }\n        if (logCallback === null) {\n            instance.userLogHandler = null;\n        }\n        else {\n            instance.userLogHandler = (instance, level, ...args) => {\n                const message = args\n                    .map(arg => {\n                    if (arg == null) {\n                        return null;\n                    }\n                    else if (typeof arg === 'string') {\n                        return arg;\n                    }\n                    else if (typeof arg === 'number' || typeof arg === 'boolean') {\n                        return arg.toString();\n                    }\n                    else if (arg instanceof Error) {\n                        return arg.message;\n                    }\n                    else {\n                        try {\n                            return JSON.stringify(arg);\n                        }\n                        catch (ignored) {\n                            return null;\n                        }\n                    }\n                })\n                    .filter(arg => arg)\n                    .join(' ');\n                if (level >= (customLogLevel !== null && customLogLevel !== void 0 ? customLogLevel : instance.logLevel)) {\n                    logCallback({\n                        level: LogLevel[level].toLowerCase(),\n                        message,\n                        args,\n                        type: instance.name\n                    });\n                }\n            };\n        }\n    }\n}\n\nexport { LogLevel, Logger, setLogLevel, setUserLogHandler };\n\n", "const instanceOfAny = (object, constructors) => constructors.some((c) => object instanceof c);\n\nlet idbProxyableTypes;\nlet cursorAdvanceMethods;\n// This is a function to prevent it throwing up in node environments.\nfunction getIdbProxyableTypes() {\n    return (idbProxyableTypes ||\n        (idbProxyableTypes = [\n            IDBDatabase,\n            IDBObjectStore,\n            IDBIndex,\n            IDBCursor,\n            IDBTransaction,\n        ]));\n}\n// This is a function to prevent it throwing up in node environments.\nfunction getCursorAdvanceMethods() {\n    return (cursorAdvanceMethods ||\n        (cursorAdvanceMethods = [\n            IDBCursor.prototype.advance,\n            IDBCursor.prototype.continue,\n            IDBCursor.prototype.continuePrimaryKey,\n        ]));\n}\nconst cursorRequestMap = new WeakMap();\nconst transactionDoneMap = new WeakMap();\nconst transactionStoreNamesMap = new WeakMap();\nconst transformCache = new WeakMap();\nconst reverseTransformCache = new WeakMap();\nfunction promisifyRequest(request) {\n    const promise = new Promise((resolve, reject) => {\n        const unlisten = () => {\n            request.removeEventListener('success', success);\n            request.removeEventListener('error', error);\n        };\n        const success = () => {\n            resolve(wrap(request.result));\n            unlisten();\n        };\n        const error = () => {\n            reject(request.error);\n            unlisten();\n        };\n        request.addEventListener('success', success);\n        request.addEventListener('error', error);\n    });\n    promise\n        .then((value) => {\n        // Since cursoring reuses the IDBRequest (*sigh*), we cache it for later retrieval\n        // (see wrapFunction).\n        if (value instanceof IDBCursor) {\n            cursorRequestMap.set(value, request);\n        }\n        // Catching to avoid \"Uncaught Promise exceptions\"\n    })\n        .catch(() => { });\n    // This mapping exists in reverseTransformCache but doesn't doesn't exist in transformCache. This\n    // is because we create many promises from a single IDBRequest.\n    reverseTransformCache.set(promise, request);\n    return promise;\n}\nfunction cacheDonePromiseForTransaction(tx) {\n    // Early bail if we've already created a done promise for this transaction.\n    if (transactionDoneMap.has(tx))\n        return;\n    const done = new Promise((resolve, reject) => {\n        const unlisten = () => {\n            tx.removeEventListener('complete', complete);\n            tx.removeEventListener('error', error);\n            tx.removeEventListener('abort', error);\n        };\n        const complete = () => {\n            resolve();\n            unlisten();\n        };\n        const error = () => {\n            reject(tx.error || new DOMException('AbortError', 'AbortError'));\n            unlisten();\n        };\n        tx.addEventListener('complete', complete);\n        tx.addEventListener('error', error);\n        tx.addEventListener('abort', error);\n    });\n    // Cache it for later retrieval.\n    transactionDoneMap.set(tx, done);\n}\nlet idbProxyTraps = {\n    get(target, prop, receiver) {\n        if (target instanceof IDBTransaction) {\n            // Special handling for transaction.done.\n            if (prop === 'done')\n                return transactionDoneMap.get(target);\n            // Polyfill for objectStoreNames because of Edge.\n            if (prop === 'objectStoreNames') {\n                return target.objectStoreNames || transactionStoreNamesMap.get(target);\n            }\n            // Make tx.store return the only store in the transaction, or undefined if there are many.\n            if (prop === 'store') {\n                return receiver.objectStoreNames[1]\n                    ? undefined\n                    : receiver.objectStore(receiver.objectStoreNames[0]);\n            }\n        }\n        // Else transform whatever we get back.\n        return wrap(target[prop]);\n    },\n    set(target, prop, value) {\n        target[prop] = value;\n        return true;\n    },\n    has(target, prop) {\n        if (target instanceof IDBTransaction &&\n            (prop === 'done' || prop === 'store')) {\n            return true;\n        }\n        return prop in target;\n    },\n};\nfunction replaceTraps(callback) {\n    idbProxyTraps = callback(idbProxyTraps);\n}\nfunction wrapFunction(func) {\n    // Due to expected object equality (which is enforced by the caching in `wrap`), we\n    // only create one new func per func.\n    // Edge doesn't support objectStoreNames (booo), so we polyfill it here.\n    if (func === IDBDatabase.prototype.transaction &&\n        !('objectStoreNames' in IDBTransaction.prototype)) {\n        return function (storeNames, ...args) {\n            const tx = func.call(unwrap(this), storeNames, ...args);\n            transactionStoreNamesMap.set(tx, storeNames.sort ? storeNames.sort() : [storeNames]);\n            return wrap(tx);\n        };\n    }\n    // Cursor methods are special, as the behaviour is a little more different to standard IDB. In\n    // IDB, you advance the cursor and wait for a new 'success' on the IDBRequest that gave you the\n    // cursor. It's kinda like a promise that can resolve with many values. That doesn't make sense\n    // with real promises, so each advance methods returns a new promise for the cursor object, or\n    // undefined if the end of the cursor has been reached.\n    if (getCursorAdvanceMethods().includes(func)) {\n        return function (...args) {\n            // Calling the original function with the proxy as 'this' causes ILLEGAL INVOCATION, so we use\n            // the original object.\n            func.apply(unwrap(this), args);\n            return wrap(cursorRequestMap.get(this));\n        };\n    }\n    return function (...args) {\n        // Calling the original function with the proxy as 'this' causes ILLEGAL INVOCATION, so we use\n        // the original object.\n        return wrap(func.apply(unwrap(this), args));\n    };\n}\nfunction transformCachableValue(value) {\n    if (typeof value === 'function')\n        return wrapFunction(value);\n    // This doesn't return, it just creates a 'done' promise for the transaction,\n    // which is later returned for transaction.done (see idbObjectHandler).\n    if (value instanceof IDBTransaction)\n        cacheDonePromiseForTransaction(value);\n    if (instanceOfAny(value, getIdbProxyableTypes()))\n        return new Proxy(value, idbProxyTraps);\n    // Return the same value back if we're not going to transform it.\n    return value;\n}\nfunction wrap(value) {\n    // We sometimes generate multiple promises from a single IDBRequest (eg when cursoring), because\n    // IDB is weird and a single IDBRequest can yield many responses, so these can't be cached.\n    if (value instanceof IDBRequest)\n        return promisifyRequest(value);\n    // If we've already transformed this value before, reuse the transformed value.\n    // This is faster, but it also provides object equality.\n    if (transformCache.has(value))\n        return transformCache.get(value);\n    const newValue = transformCachableValue(value);\n    // Not all types are transformed.\n    // These may be primitive types, so they can't be WeakMap keys.\n    if (newValue !== value) {\n        transformCache.set(value, newValue);\n        reverseTransformCache.set(newValue, value);\n    }\n    return newValue;\n}\nconst unwrap = (value) => reverseTransformCache.get(value);\n\nexport { reverseTransformCache as a, instanceOfAny as i, replaceTraps as r, unwrap as u, wrap as w };\n", "import { w as wrap, r as replaceTraps } from './wrap-idb-value.js';\nexport { u as unwrap, w as wrap } from './wrap-idb-value.js';\n\n/**\n * Open a database.\n *\n * @param name Name of the database.\n * @param version Schema version.\n * @param callbacks Additional callbacks.\n */\nfunction openDB(name, version, { blocked, upgrade, blocking, terminated } = {}) {\n    const request = indexedDB.open(name, version);\n    const openPromise = wrap(request);\n    if (upgrade) {\n        request.addEventListener('upgradeneeded', (event) => {\n            upgrade(wrap(request.result), event.oldVersion, event.newVersion, wrap(request.transaction), event);\n        });\n    }\n    if (blocked) {\n        request.addEventListener('blocked', (event) => blocked(\n        // Casting due to https://github.com/microsoft/TypeScript-DOM-lib-generator/pull/1405\n        event.oldVersion, event.newVersion, event));\n    }\n    openPromise\n        .then((db) => {\n        if (terminated)\n            db.addEventListener('close', () => terminated());\n        if (blocking) {\n            db.addEventListener('versionchange', (event) => blocking(event.oldVersion, event.newVersion, event));\n        }\n    })\n        .catch(() => { });\n    return openPromise;\n}\n/**\n * Delete a database.\n *\n * @param name Name of the database.\n */\nfunction deleteDB(name, { blocked } = {}) {\n    const request = indexedDB.deleteDatabase(name);\n    if (blocked) {\n        request.addEventListener('blocked', (event) => blocked(\n        // Casting due to https://github.com/microsoft/TypeScript-DOM-lib-generator/pull/1405\n        event.oldVersion, event));\n    }\n    return wrap(request).then(() => undefined);\n}\n\nconst readMethods = ['get', 'getKey', 'getAll', 'getAllKeys', 'count'];\nconst writeMethods = ['put', 'add', 'delete', 'clear'];\nconst cachedMethods = new Map();\nfunction getMethod(target, prop) {\n    if (!(target instanceof IDBDatabase &&\n        !(prop in target) &&\n        typeof prop === 'string')) {\n        return;\n    }\n    if (cachedMethods.get(prop))\n        return cachedMethods.get(prop);\n    const targetFuncName = prop.replace(/FromIndex$/, '');\n    const useIndex = prop !== targetFuncName;\n    const isWrite = writeMethods.includes(targetFuncName);\n    if (\n    // Bail if the target doesn't exist on the target. Eg, getAll isn't in Edge.\n    !(targetFuncName in (useIndex ? IDBIndex : IDBObjectStore).prototype) ||\n        !(isWrite || readMethods.includes(targetFuncName))) {\n        return;\n    }\n    const method = async function (storeName, ...args) {\n        // isWrite ? 'readwrite' : undefined gzipps better, but fails in Edge :(\n        const tx = this.transaction(storeName, isWrite ? 'readwrite' : 'readonly');\n        let target = tx.store;\n        if (useIndex)\n            target = target.index(args.shift());\n        // Must reject if op rejects.\n        // If it's a write operation, must reject if tx.done rejects.\n        // Must reject with op rejection first.\n        // Must resolve with op value.\n        // Must handle both promises (no unhandled rejections)\n        return (await Promise.all([\n            target[targetFuncName](...args),\n            isWrite && tx.done,\n        ]))[0];\n    };\n    cachedMethods.set(prop, method);\n    return method;\n}\nreplaceTraps((oldTraps) => ({\n    ...oldTraps,\n    get: (target, prop, receiver) => getMethod(target, prop) || oldTraps.get(target, prop, receiver),\n    has: (target, prop) => !!getMethod(target, prop) || oldTraps.has(target, prop),\n}));\n\nexport { deleteDB, openDB };\n", "import { Component, ComponentContainer } from '@firebase/component';\nimport { <PERSON><PERSON>, setUser<PERSON>og<PERSON><PERSON><PERSON>, setLogLevel as setLogLevel$1 } from '@firebase/logger';\nimport { ErrorFactory, base64Decode, getDefaultAppConfig, deepEqual, isBrowser, isWebWorker, FirebaseError, base64urlEncodeWithoutPadding, isIndexedDBAvailable, validateIndexedDBOpenable } from '@firebase/util';\nexport { FirebaseError } from '@firebase/util';\nimport { openDB } from 'idb';\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nclass PlatformLoggerServiceImpl {\n    constructor(container) {\n        this.container = container;\n    }\n    // In initial implementation, this will be called by installations on\n    // auth token refresh, and installations will send this string.\n    getPlatformInfoString() {\n        const providers = this.container.getProviders();\n        // Loop through providers and get library/version pairs from any that are\n        // version components.\n        return providers\n            .map(provider => {\n            if (isVersionServiceProvider(provider)) {\n                const service = provider.getImmediate();\n                return `${service.library}/${service.version}`;\n            }\n            else {\n                return null;\n            }\n        })\n            .filter(logString => logString)\n            .join(' ');\n    }\n}\n/**\n *\n * @param provider check if this provider provides a VersionService\n *\n * NOTE: Using Provider<'app-version'> is a hack to indicate that the provider\n * provides VersionService. The provider is not necessarily a 'app-version'\n * provider.\n */\nfunction isVersionServiceProvider(provider) {\n    const component = provider.getComponent();\n    return (component === null || component === void 0 ? void 0 : component.type) === \"VERSION\" /* ComponentType.VERSION */;\n}\n\nconst name$q = \"@firebase/app\";\nconst version$1 = \"0.13.2\";\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst logger = new Logger('@firebase/app');\n\nconst name$p = \"@firebase/app-compat\";\n\nconst name$o = \"@firebase/analytics-compat\";\n\nconst name$n = \"@firebase/analytics\";\n\nconst name$m = \"@firebase/app-check-compat\";\n\nconst name$l = \"@firebase/app-check\";\n\nconst name$k = \"@firebase/auth\";\n\nconst name$j = \"@firebase/auth-compat\";\n\nconst name$i = \"@firebase/database\";\n\nconst name$h = \"@firebase/data-connect\";\n\nconst name$g = \"@firebase/database-compat\";\n\nconst name$f = \"@firebase/functions\";\n\nconst name$e = \"@firebase/functions-compat\";\n\nconst name$d = \"@firebase/installations\";\n\nconst name$c = \"@firebase/installations-compat\";\n\nconst name$b = \"@firebase/messaging\";\n\nconst name$a = \"@firebase/messaging-compat\";\n\nconst name$9 = \"@firebase/performance\";\n\nconst name$8 = \"@firebase/performance-compat\";\n\nconst name$7 = \"@firebase/remote-config\";\n\nconst name$6 = \"@firebase/remote-config-compat\";\n\nconst name$5 = \"@firebase/storage\";\n\nconst name$4 = \"@firebase/storage-compat\";\n\nconst name$3 = \"@firebase/firestore\";\n\nconst name$2 = \"@firebase/ai\";\n\nconst name$1 = \"@firebase/firestore-compat\";\n\nconst name = \"firebase\";\nconst version = \"11.10.0\";\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * The default app name\n *\n * @internal\n */\nconst DEFAULT_ENTRY_NAME = '[DEFAULT]';\nconst PLATFORM_LOG_STRING = {\n    [name$q]: 'fire-core',\n    [name$p]: 'fire-core-compat',\n    [name$n]: 'fire-analytics',\n    [name$o]: 'fire-analytics-compat',\n    [name$l]: 'fire-app-check',\n    [name$m]: 'fire-app-check-compat',\n    [name$k]: 'fire-auth',\n    [name$j]: 'fire-auth-compat',\n    [name$i]: 'fire-rtdb',\n    [name$h]: 'fire-data-connect',\n    [name$g]: 'fire-rtdb-compat',\n    [name$f]: 'fire-fn',\n    [name$e]: 'fire-fn-compat',\n    [name$d]: 'fire-iid',\n    [name$c]: 'fire-iid-compat',\n    [name$b]: 'fire-fcm',\n    [name$a]: 'fire-fcm-compat',\n    [name$9]: 'fire-perf',\n    [name$8]: 'fire-perf-compat',\n    [name$7]: 'fire-rc',\n    [name$6]: 'fire-rc-compat',\n    [name$5]: 'fire-gcs',\n    [name$4]: 'fire-gcs-compat',\n    [name$3]: 'fire-fst',\n    [name$1]: 'fire-fst-compat',\n    [name$2]: 'fire-vertex',\n    'fire-js': 'fire-js', // Platform identifier for JS SDK.\n    [name]: 'fire-js-all'\n};\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @internal\n */\nconst _apps = new Map();\n/**\n * @internal\n */\nconst _serverApps = new Map();\n/**\n * Registered components.\n *\n * @internal\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nconst _components = new Map();\n/**\n * @param component - the component being added to this app's container\n *\n * @internal\n */\nfunction _addComponent(app, component) {\n    try {\n        app.container.addComponent(component);\n    }\n    catch (e) {\n        logger.debug(`Component ${component.name} failed to register with FirebaseApp ${app.name}`, e);\n    }\n}\n/**\n *\n * @internal\n */\nfunction _addOrOverwriteComponent(app, component) {\n    app.container.addOrOverwriteComponent(component);\n}\n/**\n *\n * @param component - the component to register\n * @returns whether or not the component is registered successfully\n *\n * @internal\n */\nfunction _registerComponent(component) {\n    const componentName = component.name;\n    if (_components.has(componentName)) {\n        logger.debug(`There were multiple attempts to register component ${componentName}.`);\n        return false;\n    }\n    _components.set(componentName, component);\n    // add the component to existing app instances\n    for (const app of _apps.values()) {\n        _addComponent(app, component);\n    }\n    for (const serverApp of _serverApps.values()) {\n        _addComponent(serverApp, component);\n    }\n    return true;\n}\n/**\n *\n * @param app - FirebaseApp instance\n * @param name - service name\n *\n * @returns the provider for the service with the matching name\n *\n * @internal\n */\nfunction _getProvider(app, name) {\n    const heartbeatController = app.container\n        .getProvider('heartbeat')\n        .getImmediate({ optional: true });\n    if (heartbeatController) {\n        void heartbeatController.triggerHeartbeat();\n    }\n    return app.container.getProvider(name);\n}\n/**\n *\n * @param app - FirebaseApp instance\n * @param name - service name\n * @param instanceIdentifier - service instance identifier in case the service supports multiple instances\n *\n * @internal\n */\nfunction _removeServiceInstance(app, name, instanceIdentifier = DEFAULT_ENTRY_NAME) {\n    _getProvider(app, name).clearInstance(instanceIdentifier);\n}\n/**\n *\n * @param obj - an object of type FirebaseApp or FirebaseOptions.\n *\n * @returns true if the provide object is of type FirebaseApp.\n *\n * @internal\n */\nfunction _isFirebaseApp(obj) {\n    return obj.options !== undefined;\n}\n/**\n *\n * @param obj - an object of type FirebaseApp.\n *\n * @returns true if the provided object is of type FirebaseServerAppImpl.\n *\n * @internal\n */\nfunction _isFirebaseServerApp(obj) {\n    if (obj === null || obj === undefined) {\n        return false;\n    }\n    return obj.settings !== undefined;\n}\n/**\n * Test only\n *\n * @internal\n */\nfunction _clearComponents() {\n    _components.clear();\n}\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst ERRORS = {\n    [\"no-app\" /* AppError.NO_APP */]: \"No Firebase App '{$appName}' has been created - \" +\n        'call initializeApp() first',\n    [\"bad-app-name\" /* AppError.BAD_APP_NAME */]: \"Illegal App name: '{$appName}'\",\n    [\"duplicate-app\" /* AppError.DUPLICATE_APP */]: \"Firebase App named '{$appName}' already exists with different options or config\",\n    [\"app-deleted\" /* AppError.APP_DELETED */]: \"Firebase App named '{$appName}' already deleted\",\n    [\"server-app-deleted\" /* AppError.SERVER_APP_DELETED */]: 'Firebase Server App has been deleted',\n    [\"no-options\" /* AppError.NO_OPTIONS */]: 'Need to provide options, when not being deployed to hosting via source.',\n    [\"invalid-app-argument\" /* AppError.INVALID_APP_ARGUMENT */]: 'firebase.{$appName}() takes either no argument or a ' +\n        'Firebase App instance.',\n    [\"invalid-log-argument\" /* AppError.INVALID_LOG_ARGUMENT */]: 'First argument to `onLog` must be null or a function.',\n    [\"idb-open\" /* AppError.IDB_OPEN */]: 'Error thrown when opening IndexedDB. Original error: {$originalErrorMessage}.',\n    [\"idb-get\" /* AppError.IDB_GET */]: 'Error thrown when reading from IndexedDB. Original error: {$originalErrorMessage}.',\n    [\"idb-set\" /* AppError.IDB_WRITE */]: 'Error thrown when writing to IndexedDB. Original error: {$originalErrorMessage}.',\n    [\"idb-delete\" /* AppError.IDB_DELETE */]: 'Error thrown when deleting from IndexedDB. Original error: {$originalErrorMessage}.',\n    [\"finalization-registry-not-supported\" /* AppError.FINALIZATION_REGISTRY_NOT_SUPPORTED */]: 'FirebaseServerApp deleteOnDeref field defined but the JS runtime does not support FinalizationRegistry.',\n    [\"invalid-server-app-environment\" /* AppError.INVALID_SERVER_APP_ENVIRONMENT */]: 'FirebaseServerApp is not for use in browser environments.'\n};\nconst ERROR_FACTORY = new ErrorFactory('app', 'Firebase', ERRORS);\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nclass FirebaseAppImpl {\n    constructor(options, config, container) {\n        this._isDeleted = false;\n        this._options = Object.assign({}, options);\n        this._config = Object.assign({}, config);\n        this._name = config.name;\n        this._automaticDataCollectionEnabled =\n            config.automaticDataCollectionEnabled;\n        this._container = container;\n        this.container.addComponent(new Component('app', () => this, \"PUBLIC\" /* ComponentType.PUBLIC */));\n    }\n    get automaticDataCollectionEnabled() {\n        this.checkDestroyed();\n        return this._automaticDataCollectionEnabled;\n    }\n    set automaticDataCollectionEnabled(val) {\n        this.checkDestroyed();\n        this._automaticDataCollectionEnabled = val;\n    }\n    get name() {\n        this.checkDestroyed();\n        return this._name;\n    }\n    get options() {\n        this.checkDestroyed();\n        return this._options;\n    }\n    get config() {\n        this.checkDestroyed();\n        return this._config;\n    }\n    get container() {\n        return this._container;\n    }\n    get isDeleted() {\n        return this._isDeleted;\n    }\n    set isDeleted(val) {\n        this._isDeleted = val;\n    }\n    /**\n     * This function will throw an Error if the App has already been deleted -\n     * use before performing API actions on the App.\n     */\n    checkDestroyed() {\n        if (this.isDeleted) {\n            throw ERROR_FACTORY.create(\"app-deleted\" /* AppError.APP_DELETED */, { appName: this._name });\n        }\n    }\n}\n\n/**\n * @license\n * Copyright 2023 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// Parse the token and check to see if the `exp` claim is in the future.\n// Reports an error to the console if the token or claim could not be parsed, or if `exp` is in\n// the past.\nfunction validateTokenTTL(base64Token, tokenName) {\n    const secondPart = base64Decode(base64Token.split('.')[1]);\n    if (secondPart === null) {\n        console.error(`FirebaseServerApp ${tokenName} is invalid: second part could not be parsed.`);\n        return;\n    }\n    const expClaim = JSON.parse(secondPart).exp;\n    if (expClaim === undefined) {\n        console.error(`FirebaseServerApp ${tokenName} is invalid: expiration claim could not be parsed`);\n        return;\n    }\n    const exp = JSON.parse(secondPart).exp * 1000;\n    const now = new Date().getTime();\n    const diff = exp - now;\n    if (diff <= 0) {\n        console.error(`FirebaseServerApp ${tokenName} is invalid: the token has expired.`);\n    }\n}\nclass FirebaseServerAppImpl extends FirebaseAppImpl {\n    constructor(options, serverConfig, name, container) {\n        // Build configuration parameters for the FirebaseAppImpl base class.\n        const automaticDataCollectionEnabled = serverConfig.automaticDataCollectionEnabled !== undefined\n            ? serverConfig.automaticDataCollectionEnabled\n            : true;\n        // Create the FirebaseAppSettings object for the FirebaseAppImp constructor.\n        const config = {\n            name,\n            automaticDataCollectionEnabled\n        };\n        if (options.apiKey !== undefined) {\n            // Construct the parent FirebaseAppImp object.\n            super(options, config, container);\n        }\n        else {\n            const appImpl = options;\n            super(appImpl.options, config, container);\n        }\n        // Now construct the data for the FirebaseServerAppImpl.\n        this._serverConfig = Object.assign({ automaticDataCollectionEnabled }, serverConfig);\n        // Ensure that the current time is within the `authIdtoken` window of validity.\n        if (this._serverConfig.authIdToken) {\n            validateTokenTTL(this._serverConfig.authIdToken, 'authIdToken');\n        }\n        // Ensure that the current time is within the `appCheckToken` window of validity.\n        if (this._serverConfig.appCheckToken) {\n            validateTokenTTL(this._serverConfig.appCheckToken, 'appCheckToken');\n        }\n        this._finalizationRegistry = null;\n        if (typeof FinalizationRegistry !== 'undefined') {\n            this._finalizationRegistry = new FinalizationRegistry(() => {\n                this.automaticCleanup();\n            });\n        }\n        this._refCount = 0;\n        this.incRefCount(this._serverConfig.releaseOnDeref);\n        // Do not retain a hard reference to the dref object, otherwise the FinalizationRegistry\n        // will never trigger.\n        this._serverConfig.releaseOnDeref = undefined;\n        serverConfig.releaseOnDeref = undefined;\n        registerVersion(name$q, version$1, 'serverapp');\n    }\n    toJSON() {\n        return undefined;\n    }\n    get refCount() {\n        return this._refCount;\n    }\n    // Increment the reference count of this server app. If an object is provided, register it\n    // with the finalization registry.\n    incRefCount(obj) {\n        if (this.isDeleted) {\n            return;\n        }\n        this._refCount++;\n        if (obj !== undefined && this._finalizationRegistry !== null) {\n            this._finalizationRegistry.register(obj, this);\n        }\n    }\n    // Decrement the reference count.\n    decRefCount() {\n        if (this.isDeleted) {\n            return 0;\n        }\n        return --this._refCount;\n    }\n    // Invoked by the FinalizationRegistry callback to note that this app should go through its\n    // reference counts and delete itself if no reference count remain. The coordinating logic that\n    // handles this is in deleteApp(...).\n    automaticCleanup() {\n        void deleteApp(this);\n    }\n    get settings() {\n        this.checkDestroyed();\n        return this._serverConfig;\n    }\n    /**\n     * This function will throw an Error if the App has already been deleted -\n     * use before performing API actions on the App.\n     */\n    checkDestroyed() {\n        if (this.isDeleted) {\n            throw ERROR_FACTORY.create(\"server-app-deleted\" /* AppError.SERVER_APP_DELETED */);\n        }\n    }\n}\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * The current SDK version.\n *\n * @public\n */\nconst SDK_VERSION = version;\nfunction initializeApp(_options, rawConfig = {}) {\n    let options = _options;\n    if (typeof rawConfig !== 'object') {\n        const name = rawConfig;\n        rawConfig = { name };\n    }\n    const config = Object.assign({ name: DEFAULT_ENTRY_NAME, automaticDataCollectionEnabled: true }, rawConfig);\n    const name = config.name;\n    if (typeof name !== 'string' || !name) {\n        throw ERROR_FACTORY.create(\"bad-app-name\" /* AppError.BAD_APP_NAME */, {\n            appName: String(name)\n        });\n    }\n    options || (options = getDefaultAppConfig());\n    if (!options) {\n        throw ERROR_FACTORY.create(\"no-options\" /* AppError.NO_OPTIONS */);\n    }\n    const existingApp = _apps.get(name);\n    if (existingApp) {\n        // return the existing app if options and config deep equal the ones in the existing app.\n        if (deepEqual(options, existingApp.options) &&\n            deepEqual(config, existingApp.config)) {\n            return existingApp;\n        }\n        else {\n            throw ERROR_FACTORY.create(\"duplicate-app\" /* AppError.DUPLICATE_APP */, { appName: name });\n        }\n    }\n    const container = new ComponentContainer(name);\n    for (const component of _components.values()) {\n        container.addComponent(component);\n    }\n    const newApp = new FirebaseAppImpl(options, config, container);\n    _apps.set(name, newApp);\n    return newApp;\n}\nfunction initializeServerApp(_options, _serverAppConfig) {\n    if (isBrowser() && !isWebWorker()) {\n        // FirebaseServerApp isn't designed to be run in browsers.\n        throw ERROR_FACTORY.create(\"invalid-server-app-environment\" /* AppError.INVALID_SERVER_APP_ENVIRONMENT */);\n    }\n    if (_serverAppConfig.automaticDataCollectionEnabled === undefined) {\n        _serverAppConfig.automaticDataCollectionEnabled = true;\n    }\n    let appOptions;\n    if (_isFirebaseApp(_options)) {\n        appOptions = _options.options;\n    }\n    else {\n        appOptions = _options;\n    }\n    // Build an app name based on a hash of the configuration options.\n    const nameObj = Object.assign(Object.assign({}, _serverAppConfig), appOptions);\n    // However, Do not mangle the name based on releaseOnDeref, since it will vary between the\n    // construction of FirebaseServerApp instances. For example, if the object is the request headers.\n    if (nameObj.releaseOnDeref !== undefined) {\n        delete nameObj.releaseOnDeref;\n    }\n    const hashCode = (s) => {\n        return [...s].reduce((hash, c) => (Math.imul(31, hash) + c.charCodeAt(0)) | 0, 0);\n    };\n    if (_serverAppConfig.releaseOnDeref !== undefined) {\n        if (typeof FinalizationRegistry === 'undefined') {\n            throw ERROR_FACTORY.create(\"finalization-registry-not-supported\" /* AppError.FINALIZATION_REGISTRY_NOT_SUPPORTED */, {});\n        }\n    }\n    const nameString = '' + hashCode(JSON.stringify(nameObj));\n    const existingApp = _serverApps.get(nameString);\n    if (existingApp) {\n        existingApp.incRefCount(_serverAppConfig.releaseOnDeref);\n        return existingApp;\n    }\n    const container = new ComponentContainer(nameString);\n    for (const component of _components.values()) {\n        container.addComponent(component);\n    }\n    const newApp = new FirebaseServerAppImpl(appOptions, _serverAppConfig, nameString, container);\n    _serverApps.set(nameString, newApp);\n    return newApp;\n}\n/**\n * Retrieves a {@link @firebase/app#FirebaseApp} instance.\n *\n * When called with no arguments, the default app is returned. When an app name\n * is provided, the app corresponding to that name is returned.\n *\n * An exception is thrown if the app being retrieved has not yet been\n * initialized.\n *\n * @example\n * ```javascript\n * // Return the default app\n * const app = getApp();\n * ```\n *\n * @example\n * ```javascript\n * // Return a named app\n * const otherApp = getApp(\"otherApp\");\n * ```\n *\n * @param name - Optional name of the app to return. If no name is\n *   provided, the default is `\"[DEFAULT]\"`.\n *\n * @returns The app corresponding to the provided app name.\n *   If no app name is provided, the default app is returned.\n *\n * @public\n */\nfunction getApp(name = DEFAULT_ENTRY_NAME) {\n    const app = _apps.get(name);\n    if (!app && name === DEFAULT_ENTRY_NAME && getDefaultAppConfig()) {\n        return initializeApp();\n    }\n    if (!app) {\n        throw ERROR_FACTORY.create(\"no-app\" /* AppError.NO_APP */, { appName: name });\n    }\n    return app;\n}\n/**\n * A (read-only) array of all initialized apps.\n * @public\n */\nfunction getApps() {\n    return Array.from(_apps.values());\n}\n/**\n * Renders this app unusable and frees the resources of all associated\n * services.\n *\n * @example\n * ```javascript\n * deleteApp(app)\n *   .then(function() {\n *     console.log(\"App deleted successfully\");\n *   })\n *   .catch(function(error) {\n *     console.log(\"Error deleting app:\", error);\n *   });\n * ```\n *\n * @public\n */\nasync function deleteApp(app) {\n    let cleanupProviders = false;\n    const name = app.name;\n    if (_apps.has(name)) {\n        cleanupProviders = true;\n        _apps.delete(name);\n    }\n    else if (_serverApps.has(name)) {\n        const firebaseServerApp = app;\n        if (firebaseServerApp.decRefCount() <= 0) {\n            _serverApps.delete(name);\n            cleanupProviders = true;\n        }\n    }\n    if (cleanupProviders) {\n        await Promise.all(app.container\n            .getProviders()\n            .map(provider => provider.delete()));\n        app.isDeleted = true;\n    }\n}\n/**\n * Registers a library's name and version for platform logging purposes.\n * @param library - Name of 1p or 3p library (e.g. firestore, angularfire)\n * @param version - Current version of that library.\n * @param variant - Bundle variant, e.g., node, rn, etc.\n *\n * @public\n */\nfunction registerVersion(libraryKeyOrName, version, variant) {\n    var _a;\n    // TODO: We can use this check to whitelist strings when/if we set up\n    // a good whitelist system.\n    let library = (_a = PLATFORM_LOG_STRING[libraryKeyOrName]) !== null && _a !== void 0 ? _a : libraryKeyOrName;\n    if (variant) {\n        library += `-${variant}`;\n    }\n    const libraryMismatch = library.match(/\\s|\\//);\n    const versionMismatch = version.match(/\\s|\\//);\n    if (libraryMismatch || versionMismatch) {\n        const warning = [\n            `Unable to register library \"${library}\" with version \"${version}\":`\n        ];\n        if (libraryMismatch) {\n            warning.push(`library name \"${library}\" contains illegal characters (whitespace or \"/\")`);\n        }\n        if (libraryMismatch && versionMismatch) {\n            warning.push('and');\n        }\n        if (versionMismatch) {\n            warning.push(`version name \"${version}\" contains illegal characters (whitespace or \"/\")`);\n        }\n        logger.warn(warning.join(' '));\n        return;\n    }\n    _registerComponent(new Component(`${library}-version`, () => ({ library, version }), \"VERSION\" /* ComponentType.VERSION */));\n}\n/**\n * Sets log handler for all Firebase SDKs.\n * @param logCallback - An optional custom log handler that executes user code whenever\n * the Firebase SDK makes a logging call.\n *\n * @public\n */\nfunction onLog(logCallback, options) {\n    if (logCallback !== null && typeof logCallback !== 'function') {\n        throw ERROR_FACTORY.create(\"invalid-log-argument\" /* AppError.INVALID_LOG_ARGUMENT */);\n    }\n    setUserLogHandler(logCallback, options);\n}\n/**\n * Sets log level for all Firebase SDKs.\n *\n * All of the log types above the current log level are captured (i.e. if\n * you set the log level to `info`, errors are logged, but `debug` and\n * `verbose` logs are not).\n *\n * @public\n */\nfunction setLogLevel(logLevel) {\n    setLogLevel$1(logLevel);\n}\n\n/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst DB_NAME = 'firebase-heartbeat-database';\nconst DB_VERSION = 1;\nconst STORE_NAME = 'firebase-heartbeat-store';\nlet dbPromise = null;\nfunction getDbPromise() {\n    if (!dbPromise) {\n        dbPromise = openDB(DB_NAME, DB_VERSION, {\n            upgrade: (db, oldVersion) => {\n                // We don't use 'break' in this switch statement, the fall-through\n                // behavior is what we want, because if there are multiple versions between\n                // the old version and the current version, we want ALL the migrations\n                // that correspond to those versions to run, not only the last one.\n                // eslint-disable-next-line default-case\n                switch (oldVersion) {\n                    case 0:\n                        try {\n                            db.createObjectStore(STORE_NAME);\n                        }\n                        catch (e) {\n                            // Safari/iOS browsers throw occasional exceptions on\n                            // db.createObjectStore() that may be a bug. Avoid blocking\n                            // the rest of the app functionality.\n                            console.warn(e);\n                        }\n                }\n            }\n        }).catch(e => {\n            throw ERROR_FACTORY.create(\"idb-open\" /* AppError.IDB_OPEN */, {\n                originalErrorMessage: e.message\n            });\n        });\n    }\n    return dbPromise;\n}\nasync function readHeartbeatsFromIndexedDB(app) {\n    try {\n        const db = await getDbPromise();\n        const tx = db.transaction(STORE_NAME);\n        const result = await tx.objectStore(STORE_NAME).get(computeKey(app));\n        // We already have the value but tx.done can throw,\n        // so we need to await it here to catch errors\n        await tx.done;\n        return result;\n    }\n    catch (e) {\n        if (e instanceof FirebaseError) {\n            logger.warn(e.message);\n        }\n        else {\n            const idbGetError = ERROR_FACTORY.create(\"idb-get\" /* AppError.IDB_GET */, {\n                originalErrorMessage: e === null || e === void 0 ? void 0 : e.message\n            });\n            logger.warn(idbGetError.message);\n        }\n    }\n}\nasync function writeHeartbeatsToIndexedDB(app, heartbeatObject) {\n    try {\n        const db = await getDbPromise();\n        const tx = db.transaction(STORE_NAME, 'readwrite');\n        const objectStore = tx.objectStore(STORE_NAME);\n        await objectStore.put(heartbeatObject, computeKey(app));\n        await tx.done;\n    }\n    catch (e) {\n        if (e instanceof FirebaseError) {\n            logger.warn(e.message);\n        }\n        else {\n            const idbGetError = ERROR_FACTORY.create(\"idb-set\" /* AppError.IDB_WRITE */, {\n                originalErrorMessage: e === null || e === void 0 ? void 0 : e.message\n            });\n            logger.warn(idbGetError.message);\n        }\n    }\n}\nfunction computeKey(app) {\n    return `${app.name}!${app.options.appId}`;\n}\n\n/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst MAX_HEADER_BYTES = 1024;\nconst MAX_NUM_STORED_HEARTBEATS = 30;\nclass HeartbeatServiceImpl {\n    constructor(container) {\n        this.container = container;\n        /**\n         * In-memory cache for heartbeats, used by getHeartbeatsHeader() to generate\n         * the header string.\n         * Stores one record per date. This will be consolidated into the standard\n         * format of one record per user agent string before being sent as a header.\n         * Populated from indexedDB when the controller is instantiated and should\n         * be kept in sync with indexedDB.\n         * Leave public for easier testing.\n         */\n        this._heartbeatsCache = null;\n        const app = this.container.getProvider('app').getImmediate();\n        this._storage = new HeartbeatStorageImpl(app);\n        this._heartbeatsCachePromise = this._storage.read().then(result => {\n            this._heartbeatsCache = result;\n            return result;\n        });\n    }\n    /**\n     * Called to report a heartbeat. The function will generate\n     * a HeartbeatsByUserAgent object, update heartbeatsCache, and persist it\n     * to IndexedDB.\n     * Note that we only store one heartbeat per day. So if a heartbeat for today is\n     * already logged, subsequent calls to this function in the same day will be ignored.\n     */\n    async triggerHeartbeat() {\n        var _a, _b;\n        try {\n            const platformLogger = this.container\n                .getProvider('platform-logger')\n                .getImmediate();\n            // This is the \"Firebase user agent\" string from the platform logger\n            // service, not the browser user agent.\n            const agent = platformLogger.getPlatformInfoString();\n            const date = getUTCDateString();\n            if (((_a = this._heartbeatsCache) === null || _a === void 0 ? void 0 : _a.heartbeats) == null) {\n                this._heartbeatsCache = await this._heartbeatsCachePromise;\n                // If we failed to construct a heartbeats cache, then return immediately.\n                if (((_b = this._heartbeatsCache) === null || _b === void 0 ? void 0 : _b.heartbeats) == null) {\n                    return;\n                }\n            }\n            // Do not store a heartbeat if one is already stored for this day\n            // or if a header has already been sent today.\n            if (this._heartbeatsCache.lastSentHeartbeatDate === date ||\n                this._heartbeatsCache.heartbeats.some(singleDateHeartbeat => singleDateHeartbeat.date === date)) {\n                return;\n            }\n            else {\n                // There is no entry for this date. Create one.\n                this._heartbeatsCache.heartbeats.push({ date, agent });\n                // If the number of stored heartbeats exceeds the maximum number of stored heartbeats, remove the heartbeat with the earliest date.\n                // Since this is executed each time a heartbeat is pushed, the limit can only be exceeded by one, so only one needs to be removed.\n                if (this._heartbeatsCache.heartbeats.length > MAX_NUM_STORED_HEARTBEATS) {\n                    const earliestHeartbeatIdx = getEarliestHeartbeatIdx(this._heartbeatsCache.heartbeats);\n                    this._heartbeatsCache.heartbeats.splice(earliestHeartbeatIdx, 1);\n                }\n            }\n            return this._storage.overwrite(this._heartbeatsCache);\n        }\n        catch (e) {\n            logger.warn(e);\n        }\n    }\n    /**\n     * Returns a base64 encoded string which can be attached to the heartbeat-specific header directly.\n     * It also clears all heartbeats from memory as well as in IndexedDB.\n     *\n     * NOTE: Consuming product SDKs should not send the header if this method\n     * returns an empty string.\n     */\n    async getHeartbeatsHeader() {\n        var _a;\n        try {\n            if (this._heartbeatsCache === null) {\n                await this._heartbeatsCachePromise;\n            }\n            // If it's still null or the array is empty, there is no data to send.\n            if (((_a = this._heartbeatsCache) === null || _a === void 0 ? void 0 : _a.heartbeats) == null ||\n                this._heartbeatsCache.heartbeats.length === 0) {\n                return '';\n            }\n            const date = getUTCDateString();\n            // Extract as many heartbeats from the cache as will fit under the size limit.\n            const { heartbeatsToSend, unsentEntries } = extractHeartbeatsForHeader(this._heartbeatsCache.heartbeats);\n            const headerString = base64urlEncodeWithoutPadding(JSON.stringify({ version: 2, heartbeats: heartbeatsToSend }));\n            // Store last sent date to prevent another being logged/sent for the same day.\n            this._heartbeatsCache.lastSentHeartbeatDate = date;\n            if (unsentEntries.length > 0) {\n                // Store any unsent entries if they exist.\n                this._heartbeatsCache.heartbeats = unsentEntries;\n                // This seems more likely than emptying the array (below) to lead to some odd state\n                // since the cache isn't empty and this will be called again on the next request,\n                // and is probably safest if we await it.\n                await this._storage.overwrite(this._heartbeatsCache);\n            }\n            else {\n                this._heartbeatsCache.heartbeats = [];\n                // Do not wait for this, to reduce latency.\n                void this._storage.overwrite(this._heartbeatsCache);\n            }\n            return headerString;\n        }\n        catch (e) {\n            logger.warn(e);\n            return '';\n        }\n    }\n}\nfunction getUTCDateString() {\n    const today = new Date();\n    // Returns date format 'YYYY-MM-DD'\n    return today.toISOString().substring(0, 10);\n}\nfunction extractHeartbeatsForHeader(heartbeatsCache, maxSize = MAX_HEADER_BYTES) {\n    // Heartbeats grouped by user agent in the standard format to be sent in\n    // the header.\n    const heartbeatsToSend = [];\n    // Single date format heartbeats that are not sent.\n    let unsentEntries = heartbeatsCache.slice();\n    for (const singleDateHeartbeat of heartbeatsCache) {\n        // Look for an existing entry with the same user agent.\n        const heartbeatEntry = heartbeatsToSend.find(hb => hb.agent === singleDateHeartbeat.agent);\n        if (!heartbeatEntry) {\n            // If no entry for this user agent exists, create one.\n            heartbeatsToSend.push({\n                agent: singleDateHeartbeat.agent,\n                dates: [singleDateHeartbeat.date]\n            });\n            if (countBytes(heartbeatsToSend) > maxSize) {\n                // If the header would exceed max size, remove the added heartbeat\n                // entry and stop adding to the header.\n                heartbeatsToSend.pop();\n                break;\n            }\n        }\n        else {\n            heartbeatEntry.dates.push(singleDateHeartbeat.date);\n            // If the header would exceed max size, remove the added date\n            // and stop adding to the header.\n            if (countBytes(heartbeatsToSend) > maxSize) {\n                heartbeatEntry.dates.pop();\n                break;\n            }\n        }\n        // Pop unsent entry from queue. (Skipped if adding the entry exceeded\n        // quota and the loop breaks early.)\n        unsentEntries = unsentEntries.slice(1);\n    }\n    return {\n        heartbeatsToSend,\n        unsentEntries\n    };\n}\nclass HeartbeatStorageImpl {\n    constructor(app) {\n        this.app = app;\n        this._canUseIndexedDBPromise = this.runIndexedDBEnvironmentCheck();\n    }\n    async runIndexedDBEnvironmentCheck() {\n        if (!isIndexedDBAvailable()) {\n            return false;\n        }\n        else {\n            return validateIndexedDBOpenable()\n                .then(() => true)\n                .catch(() => false);\n        }\n    }\n    /**\n     * Read all heartbeats.\n     */\n    async read() {\n        const canUseIndexedDB = await this._canUseIndexedDBPromise;\n        if (!canUseIndexedDB) {\n            return { heartbeats: [] };\n        }\n        else {\n            const idbHeartbeatObject = await readHeartbeatsFromIndexedDB(this.app);\n            if (idbHeartbeatObject === null || idbHeartbeatObject === void 0 ? void 0 : idbHeartbeatObject.heartbeats) {\n                return idbHeartbeatObject;\n            }\n            else {\n                return { heartbeats: [] };\n            }\n        }\n    }\n    // overwrite the storage with the provided heartbeats\n    async overwrite(heartbeatsObject) {\n        var _a;\n        const canUseIndexedDB = await this._canUseIndexedDBPromise;\n        if (!canUseIndexedDB) {\n            return;\n        }\n        else {\n            const existingHeartbeatsObject = await this.read();\n            return writeHeartbeatsToIndexedDB(this.app, {\n                lastSentHeartbeatDate: (_a = heartbeatsObject.lastSentHeartbeatDate) !== null && _a !== void 0 ? _a : existingHeartbeatsObject.lastSentHeartbeatDate,\n                heartbeats: heartbeatsObject.heartbeats\n            });\n        }\n    }\n    // add heartbeats\n    async add(heartbeatsObject) {\n        var _a;\n        const canUseIndexedDB = await this._canUseIndexedDBPromise;\n        if (!canUseIndexedDB) {\n            return;\n        }\n        else {\n            const existingHeartbeatsObject = await this.read();\n            return writeHeartbeatsToIndexedDB(this.app, {\n                lastSentHeartbeatDate: (_a = heartbeatsObject.lastSentHeartbeatDate) !== null && _a !== void 0 ? _a : existingHeartbeatsObject.lastSentHeartbeatDate,\n                heartbeats: [\n                    ...existingHeartbeatsObject.heartbeats,\n                    ...heartbeatsObject.heartbeats\n                ]\n            });\n        }\n    }\n}\n/**\n * Calculate bytes of a HeartbeatsByUserAgent array after being wrapped\n * in a platform logging header JSON object, stringified, and converted\n * to base 64.\n */\nfunction countBytes(heartbeatsCache) {\n    // base64 has a restricted set of characters, all of which should be 1 byte.\n    return base64urlEncodeWithoutPadding(\n    // heartbeatsCache wrapper properties\n    JSON.stringify({ version: 2, heartbeats: heartbeatsCache })).length;\n}\n/**\n * Returns the index of the heartbeat with the earliest date.\n * If the heartbeats array is empty, -1 is returned.\n */\nfunction getEarliestHeartbeatIdx(heartbeats) {\n    if (heartbeats.length === 0) {\n        return -1;\n    }\n    let earliestHeartbeatIdx = 0;\n    let earliestHeartbeatDate = heartbeats[0].date;\n    for (let i = 1; i < heartbeats.length; i++) {\n        if (heartbeats[i].date < earliestHeartbeatDate) {\n            earliestHeartbeatDate = heartbeats[i].date;\n            earliestHeartbeatIdx = i;\n        }\n    }\n    return earliestHeartbeatIdx;\n}\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nfunction registerCoreComponents(variant) {\n    _registerComponent(new Component('platform-logger', container => new PlatformLoggerServiceImpl(container), \"PRIVATE\" /* ComponentType.PRIVATE */));\n    _registerComponent(new Component('heartbeat', container => new HeartbeatServiceImpl(container), \"PRIVATE\" /* ComponentType.PRIVATE */));\n    // Register `app` package.\n    registerVersion(name$q, version$1, variant);\n    // BUILD_TARGET will be replaced by values like esm2017, cjs2017, etc during the compilation\n    registerVersion(name$q, version$1, 'esm2017');\n    // Register platform SDK identifier (no version).\n    registerVersion('fire-js', '');\n}\n\n/**\n * Firebase App\n *\n * @remarks This package coordinates the communication between the different Firebase components\n * @packageDocumentation\n */\nregisterCoreComponents('');\n\nexport { SDK_VERSION, DEFAULT_ENTRY_NAME as _DEFAULT_ENTRY_NAME, _addComponent, _addOrOverwriteComponent, _apps, _clearComponents, _components, _getProvider, _isFirebaseApp, _isFirebaseServerApp, _registerComponent, _removeServiceInstance, _serverApps, deleteApp, getApp, getApps, initializeApp, initializeServerApp, onLog, registerVersion, setLogLevel };\n\n", "import { registerVersion } from '@firebase/app';\nexport * from '@firebase/app';\n\nvar name = \"firebase\";\nvar version = \"11.10.0\";\n\n/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nregisterVersion(name, version, 'app');\n\n", "/**\n * @license Angular v20.2.3\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { Observable, ReplaySubject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport { assertInInjectionContext, inject, DestroyRef, RuntimeError, Injector, assertNotInReactiveContext, signal, PendingTasks } from './root_effect_scheduler.mjs';\nimport { getOutputDestroyRef, effect, untracked, computed, resource, encapsulateResourceError } from './resource.mjs';\nimport './not_found.mjs';\nimport './signal.mjs';\nimport '@angular/core/primitives/signals';\nimport '@angular/core/primitives/di';\nimport './effect.mjs';\n\n/**\n * Operator which completes the Observable when the calling context (component, directive, service,\n * etc) is destroyed.\n *\n * @param destroyRef optionally, the `DestroyRef` representing the current context. This can be\n *     passed explicitly to use `takeUntilDestroyed` outside of an [injection\n * context](guide/di/dependency-injection-context). Otherwise, the current `DestroyRef` is injected.\n *\n * @publicApi 19.0\n */\nfunction takeUntilDestroyed(destroyRef) {\n    if (!destroyRef) {\n        ngDevMode && assertInInjectionContext(takeUntilDestroyed);\n        destroyRef = inject(DestroyRef);\n    }\n    const destroyed$ = new Observable((subscriber) => {\n        if (destroyRef.destroyed) {\n            subscriber.next();\n            return;\n        }\n        const unregisterFn = destroyRef.onDestroy(subscriber.next.bind(subscriber));\n        return unregisterFn;\n    });\n    return (source) => {\n        return source.pipe(takeUntil(destroyed$));\n    };\n}\n\n/**\n * Implementation of `OutputRef` that emits values from\n * an RxJS observable source.\n *\n * @internal\n */\nclass OutputFromObservableRef {\n    source;\n    destroyed = false;\n    destroyRef = inject(DestroyRef);\n    constructor(source) {\n        this.source = source;\n        this.destroyRef.onDestroy(() => {\n            this.destroyed = true;\n        });\n    }\n    subscribe(callbackFn) {\n        if (this.destroyed) {\n            throw new RuntimeError(953 /* ɵRuntimeErrorCode.OUTPUT_REF_DESTROYED */, ngDevMode &&\n                'Unexpected subscription to destroyed `OutputRef`. ' +\n                    'The owning directive/component is destroyed.');\n        }\n        // Stop yielding more values when the directive/component is already destroyed.\n        const subscription = this.source.pipe(takeUntilDestroyed(this.destroyRef)).subscribe({\n            next: (value) => callbackFn(value),\n        });\n        return {\n            unsubscribe: () => subscription.unsubscribe(),\n        };\n    }\n}\n/**\n * Declares an Angular output that is using an RxJS observable as a source\n * for events dispatched to parent subscribers.\n *\n * The behavior for an observable as source is defined as followed:\n *    1. New values are forwarded to the Angular output (next notifications).\n *    2. Errors notifications are not handled by Angular. You need to handle these manually.\n *       For example by using `catchError`.\n *    3. Completion notifications stop the output from emitting new values.\n *\n * @usageNotes\n * Initialize an output in your directive by declaring a\n * class field and initializing it with the `outputFromObservable()` function.\n *\n * ```ts\n * @Directive({..})\n * export class MyDir {\n *   nameChange$ = <some-observable>;\n *   nameChange = outputFromObservable(this.nameChange$);\n * }\n * ```\n *\n * @publicApi 19.0\n */\nfunction outputFromObservable(observable, opts) {\n    ngDevMode && assertInInjectionContext(outputFromObservable);\n    return new OutputFromObservableRef(observable);\n}\n\n/**\n * Converts an Angular output declared via `output()` or `outputFromObservable()`\n * to an observable.\n *\n * You can subscribe to the output via `Observable.subscribe` then.\n *\n * @publicApi 19.0\n */\nfunction outputToObservable(ref) {\n    const destroyRef = getOutputDestroyRef(ref);\n    return new Observable((observer) => {\n        // Complete the observable upon directive/component destroy.\n        // Note: May be `undefined` if an `EventEmitter` is declared outside\n        // of an injection context.\n        const unregisterOnDestroy = destroyRef?.onDestroy(() => observer.complete());\n        const subscription = ref.subscribe((v) => observer.next(v));\n        return () => {\n            subscription.unsubscribe();\n            unregisterOnDestroy?.();\n        };\n    });\n}\n\n/**\n * Exposes the value of an Angular `Signal` as an RxJS `Observable`.\n *\n * The signal's value will be propagated into the `Observable`'s subscribers using an `effect`.\n *\n * `toObservable` must be called in an injection context unless an injector is provided via options.\n *\n * @publicApi 20.0\n */\nfunction toObservable(source, options) {\n    if (ngDevMode && !options?.injector) {\n        assertInInjectionContext(toObservable);\n    }\n    const injector = options?.injector ?? inject(Injector);\n    const subject = new ReplaySubject(1);\n    const watcher = effect(() => {\n        let value;\n        try {\n            value = source();\n        }\n        catch (err) {\n            untracked(() => subject.error(err));\n            return;\n        }\n        untracked(() => subject.next(value));\n    }, { injector, manualCleanup: true });\n    injector.get(DestroyRef).onDestroy(() => {\n        watcher.destroy();\n        subject.complete();\n    });\n    return subject.asObservable();\n}\n\n/**\n * Get the current value of an `Observable` as a reactive `Signal`.\n *\n * `toSignal` returns a `Signal` which provides synchronous reactive access to values produced\n * by the given `Observable`, by subscribing to that `Observable`. The returned `Signal` will always\n * have the most recent value emitted by the subscription, and will throw an error if the\n * `Observable` errors.\n *\n * With `requireSync` set to `true`, `toSignal` will assert that the `Observable` produces a value\n * immediately upon subscription. No `initialValue` is needed in this case, and the returned signal\n * does not include an `undefined` type.\n *\n * By default, the subscription will be automatically cleaned up when the current [injection\n * context](guide/di/dependency-injection-context) is destroyed. For example, when `toSignal` is\n * called during the construction of a component, the subscription will be cleaned up when the\n * component is destroyed. If an injection context is not available, an explicit `Injector` can be\n * passed instead.\n *\n * If the subscription should persist until the `Observable` itself completes, the `manualCleanup`\n * option can be specified instead, which disables the automatic subscription teardown. No injection\n * context is needed in this configuration as well.\n */\nfunction toSignal(source, options) {\n    typeof ngDevMode !== 'undefined' &&\n        ngDevMode &&\n        assertNotInReactiveContext(toSignal, 'Invoking `toSignal` causes new subscriptions every time. ' +\n            'Consider moving `toSignal` outside of the reactive context and read the signal value where needed.');\n    const requiresCleanup = !options?.manualCleanup;\n    if (ngDevMode && requiresCleanup && !options?.injector) {\n        assertInInjectionContext(toSignal);\n    }\n    const cleanupRef = requiresCleanup\n        ? (options?.injector?.get(DestroyRef) ?? inject(DestroyRef))\n        : null;\n    const equal = makeToSignalEqual(options?.equal);\n    // Note: T is the Observable value type, and U is the initial value type. They don't have to be\n    // the same - the returned signal gives values of type `T`.\n    let state;\n    if (options?.requireSync) {\n        // Initially the signal is in a `NoValue` state.\n        state = signal({ kind: 0 /* StateKind.NoValue */ }, { equal });\n    }\n    else {\n        // If an initial value was passed, use it. Otherwise, use `undefined` as the initial value.\n        state = signal({ kind: 1 /* StateKind.Value */, value: options?.initialValue }, { equal });\n    }\n    let destroyUnregisterFn;\n    // Note: This code cannot run inside a reactive context (see assertion above). If we'd support\n    // this, we would subscribe to the observable outside of the current reactive context, avoiding\n    // that side-effect signal reads/writes are attribute to the current consumer. The current\n    // consumer only needs to be notified when the `state` signal changes through the observable\n    // subscription. Additional context (related to async pipe):\n    // https://github.com/angular/angular/pull/50522.\n    const sub = source.subscribe({\n        next: (value) => state.set({ kind: 1 /* StateKind.Value */, value }),\n        error: (error) => {\n            state.set({ kind: 2 /* StateKind.Error */, error });\n            destroyUnregisterFn?.();\n        },\n        complete: () => {\n            destroyUnregisterFn?.();\n        },\n        // Completion of the Observable is meaningless to the signal. Signals don't have a concept of\n        // \"complete\".\n    });\n    if (options?.requireSync && state().kind === 0 /* StateKind.NoValue */) {\n        throw new RuntimeError(601 /* ɵRuntimeErrorCode.REQUIRE_SYNC_WITHOUT_SYNC_EMIT */, (typeof ngDevMode === 'undefined' || ngDevMode) &&\n            '`toSignal()` called with `requireSync` but `Observable` did not emit synchronously.');\n    }\n    // Unsubscribe when the current context is destroyed, if requested.\n    destroyUnregisterFn = cleanupRef?.onDestroy(sub.unsubscribe.bind(sub));\n    // The actual returned signal is a `computed` of the `State` signal, which maps the various states\n    // to either values or errors.\n    return computed(() => {\n        const current = state();\n        switch (current.kind) {\n            case 1 /* StateKind.Value */:\n                return current.value;\n            case 2 /* StateKind.Error */:\n                throw current.error;\n            case 0 /* StateKind.NoValue */:\n                // This shouldn't really happen because the error is thrown on creation.\n                throw new RuntimeError(601 /* ɵRuntimeErrorCode.REQUIRE_SYNC_WITHOUT_SYNC_EMIT */, (typeof ngDevMode === 'undefined' || ngDevMode) &&\n                    '`toSignal()` called with `requireSync` but `Observable` did not emit synchronously.');\n        }\n    }, { equal: options?.equal });\n}\nfunction makeToSignalEqual(userEquality = Object.is) {\n    return (a, b) => a.kind === 1 /* StateKind.Value */ && b.kind === 1 /* StateKind.Value */ && userEquality(a.value, b.value);\n}\n\n/**\n * Operator which makes the application unstable until the observable emits, completes, errors, or is unsubscribed.\n *\n * Use this operator in observables whose subscriptions are important for rendering and should be included in SSR serialization.\n *\n * @param injector The `Injector` to use during creation. If this is not provided, the current injection context will be used instead (via `inject`).\n *\n * @developerPreview 20.0\n */\nfunction pendingUntilEvent(injector) {\n    if (injector === undefined) {\n        ngDevMode && assertInInjectionContext(pendingUntilEvent);\n        injector = inject(Injector);\n    }\n    const taskService = injector.get(PendingTasks);\n    return (sourceObservable) => {\n        return new Observable((originalSubscriber) => {\n            // create a new task on subscription\n            const removeTask = taskService.add();\n            let cleanedUp = false;\n            function cleanupTask() {\n                if (cleanedUp) {\n                    return;\n                }\n                removeTask();\n                cleanedUp = true;\n            }\n            const innerSubscription = sourceObservable.subscribe({\n                next: (v) => {\n                    originalSubscriber.next(v);\n                    cleanupTask();\n                },\n                complete: () => {\n                    originalSubscriber.complete();\n                    cleanupTask();\n                },\n                error: (e) => {\n                    originalSubscriber.error(e);\n                    cleanupTask();\n                },\n            });\n            innerSubscription.add(() => {\n                originalSubscriber.unsubscribe();\n                cleanupTask();\n            });\n            return innerSubscription;\n        });\n    };\n}\n\nfunction rxResource(opts) {\n    if (ngDevMode && !opts?.injector) {\n        assertInInjectionContext(rxResource);\n    }\n    return resource({\n        ...opts,\n        loader: undefined,\n        stream: (params) => {\n            let sub;\n            // Track the abort listener so it can be removed if the Observable completes (as a memory\n            // optimization).\n            const onAbort = () => sub?.unsubscribe();\n            params.abortSignal.addEventListener('abort', onAbort);\n            // Start off stream as undefined.\n            const stream = signal({ value: undefined });\n            let resolve;\n            const promise = new Promise((r) => (resolve = r));\n            function send(value) {\n                stream.set(value);\n                resolve?.(stream);\n                resolve = undefined;\n            }\n            // TODO(alxhub): remove after g3 updated to rename loader -> stream\n            const streamFn = opts.stream ?? opts.loader;\n            if (streamFn === undefined) {\n                throw new RuntimeError(990 /* ɵRuntimeErrorCode.MUST_PROVIDE_STREAM_OPTION */, ngDevMode && `Must provide \\`stream\\` option.`);\n            }\n            sub = streamFn(params).subscribe({\n                next: (value) => send({ value }),\n                error: (error) => {\n                    send({ error: encapsulateResourceError(error) });\n                    params.abortSignal.removeEventListener('abort', onAbort);\n                },\n                complete: () => {\n                    if (resolve) {\n                        send({\n                            error: new RuntimeError(991 /* ɵRuntimeErrorCode.RESOURCE_COMPLETED_BEFORE_PRODUCING_VALUE */, ngDevMode && 'Resource completed before producing a value'),\n                        });\n                    }\n                    params.abortSignal.removeEventListener('abort', onAbort);\n                },\n            });\n            return promise;\n        },\n    });\n}\n\nexport { outputFromObservable, outputToObservable, pendingUntilEvent, rxResource, takeUntilDestroyed, toObservable, toSignal };\n\n", "import * as i0 from '@angular/core';\nimport { Version, isDevMode, inject, NgZone, Injectable, runInInjectionContext, PendingTasks, EnvironmentInjector } from '@angular/core';\nimport { getApps } from 'firebase/app';\nimport { pendingUntilEvent } from '@angular/core/rxjs-interop';\nimport { queueScheduler, asyncScheduler, Observable } from 'rxjs';\nimport { subscribeOn, observeOn } from 'rxjs/operators';\nconst VERSION = new Version('ANGULARFIRE2_VERSION');\nconst ɵisSupportedError = module => `The APP_INITIALIZER that is \"making\" isSupported() sync for the sake of convenient DI has not resolved in this\ncontext. Rather than injecting ${module} in the constructor, first ensure that ${module} is supported by calling\n\\`await isSupported()\\`, then retrieve the instance from the injector manually \\`injector.get(${module})\\`.`;\nfunction ɵgetDefaultInstanceOf(identifier, provided, defaultApp) {\n  if (provided) {\n    // Was provide* only called once? If so grab that\n    if (provided.length === 1) {\n      return provided[0];\n    }\n    const providedUsingDefaultApp = provided.filter(it => it.app === defaultApp);\n    // Was provide* only called once, using the default app? If so use that\n    if (providedUsingDefaultApp.length === 1) {\n      return providedUsingDefaultApp[0];\n    }\n  }\n  // Grab the default instance from the defaultApp\n  const defaultAppWithContainer = defaultApp;\n  const provider = defaultAppWithContainer.container.getProvider(identifier);\n  return provider.getImmediate({\n    optional: true\n  });\n}\nconst ɵgetAllInstancesOf = (identifier, app) => {\n  const apps = app ? [app] : getApps();\n  const instances = [];\n  apps.forEach(app => {\n    const provider = app.container.getProvider(identifier);\n    provider.instances.forEach(instance => {\n      if (!instances.includes(instance)) {\n        instances.push(instance);\n      }\n    });\n  });\n  return instances;\n};\n\n/* eslint-disable @typescript-eslint/ban-ts-comment */\nvar LogLevel;\n(function (LogLevel) {\n  LogLevel[LogLevel[\"SILENT\"] = 0] = \"SILENT\";\n  LogLevel[LogLevel[\"WARN\"] = 1] = \"WARN\";\n  LogLevel[LogLevel[\"VERBOSE\"] = 2] = \"VERBOSE\";\n})(LogLevel || (LogLevel = {}));\nvar currentLogLevel = isDevMode() && typeof Zone !== \"undefined\" ? LogLevel.WARN : LogLevel.SILENT;\nconst setLogLevel = logLevel => currentLogLevel = logLevel;\n/**\n * Schedules tasks so that they are invoked inside the Zone that is passed in the constructor.\n */\nclass ɵZoneScheduler {\n  zone;\n  delegate;\n  constructor(zone, delegate = queueScheduler) {\n    this.zone = zone;\n    this.delegate = delegate;\n  }\n  now() {\n    return this.delegate.now();\n  }\n  schedule(work, delay, state) {\n    const targetZone = this.zone;\n    // Wrap the specified work function to make sure that if nested scheduling takes place the\n    // work is executed in the correct zone\n    const workInZone = function (state) {\n      if (targetZone) {\n        targetZone.runGuarded(() => {\n          work.apply(this, [state]);\n        });\n      } else {\n        work.apply(this, [state]);\n      }\n    };\n    // Scheduling itself needs to be run in zone to ensure setInterval calls for async scheduling are done\n    // inside the correct zone. This scheduler needs to schedule asynchronously always to ensure that\n    // firebase emissions are never synchronous. Specifying a delay causes issues with the queueScheduler delegate.\n    return this.delegate.schedule(workInZone, delay, state);\n  }\n}\nclass ɵAngularFireSchedulers {\n  outsideAngular;\n  insideAngular;\n  constructor() {\n    const ngZone = inject(NgZone);\n    this.outsideAngular = ngZone.runOutsideAngular(() => new ɵZoneScheduler(typeof Zone === 'undefined' ? undefined : Zone.current));\n    this.insideAngular = ngZone.run(() => new ɵZoneScheduler(typeof Zone === 'undefined' ? undefined : Zone.current, asyncScheduler));\n  }\n  static ɵfac = function ɵAngularFireSchedulers_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ɵAngularFireSchedulers)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: ɵAngularFireSchedulers,\n    factory: ɵAngularFireSchedulers.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ɵAngularFireSchedulers, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\nvar alreadyWarned = false;\nfunction warnOutsideInjectionContext(original, logLevel) {\n  if (!alreadyWarned && (currentLogLevel > LogLevel.SILENT || isDevMode())) {\n    alreadyWarned = true;\n    console.warn(\"Calling Firebase APIs outside of an Injection context may destabilize your application leading to subtle change-detection and hydration bugs. Find more at https://github.com/angular/angularfire/blob/main/docs/zones.md\");\n  }\n  if (currentLogLevel >= logLevel) {\n    console.warn(`Firebase API called outside injection context: ${original.name}`);\n  }\n}\nfunction runOutsideAngular(fn) {\n  const ngZone = inject(NgZone, {\n    optional: true\n  });\n  if (!ngZone) {\n    return fn();\n  }\n  return ngZone.runOutsideAngular(() => fn());\n}\nfunction run(fn) {\n  const ngZone = inject(NgZone, {\n    optional: true\n  });\n  if (!ngZone) {\n    return fn();\n  }\n  return ngZone.run(() => fn());\n}\nconst zoneWrapFn = (it, taskDone, injector) => {\n  return (...args) => {\n    if (taskDone) {\n      setTimeout(taskDone, 0);\n    }\n    return runInInjectionContext(injector, () => run(() => it.apply(this, args)));\n  };\n};\nconst ɵzoneWrap = (it, blockUntilFirst, logLevel) => {\n  logLevel ||= blockUntilFirst ? LogLevel.WARN : LogLevel.VERBOSE;\n  // function() is needed for the arguments object\n  return function () {\n    let taskDone;\n    const _arguments = arguments;\n    let schedulers;\n    let pendingTasks;\n    let injector;\n    try {\n      schedulers = inject(ɵAngularFireSchedulers);\n      pendingTasks = inject(PendingTasks);\n      injector = inject(EnvironmentInjector);\n    } catch (e) {\n      warnOutsideInjectionContext(it, logLevel);\n      return it.apply(this, _arguments);\n    }\n    // if this is a callback function, e.g, onSnapshot, we should create a pending task and complete it\n    // only once one of the callback functions is tripped.\n    for (let i = 0; i < arguments.length; i++) {\n      if (typeof _arguments[i] === 'function') {\n        if (blockUntilFirst) {\n          taskDone ||= run(() => pendingTasks.add());\n        }\n        // TODO create a microtask to track callback functions\n        _arguments[i] = zoneWrapFn(_arguments[i], taskDone, injector);\n      }\n    }\n    const ret = runOutsideAngular(() => it.apply(this, _arguments));\n    if (!blockUntilFirst) {\n      if (ret instanceof Observable) {\n        return ret.pipe(subscribeOn(schedulers.outsideAngular), observeOn(schedulers.insideAngular));\n      } else {\n        return run(() => ret);\n      }\n    }\n    if (ret instanceof Observable) {\n      return ret.pipe(subscribeOn(schedulers.outsideAngular), observeOn(schedulers.insideAngular), pendingUntilEvent(injector));\n    } else if (ret instanceof Promise) {\n      // eslint-disable-next-line @typescript-eslint/no-misused-promises\n      return run(() => {\n        const removeTask = pendingTasks.add();\n        return new Promise((resolve, reject) => {\n          ret.then(it => runInInjectionContext(injector, () => run(() => resolve(it))), reason => runInInjectionContext(injector, () => run(() => reject(reason)))).finally(removeTask);\n        });\n      });\n    } else if (typeof ret === 'function' && taskDone) {\n      // Handle unsubscribe\n      // function() is needed for the arguments object\n      return function () {\n        setTimeout(taskDone, 0);\n        return ret.apply(this, arguments);\n      };\n    } else {\n      // TODO how do we handle storage uploads in Zone? and other stuff with cancel() etc?\n      return run(() => ret);\n    }\n  };\n};\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { LogLevel, VERSION, setLogLevel, ɵAngularFireSchedulers, ɵZoneScheduler, ɵgetAllInstancesOf, ɵgetDefaultInstanceOf, ɵisSupportedError, ɵzoneWrap };\n", "import { getApps as getApps$1, getApp as getApp$1, registerVersion as registerVersion$1, deleteApp as deleteApp$1, initializeApp as initializeApp$1, initializeServerApp as initializeServerApp$1, onLog as onLog$1, setLogLevel as setLogLevel$1 } from 'firebase/app';\nexport * from 'firebase/app';\nimport { timer, from } from 'rxjs';\nimport { concatMap, distinct } from 'rxjs/operators';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, Optional, PLATFORM_ID, VERSION as VERSION$1, Inject, NgModule, makeEnvironmentProviders, NgZone, Injector } from '@angular/core';\nimport { VERSION, ɵAngularFireSchedulers as _AngularFireSchedulers, ɵzoneWrap as _zoneWrap } from '@angular/fire';\nclass FirebaseApp {\n  constructor(app) {\n    return app;\n  }\n}\nclass FirebaseApps {\n  constructor() {\n    return getApps$1();\n  }\n}\nconst firebaseApp$ = timer(0, 300).pipe(concatMap(() => from(getApps$1())), distinct());\nfunction defaultFirebaseAppFactory(provided) {\n  // Use the provided app, if there is only one, otherwise fetch the default app\n  if (provided && provided.length === 1) {\n    return provided[0];\n  }\n  return new FirebaseApp(getApp$1());\n}\n// With FIREBASE_APPS I wanted to capture the default app instance, if it is initialized by\n// the reserved URL; ɵPROVIDED_FIREBASE_APPS is not for public consumption and serves to ensure that all\n// provideFirebaseApp(...) calls are satisfied before FirebaseApp$ or FirebaseApp is resolved\nconst PROVIDED_FIREBASE_APPS = new InjectionToken('angularfire2._apps');\n// Injecting FirebaseApp will now only inject the default Firebase App\n// this allows allows beginners to import /__/firebase/init.js to auto initialize Firebase App\n// from the reserved URL.\nconst DEFAULT_FIREBASE_APP_PROVIDER = {\n  provide: FirebaseApp,\n  useFactory: defaultFirebaseAppFactory,\n  deps: [[new Optional(), PROVIDED_FIREBASE_APPS]]\n};\nconst FIREBASE_APPS_PROVIDER = {\n  provide: FirebaseApps,\n  deps: [[new Optional(), PROVIDED_FIREBASE_APPS]]\n};\nfunction firebaseAppFactory(fn) {\n  return (zone, injector) => {\n    const platformId = injector.get(PLATFORM_ID);\n    registerVersion$1('angularfire', VERSION.full, 'core');\n    registerVersion$1('angularfire', VERSION.full, 'app');\n    // eslint-disable-next-line @typescript-eslint/no-base-to-string\n    registerVersion$1('angular', VERSION$1.full, platformId.toString());\n    const app = zone.runOutsideAngular(() => fn(injector));\n    return new FirebaseApp(app);\n  };\n}\nclass FirebaseAppModule {\n  // eslint-disable-next-line @typescript-eslint/ban-types\n  constructor(platformId) {\n    registerVersion$1('angularfire', VERSION.full, 'core');\n    registerVersion$1('angularfire', VERSION.full, 'app');\n    // eslint-disable-next-line @typescript-eslint/no-base-to-string\n    registerVersion$1('angular', VERSION$1.full, platformId.toString());\n  }\n  static ɵfac = function FirebaseAppModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || FirebaseAppModule)(i0.ɵɵinject(PLATFORM_ID));\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: FirebaseAppModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [DEFAULT_FIREBASE_APP_PROVIDER, FIREBASE_APPS_PROVIDER]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FirebaseAppModule, [{\n    type: NgModule,\n    args: [{\n      providers: [DEFAULT_FIREBASE_APP_PROVIDER, FIREBASE_APPS_PROVIDER]\n    }]\n  }], () => [{\n    type: Object,\n    decorators: [{\n      type: Inject,\n      args: [PLATFORM_ID]\n    }]\n  }], null);\n})();\n// Calling initializeApp({ ... }, 'name') multiple times will add more FirebaseApps into the FIREBASE_APPS\n// injection scope. This allows developers to more easily work with multiple Firebase Applications. Downside\n// is that DI for app name and options doesn't really make sense anymore.\nfunction provideFirebaseApp(fn, ...deps) {\n  return makeEnvironmentProviders([DEFAULT_FIREBASE_APP_PROVIDER, FIREBASE_APPS_PROVIDER, {\n    provide: PROVIDED_FIREBASE_APPS,\n    useFactory: firebaseAppFactory(fn),\n    multi: true,\n    deps: [NgZone, Injector, _AngularFireSchedulers, ...deps]\n  }]);\n}\n\n// DO NOT MODIFY, this file is autogenerated by tools/build.ts\nconst deleteApp = _zoneWrap(deleteApp$1, true);\nconst getApp = _zoneWrap(getApp$1, true);\nconst getApps = _zoneWrap(getApps$1, true);\nconst initializeApp = _zoneWrap(initializeApp$1, true);\nconst initializeServerApp = _zoneWrap(initializeServerApp$1, true);\nconst onLog = _zoneWrap(onLog$1, true);\nconst registerVersion = _zoneWrap(registerVersion$1, true);\nconst setLogLevel = _zoneWrap(setLogLevel$1, true);\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { FirebaseApp, FirebaseAppModule, FirebaseApps, deleteApp, firebaseApp$, getApp, getApps, initializeApp, initializeServerApp, onLog, provideFirebaseApp, registerVersion, setLogLevel };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAM,6BAA6B,MAAO;;;ACsF1C,IAAM,sBAAsB,SAAU,KAAK;AAEvC,QAAM,MAAM,CAAC;AACb,MAAI,IAAI;AACR,WAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACjC,QAAI,IAAI,IAAI,WAAW,CAAC;AACxB,QAAI,IAAI,KAAK;AACT,UAAI,GAAG,IAAI;AAAA,IACf,WACS,IAAI,MAAM;AACf,UAAI,GAAG,IAAK,KAAK,IAAK;AACtB,UAAI,GAAG,IAAK,IAAI,KAAM;AAAA,IAC1B,YACU,IAAI,WAAY,SACtB,IAAI,IAAI,IAAI,WACX,IAAI,WAAW,IAAI,CAAC,IAAI,WAAY,OAAQ;AAE7C,UAAI,UAAY,IAAI,SAAW,OAAO,IAAI,WAAW,EAAE,CAAC,IAAI;AAC5D,UAAI,GAAG,IAAK,KAAK,KAAM;AACvB,UAAI,GAAG,IAAM,KAAK,KAAM,KAAM;AAC9B,UAAI,GAAG,IAAM,KAAK,IAAK,KAAM;AAC7B,UAAI,GAAG,IAAK,IAAI,KAAM;AAAA,IAC1B,OACK;AACD,UAAI,GAAG,IAAK,KAAK,KAAM;AACvB,UAAI,GAAG,IAAM,KAAK,IAAK,KAAM;AAC7B,UAAI,GAAG,IAAK,IAAI,KAAM;AAAA,IAC1B;AAAA,EACJ;AACA,SAAO;AACX;AAOA,IAAM,oBAAoB,SAAU,OAAO;AAEvC,QAAM,MAAM,CAAC;AACb,MAAI,MAAM,GAAG,IAAI;AACjB,SAAO,MAAM,MAAM,QAAQ;AACvB,UAAM,KAAK,MAAM,KAAK;AACtB,QAAI,KAAK,KAAK;AACV,UAAI,GAAG,IAAI,OAAO,aAAa,EAAE;AAAA,IACrC,WACS,KAAK,OAAO,KAAK,KAAK;AAC3B,YAAM,KAAK,MAAM,KAAK;AACtB,UAAI,GAAG,IAAI,OAAO,cAAe,KAAK,OAAO,IAAM,KAAK,EAAG;AAAA,IAC/D,WACS,KAAK,OAAO,KAAK,KAAK;AAE3B,YAAM,KAAK,MAAM,KAAK;AACtB,YAAM,KAAK,MAAM,KAAK;AACtB,YAAM,KAAK,MAAM,KAAK;AACtB,YAAM,MAAO,KAAK,MAAM,MAAQ,KAAK,OAAO,MAAQ,KAAK,OAAO,IAAM,KAAK,MACvE;AACJ,UAAI,GAAG,IAAI,OAAO,aAAa,SAAU,KAAK,GAAG;AACjD,UAAI,GAAG,IAAI,OAAO,aAAa,SAAU,IAAI,KAAK;AAAA,IACtD,OACK;AACD,YAAM,KAAK,MAAM,KAAK;AACtB,YAAM,KAAK,MAAM,KAAK;AACtB,UAAI,GAAG,IAAI,OAAO,cAAe,KAAK,OAAO,MAAQ,KAAK,OAAO,IAAM,KAAK,EAAG;AAAA,IACnF;AAAA,EACJ;AACA,SAAO,IAAI,KAAK,EAAE;AACtB;AAKA,IAAM,SAAS;AAAA;AAAA;AAAA;AAAA,EAIX,gBAAgB;AAAA;AAAA;AAAA;AAAA,EAIhB,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKhB,uBAAuB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKvB,uBAAuB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKvB,mBAAmB;AAAA;AAAA;AAAA;AAAA,EAInB,IAAI,eAAe;AACf,WAAO,KAAK,oBAAoB;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,uBAAuB;AACvB,WAAO,KAAK,oBAAoB;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,oBAAoB,OAAO,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUpC,gBAAgB,OAAO,SAAS;AAC5B,QAAI,CAAC,MAAM,QAAQ,KAAK,GAAG;AACvB,YAAM,MAAM,+CAA+C;AAAA,IAC/D;AACA,SAAK,MAAM;AACX,UAAM,gBAAgB,UAChB,KAAK,wBACL,KAAK;AACX,UAAM,SAAS,CAAC;AAChB,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK,GAAG;AACtC,YAAM,QAAQ,MAAM,CAAC;AACrB,YAAM,YAAY,IAAI,IAAI,MAAM;AAChC,YAAM,QAAQ,YAAY,MAAM,IAAI,CAAC,IAAI;AACzC,YAAM,YAAY,IAAI,IAAI,MAAM;AAChC,YAAM,QAAQ,YAAY,MAAM,IAAI,CAAC,IAAI;AACzC,YAAM,WAAW,SAAS;AAC1B,YAAM,YAAa,QAAQ,MAAS,IAAM,SAAS;AACnD,UAAI,YAAa,QAAQ,OAAS,IAAM,SAAS;AACjD,UAAI,WAAW,QAAQ;AACvB,UAAI,CAAC,WAAW;AACZ,mBAAW;AACX,YAAI,CAAC,WAAW;AACZ,qBAAW;AAAA,QACf;AAAA,MACJ;AACA,aAAO,KAAK,cAAc,QAAQ,GAAG,cAAc,QAAQ,GAAG,cAAc,QAAQ,GAAG,cAAc,QAAQ,CAAC;AAAA,IAClH;AACA,WAAO,OAAO,KAAK,EAAE;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,aAAa,OAAO,SAAS;AAGzB,QAAI,KAAK,sBAAsB,CAAC,SAAS;AACrC,aAAO,KAAK,KAAK;AAAA,IACrB;AACA,WAAO,KAAK,gBAAgB,oBAAoB,KAAK,GAAG,OAAO;AAAA,EACnE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,aAAa,OAAO,SAAS;AAGzB,QAAI,KAAK,sBAAsB,CAAC,SAAS;AACrC,aAAO,KAAK,KAAK;AAAA,IACrB;AACA,WAAO,kBAAkB,KAAK,wBAAwB,OAAO,OAAO,CAAC;AAAA,EACzE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBA,wBAAwB,OAAO,SAAS;AACpC,SAAK,MAAM;AACX,UAAM,gBAAgB,UAChB,KAAK,wBACL,KAAK;AACX,UAAM,SAAS,CAAC;AAChB,aAAS,IAAI,GAAG,IAAI,MAAM,UAAS;AAC/B,YAAM,QAAQ,cAAc,MAAM,OAAO,GAAG,CAAC;AAC7C,YAAM,YAAY,IAAI,MAAM;AAC5B,YAAM,QAAQ,YAAY,cAAc,MAAM,OAAO,CAAC,CAAC,IAAI;AAC3D,QAAE;AACF,YAAM,YAAY,IAAI,MAAM;AAC5B,YAAM,QAAQ,YAAY,cAAc,MAAM,OAAO,CAAC,CAAC,IAAI;AAC3D,QAAE;AACF,YAAM,YAAY,IAAI,MAAM;AAC5B,YAAM,QAAQ,YAAY,cAAc,MAAM,OAAO,CAAC,CAAC,IAAI;AAC3D,QAAE;AACF,UAAI,SAAS,QAAQ,SAAS,QAAQ,SAAS,QAAQ,SAAS,MAAM;AAClE,cAAM,IAAI,wBAAwB;AAAA,MACtC;AACA,YAAM,WAAY,SAAS,IAAM,SAAS;AAC1C,aAAO,KAAK,QAAQ;AACpB,UAAI,UAAU,IAAI;AACd,cAAM,WAAa,SAAS,IAAK,MAAS,SAAS;AACnD,eAAO,KAAK,QAAQ;AACpB,YAAI,UAAU,IAAI;AACd,gBAAM,WAAa,SAAS,IAAK,MAAQ;AACzC,iBAAO,KAAK,QAAQ;AAAA,QACxB;AAAA,MACJ;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QAAQ;AACJ,QAAI,CAAC,KAAK,gBAAgB;AACtB,WAAK,iBAAiB,CAAC;AACvB,WAAK,iBAAiB,CAAC;AACvB,WAAK,wBAAwB,CAAC;AAC9B,WAAK,wBAAwB,CAAC;AAE9B,eAAS,IAAI,GAAG,IAAI,KAAK,aAAa,QAAQ,KAAK;AAC/C,aAAK,eAAe,CAAC,IAAI,KAAK,aAAa,OAAO,CAAC;AACnD,aAAK,eAAe,KAAK,eAAe,CAAC,CAAC,IAAI;AAC9C,aAAK,sBAAsB,CAAC,IAAI,KAAK,qBAAqB,OAAO,CAAC;AAClE,aAAK,sBAAsB,KAAK,sBAAsB,CAAC,CAAC,IAAI;AAE5D,YAAI,KAAK,KAAK,kBAAkB,QAAQ;AACpC,eAAK,eAAe,KAAK,qBAAqB,OAAO,CAAC,CAAC,IAAI;AAC3D,eAAK,sBAAsB,KAAK,aAAa,OAAO,CAAC,CAAC,IAAI;AAAA,QAC9D;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACJ;AAIA,IAAM,0BAAN,cAAsC,MAAM;AAAA,EACxC,cAAc;AACV,UAAM,GAAG,SAAS;AAClB,SAAK,OAAO;AAAA,EAChB;AACJ;AAIA,IAAM,eAAe,SAAU,KAAK;AAChC,QAAM,YAAY,oBAAoB,GAAG;AACzC,SAAO,OAAO,gBAAgB,WAAW,IAAI;AACjD;AAKA,IAAM,gCAAgC,SAAU,KAAK;AAEjD,SAAO,aAAa,GAAG,EAAE,QAAQ,OAAO,EAAE;AAC9C;AAUA,IAAM,eAAe,SAAU,KAAK;AAChC,MAAI;AACA,WAAO,OAAO,aAAa,KAAK,IAAI;AAAA,EACxC,SACO,GAAG;AACN,YAAQ,MAAM,yBAAyB,CAAC;AAAA,EAC5C;AACA,SAAO;AACX;AA+FA,SAAS,YAAY;AACjB,MAAI,OAAO,SAAS,aAAa;AAC7B,WAAO;AAAA,EACX;AACA,MAAI,OAAO,WAAW,aAAa;AAC/B,WAAO;AAAA,EACX;AACA,MAAI,OAAO,WAAW,aAAa;AAC/B,WAAO;AAAA,EACX;AACA,QAAM,IAAI,MAAM,iCAAiC;AACrD;AAkBA,IAAM,wBAAwB,MAAM,UAAU,EAAE;AAShD,IAAM,6BAA6B,MAAM;AACrC,MAAI,OAAO,YAAY,eAAe,OAAO,QAAQ,QAAQ,aAAa;AACtE;AAAA,EACJ;AACA,QAAM,qBAAqB,QAAQ,IAAI;AACvC,MAAI,oBAAoB;AACpB,WAAO,KAAK,MAAM,kBAAkB;AAAA,EACxC;AACJ;AACA,IAAM,wBAAwB,MAAM;AAChC,MAAI,OAAO,aAAa,aAAa;AACjC;AAAA,EACJ;AACA,MAAI;AACJ,MAAI;AACA,YAAQ,SAAS,OAAO,MAAM,+BAA+B;AAAA,EACjE,SACO,GAAG;AAGN;AAAA,EACJ;AACA,QAAM,UAAU,SAAS,aAAa,MAAM,CAAC,CAAC;AAC9C,SAAO,WAAW,KAAK,MAAM,OAAO;AACxC;AAQA,IAAM,cAAc,MAAM;AACtB,MAAI;AACA,WAAQ,2BAA2B,KAC/B,sBAAsB,KACtB,2BAA2B,KAC3B,sBAAsB;AAAA,EAC9B,SACO,GAAG;AAON,YAAQ,KAAK,+CAA+C,CAAC,EAAE;AAC/D;AAAA,EACJ;AACJ;AAOA,IAAM,yBAAyB,CAAC,gBAAgB;AAAE,MAAI,IAAI;AAAI,UAAQ,MAAM,KAAK,YAAY,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,mBAAmB,QAAQ,OAAO,SAAS,SAAS,GAAG,WAAW;AAAG;AA8B7M,IAAM,sBAAsB,MAAM;AAAE,MAAI;AAAI,UAAQ,KAAK,YAAY,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG;AAAQ;AAMxH,IAAM,yBAAyB,CAACA,UAAS;AAAE,MAAI;AAAI,UAAQ,KAAK,YAAY,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,IAAIA,KAAI,EAAE;AAAG;AAkBpI,IAAM,WAAN,MAAe;AAAA,EACX,cAAc;AACV,SAAK,SAAS,MAAM;AAAA,IAAE;AACtB,SAAK,UAAU,MAAM;AAAA,IAAE;AACvB,SAAK,UAAU,IAAI,QAAQ,CAAC,SAAS,WAAW;AAC5C,WAAK,UAAU;AACf,WAAK,SAAS;AAAA,IAClB,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,aAAa,UAAU;AACnB,WAAO,CAAC,OAAO,UAAU;AACrB,UAAI,OAAO;AACP,aAAK,OAAO,KAAK;AAAA,MACrB,OACK;AACD,aAAK,QAAQ,KAAK;AAAA,MACtB;AACA,UAAI,OAAO,aAAa,YAAY;AAGhC,aAAK,QAAQ,MAAM,MAAM;AAAA,QAAE,CAAC;AAG5B,YAAI,SAAS,WAAW,GAAG;AACvB,mBAAS,KAAK;AAAA,QAClB,OACK;AACD,mBAAS,OAAO,KAAK;AAAA,QACzB;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACJ;AAsBA,SAAS,mBAAmB,KAAK;AAK7B,MAAI;AACA,UAAM,OAAO,IAAI,WAAW,SAAS,KAAK,IAAI,WAAW,UAAU,IAC7D,IAAI,IAAI,GAAG,EAAE,WACb;AACN,WAAO,KAAK,SAAS,wBAAwB;AAAA,EACjD,SACO,IAAI;AACP,WAAO;AAAA,EACX;AACJ;AAMA,eAAe,WAAW,UAAU;AAChC,QAAM,SAAS,MAAM,MAAM,UAAU;AAAA,IACjC,aAAa;AAAA,EACjB,CAAC;AACD,SAAO,OAAO;AAClB;AA+CA,IAAM,iBAAiB,CAAC;AAExB,SAAS,qBAAqB;AAC1B,QAAM,UAAU;AAAA,IACZ,MAAM,CAAC;AAAA,IACP,UAAU,CAAC;AAAA,EACf;AACA,aAAW,OAAO,OAAO,KAAK,cAAc,GAAG;AAC3C,QAAI,eAAe,GAAG,GAAG;AACrB,cAAQ,SAAS,KAAK,GAAG;AAAA,IAC7B,OACK;AACD,cAAQ,KAAK,KAAK,GAAG;AAAA,IACzB;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,cAAc,IAAI;AACvB,MAAI,YAAY,SAAS,eAAe,EAAE;AAC1C,MAAI,UAAU;AACd,MAAI,CAAC,WAAW;AACZ,gBAAY,SAAS,cAAc,KAAK;AACxC,cAAU,aAAa,MAAM,EAAE;AAC/B,cAAU;AAAA,EACd;AACA,SAAO,EAAE,SAAS,SAAS,UAAU;AACzC;AACA,IAAI,sBAAsB;AAO1B,SAAS,qBAAqBC,OAAM,mBAAmB;AACnD,MAAI,OAAO,WAAW,eAClB,OAAO,aAAa,eACpB,CAAC,mBAAmB,OAAO,SAAS,IAAI,KACxC,eAAeA,KAAI,MAAM,qBACzB,eAAeA,KAAI;AAAA,EACnB,qBAAqB;AACrB;AAAA,EACJ;AACA,iBAAeA,KAAI,IAAI;AACvB,WAAS,WAAW,IAAI;AACpB,WAAO,uBAAuB,EAAE;AAAA,EACpC;AACA,QAAM,WAAW;AACjB,QAAM,UAAU,mBAAmB;AACnC,QAAM,YAAY,QAAQ,KAAK,SAAS;AACxC,WAAS,WAAW;AAChB,UAAM,UAAU,SAAS,eAAe,QAAQ;AAChD,QAAI,SAAS;AACT,cAAQ,OAAO;AAAA,IACnB;AAAA,EACJ;AACA,WAAS,kBAAkB,UAAU;AACjC,aAAS,MAAM,UAAU;AACzB,aAAS,MAAM,aAAa;AAC5B,aAAS,MAAM,WAAW;AAC1B,aAAS,MAAM,SAAS;AACxB,aAAS,MAAM,OAAO;AACtB,aAAS,MAAM,UAAU;AACzB,aAAS,MAAM,eAAe;AAC9B,aAAS,MAAM,aAAa;AAAA,EAChC;AACA,WAAS,gBAAgB,aAAa,QAAQ;AAC1C,gBAAY,aAAa,SAAS,IAAI;AACtC,gBAAY,aAAa,MAAM,MAAM;AACrC,gBAAY,aAAa,UAAU,IAAI;AACvC,gBAAY,aAAa,WAAW,WAAW;AAC/C,gBAAY,aAAa,QAAQ,MAAM;AACvC,gBAAY,MAAM,aAAa;AAAA,EACnC;AACA,WAAS,gBAAgB;AACrB,UAAM,WAAW,SAAS,cAAc,MAAM;AAC9C,aAAS,MAAM,SAAS;AACxB,aAAS,MAAM,aAAa;AAC5B,aAAS,MAAM,WAAW;AAC1B,aAAS,YAAY;AACrB,aAAS,UAAU,MAAM;AACrB,4BAAsB;AACtB,eAAS;AAAA,IACb;AACA,WAAO;AAAA,EACX;AACA,WAAS,gBAAgB,eAAe,aAAa;AACjD,kBAAc,aAAa,MAAM,WAAW;AAC5C,kBAAc,YAAY;AAC1B,kBAAc,OACV;AACJ,kBAAc,aAAa,UAAU,SAAS;AAC9C,kBAAc,MAAM,cAAc;AAClC,kBAAc,MAAM,iBAAiB;AAAA,EACzC;AACA,WAAS,WAAW;AAChB,UAAM,SAAS,cAAc,QAAQ;AACrC,UAAM,iBAAiB,WAAW,MAAM;AACxC,UAAM,eAAe,SAAS,eAAe,cAAc,KAAK,SAAS,cAAc,MAAM;AAC7F,UAAM,cAAc,WAAW,WAAW;AAC1C,UAAM,gBAAgB,SAAS,eAAe,WAAW,KACrD,SAAS,cAAc,GAAG;AAC9B,UAAM,gBAAgB,WAAW,cAAc;AAC/C,UAAM,cAAc,SAAS,eAAe,aAAa,KACrD,SAAS,gBAAgB,8BAA8B,KAAK;AAChE,QAAI,OAAO,SAAS;AAEhB,YAAM,WAAW,OAAO;AACxB,wBAAkB,QAAQ;AAC1B,sBAAgB,eAAe,WAAW;AAC1C,YAAM,WAAW,cAAc;AAC/B,sBAAgB,aAAa,aAAa;AAC1C,eAAS,OAAO,aAAa,cAAc,eAAe,QAAQ;AAClE,eAAS,KAAK,YAAY,QAAQ;AAAA,IACtC;AACA,QAAI,WAAW;AACX,mBAAa,YAAY;AACzB,kBAAY,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAQ5B,OACK;AACD,kBAAY,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQxB,mBAAa,YAAY;AAAA,IAC7B;AACA,iBAAa,aAAa,MAAM,cAAc;AAAA,EAClD;AACA,MAAI,SAAS,eAAe,WAAW;AACnC,WAAO,iBAAiB,oBAAoB,QAAQ;AAAA,EACxD,OACK;AACD,aAAS;AAAA,EACb;AACJ;AAsBA,SAAS,QAAQ;AACb,MAAI,OAAO,cAAc,eACrB,OAAO,UAAU,WAAW,MAAM,UAAU;AAC5C,WAAO,UAAU,WAAW;AAAA,EAChC,OACK;AACD,WAAO;AAAA,EACX;AACJ;AAQA,SAAS,kBAAkB;AACvB,SAAQ,OAAO,WAAW;AAAA;AAAA,EAGtB,CAAC,EAAE,OAAO,SAAS,KAAK,OAAO,UAAU,KAAK,OAAO,UAAU,MAC/D,oDAAoD,KAAK,MAAM,CAAC;AACxE;AA6BA,SAAS,YAAY;AACjB,SAAO,OAAO,WAAW,eAAe,YAAY;AACxD;AAIA,SAAS,cAAc;AACnB,SAAQ,OAAO,sBAAsB,eACjC,OAAO,SAAS,eAChB,gBAAgB;AACxB;AAIA,SAAS,qBAAqB;AAC1B,SAAQ,OAAO,cAAc,eACzB,UAAU,cAAc;AAChC;AACA,SAAS,qBAAqB;AAC1B,QAAM,UAAU,OAAO,WAAW,WAC5B,OAAO,UACP,OAAO,YAAY,WACf,QAAQ,UACR;AACV,SAAO,OAAO,YAAY,YAAY,QAAQ,OAAO;AACzD;AAMA,SAAS,gBAAgB;AACrB,SAAQ,OAAO,cAAc,YAAY,UAAU,SAAS,MAAM;AACtE;AAMA,SAAS,OAAO;AACZ,QAAM,KAAK,MAAM;AACjB,SAAO,GAAG,QAAQ,OAAO,KAAK,KAAK,GAAG,QAAQ,UAAU,KAAK;AACjE;AAgCA,SAAS,uBAAuB;AAC5B,MAAI;AACA,WAAO,OAAO,cAAc;AAAA,EAChC,SACO,GAAG;AACN,WAAO;AAAA,EACX;AACJ;AAQA,SAAS,4BAA4B;AACjC,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACpC,QAAI;AACA,UAAI,WAAW;AACf,YAAM,gBAAgB;AACtB,YAAM,UAAU,KAAK,UAAU,KAAK,aAAa;AACjD,cAAQ,YAAY,MAAM;AACtB,gBAAQ,OAAO,MAAM;AAErB,YAAI,CAAC,UAAU;AACX,eAAK,UAAU,eAAe,aAAa;AAAA,QAC/C;AACA,gBAAQ,IAAI;AAAA,MAChB;AACA,cAAQ,kBAAkB,MAAM;AAC5B,mBAAW;AAAA,MACf;AACA,cAAQ,UAAU,MAAM;AACpB,YAAI;AACJ,iBAAS,KAAK,QAAQ,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG,YAAY,EAAE;AAAA,MACvF;AAAA,IACJ,SACO,OAAO;AACV,aAAO,KAAK;AAAA,IAChB;AAAA,EACJ,CAAC;AACL;AAqEA,IAAM,aAAa;AAGnB,IAAM,gBAAN,MAAM,uBAAsB,MAAM;AAAA,EAC9B,YAEA,MAAM,SAEN,YAAY;AACR,UAAM,OAAO;AACb,SAAK,OAAO;AACZ,SAAK,aAAa;AAElB,SAAK,OAAO;AAKZ,WAAO,eAAe,MAAM,eAAc,SAAS;AAGnD,QAAI,MAAM,mBAAmB;AACzB,YAAM,kBAAkB,MAAM,aAAa,UAAU,MAAM;AAAA,IAC/D;AAAA,EACJ;AACJ;AACA,IAAM,eAAN,MAAmB;AAAA,EACf,YAAY,SAAS,aAAa,QAAQ;AACtC,SAAK,UAAU;AACf,SAAK,cAAc;AACnB,SAAK,SAAS;AAAA,EAClB;AAAA,EACA,OAAO,SAAS,MAAM;AAClB,UAAM,aAAa,KAAK,CAAC,KAAK,CAAC;AAC/B,UAAM,WAAW,GAAG,KAAK,OAAO,IAAI,IAAI;AACxC,UAAM,WAAW,KAAK,OAAO,IAAI;AACjC,UAAM,UAAU,WAAW,gBAAgB,UAAU,UAAU,IAAI;AAEnE,UAAM,cAAc,GAAG,KAAK,WAAW,KAAK,OAAO,KAAK,QAAQ;AAChE,UAAM,QAAQ,IAAI,cAAc,UAAU,aAAa,UAAU;AACjE,WAAO;AAAA,EACX;AACJ;AACA,SAAS,gBAAgB,UAAU,MAAM;AACrC,SAAO,SAAS,QAAQ,SAAS,CAAC,GAAG,QAAQ;AACzC,UAAM,QAAQ,KAAK,GAAG;AACtB,WAAO,SAAS,OAAO,OAAO,KAAK,IAAI,IAAI,GAAG;AAAA,EAClD,CAAC;AACL;AACA,IAAM,UAAU;AA8KhB,SAAS,QAAQ,KAAK;AAClB,aAAW,OAAO,KAAK;AACnB,QAAI,OAAO,UAAU,eAAe,KAAK,KAAK,GAAG,GAAG;AAChD,aAAO;AAAA,IACX;AAAA,EACJ;AACA,SAAO;AACX;AAaA,SAAS,UAAU,GAAG,GAAG;AACrB,MAAI,MAAM,GAAG;AACT,WAAO;AAAA,EACX;AACA,QAAM,QAAQ,OAAO,KAAK,CAAC;AAC3B,QAAM,QAAQ,OAAO,KAAK,CAAC;AAC3B,aAAW,KAAK,OAAO;AACnB,QAAI,CAAC,MAAM,SAAS,CAAC,GAAG;AACpB,aAAO;AAAA,IACX;AACA,UAAM,QAAQ,EAAE,CAAC;AACjB,UAAM,QAAQ,EAAE,CAAC;AACjB,QAAI,SAAS,KAAK,KAAK,SAAS,KAAK,GAAG;AACpC,UAAI,CAAC,UAAU,OAAO,KAAK,GAAG;AAC1B,eAAO;AAAA,MACX;AAAA,IACJ,WACS,UAAU,OAAO;AACtB,aAAO;AAAA,IACX;AAAA,EACJ;AACA,aAAW,KAAK,OAAO;AACnB,QAAI,CAAC,MAAM,SAAS,CAAC,GAAG;AACpB,aAAO;AAAA,IACX;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,SAAS,OAAO;AACrB,SAAO,UAAU,QAAQ,OAAO,UAAU;AAC9C;AAkDA,SAAS,YAAY,mBAAmB;AACpC,QAAM,SAAS,CAAC;AAChB,aAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,iBAAiB,GAAG;AAC1D,QAAI,MAAM,QAAQ,KAAK,GAAG;AACtB,YAAM,QAAQ,cAAY;AACtB,eAAO,KAAK,mBAAmB,GAAG,IAAI,MAAM,mBAAmB,QAAQ,CAAC;AAAA,MAC5E,CAAC;AAAA,IACL,OACK;AACD,aAAO,KAAK,mBAAmB,GAAG,IAAI,MAAM,mBAAmB,KAAK,CAAC;AAAA,IACzE;AAAA,EACJ;AACA,SAAO,OAAO,SAAS,MAAM,OAAO,KAAK,GAAG,IAAI;AACpD;AAKA,SAAS,kBAAkBC,cAAa;AACpC,QAAM,MAAM,CAAC;AACb,QAAM,SAASA,aAAY,QAAQ,OAAO,EAAE,EAAE,MAAM,GAAG;AACvD,SAAO,QAAQ,WAAS;AACpB,QAAI,OAAO;AACP,YAAM,CAAC,KAAK,KAAK,IAAI,MAAM,MAAM,GAAG;AACpC,UAAI,mBAAmB,GAAG,CAAC,IAAI,mBAAmB,KAAK;AAAA,IAC3D;AAAA,EACJ,CAAC;AACD,SAAO;AACX;AAIA,SAAS,mBAAmB,KAAK;AAC7B,QAAM,aAAa,IAAI,QAAQ,GAAG;AAClC,MAAI,CAAC,YAAY;AACb,WAAO;AAAA,EACX;AACA,QAAM,gBAAgB,IAAI,QAAQ,KAAK,UAAU;AACjD,SAAO,IAAI,UAAU,YAAY,gBAAgB,IAAI,gBAAgB,MAAS;AAClF;AA0QA,SAAS,gBAAgB,UAAU,eAAe;AAC9C,QAAM,QAAQ,IAAI,cAAc,UAAU,aAAa;AACvD,SAAO,MAAM,UAAU,KAAK,KAAK;AACrC;AAKA,IAAM,gBAAN,MAAoB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMhB,YAAY,UAAU,eAAe;AACjC,SAAK,YAAY,CAAC;AAClB,SAAK,eAAe,CAAC;AACrB,SAAK,gBAAgB;AAErB,SAAK,OAAO,QAAQ,QAAQ;AAC5B,SAAK,YAAY;AACjB,SAAK,gBAAgB;AAIrB,SAAK,KACA,KAAK,MAAM;AACZ,eAAS,IAAI;AAAA,IACjB,CAAC,EACI,MAAM,OAAK;AACZ,WAAK,MAAM,CAAC;AAAA,IAChB,CAAC;AAAA,EACL;AAAA,EACA,KAAK,OAAO;AACR,SAAK,gBAAgB,CAAC,aAAa;AAC/B,eAAS,KAAK,KAAK;AAAA,IACvB,CAAC;AAAA,EACL;AAAA,EACA,MAAM,OAAO;AACT,SAAK,gBAAgB,CAAC,aAAa;AAC/B,eAAS,MAAM,KAAK;AAAA,IACxB,CAAC;AACD,SAAK,MAAM,KAAK;AAAA,EACpB;AAAA,EACA,WAAW;AACP,SAAK,gBAAgB,CAAC,aAAa;AAC/B,eAAS,SAAS;AAAA,IACtB,CAAC;AACD,SAAK,MAAM;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UAAU,gBAAgB,OAAO,UAAU;AACvC,QAAI;AACJ,QAAI,mBAAmB,UACnB,UAAU,UACV,aAAa,QAAW;AACxB,YAAM,IAAI,MAAM,mBAAmB;AAAA,IACvC;AAEA,QAAI,qBAAqB,gBAAgB;AAAA,MACrC;AAAA,MACA;AAAA,MACA;AAAA,IACJ,CAAC,GAAG;AACA,iBAAW;AAAA,IACf,OACK;AACD,iBAAW;AAAA,QACP,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,SAAS,SAAS,QAAW;AAC7B,eAAS,OAAO;AAAA,IACpB;AACA,QAAI,SAAS,UAAU,QAAW;AAC9B,eAAS,QAAQ;AAAA,IACrB;AACA,QAAI,SAAS,aAAa,QAAW;AACjC,eAAS,WAAW;AAAA,IACxB;AACA,UAAM,QAAQ,KAAK,eAAe,KAAK,MAAM,KAAK,UAAU,MAAM;AAIlE,QAAI,KAAK,WAAW;AAEhB,WAAK,KAAK,KAAK,MAAM;AACjB,YAAI;AACA,cAAI,KAAK,YAAY;AACjB,qBAAS,MAAM,KAAK,UAAU;AAAA,UAClC,OACK;AACD,qBAAS,SAAS;AAAA,UACtB;AAAA,QACJ,SACO,GAAG;AAAA,QAEV;AACA;AAAA,MACJ,CAAC;AAAA,IACL;AACA,SAAK,UAAU,KAAK,QAAQ;AAC5B,WAAO;AAAA,EACX;AAAA;AAAA;AAAA,EAGA,eAAe,GAAG;AACd,QAAI,KAAK,cAAc,UAAa,KAAK,UAAU,CAAC,MAAM,QAAW;AACjE;AAAA,IACJ;AACA,WAAO,KAAK,UAAU,CAAC;AACvB,SAAK,iBAAiB;AACtB,QAAI,KAAK,kBAAkB,KAAK,KAAK,kBAAkB,QAAW;AAC9D,WAAK,cAAc,IAAI;AAAA,IAC3B;AAAA,EACJ;AAAA,EACA,gBAAgB,IAAI;AAChB,QAAI,KAAK,WAAW;AAEhB;AAAA,IACJ;AAGA,aAAS,IAAI,GAAG,IAAI,KAAK,UAAU,QAAQ,KAAK;AAC5C,WAAK,QAAQ,GAAG,EAAE;AAAA,IACtB;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA,EAIA,QAAQ,GAAG,IAAI;AAGX,SAAK,KAAK,KAAK,MAAM;AACjB,UAAI,KAAK,cAAc,UAAa,KAAK,UAAU,CAAC,MAAM,QAAW;AACjE,YAAI;AACA,aAAG,KAAK,UAAU,CAAC,CAAC;AAAA,QACxB,SACO,GAAG;AAIN,cAAI,OAAO,YAAY,eAAe,QAAQ,OAAO;AACjD,oBAAQ,MAAM,CAAC;AAAA,UACnB;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EACA,MAAM,KAAK;AACP,QAAI,KAAK,WAAW;AAChB;AAAA,IACJ;AACA,SAAK,YAAY;AACjB,QAAI,QAAQ,QAAW;AACnB,WAAK,aAAa;AAAA,IACtB;AAGA,SAAK,KAAK,KAAK,MAAM;AACjB,WAAK,YAAY;AACjB,WAAK,gBAAgB;AAAA,IACzB,CAAC;AAAA,EACL;AACJ;AAmBA,SAAS,qBAAqB,KAAK,SAAS;AACxC,MAAI,OAAO,QAAQ,YAAY,QAAQ,MAAM;AACzC,WAAO;AAAA,EACX;AACA,aAAW,UAAU,SAAS;AAC1B,QAAI,UAAU,OAAO,OAAO,IAAI,MAAM,MAAM,YAAY;AACpD,aAAO;AAAA,IACX;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,OAAO;AAEhB;AAkNA,IAAM,mBAAmB,IAAI,KAAK,KAAK;AA+FvC,SAAS,mBAAmB,SAAS;AACjC,MAAI,WAAW,QAAQ,WAAW;AAC9B,WAAO,QAAQ;AAAA,EACnB,OACK;AACD,WAAO;AAAA,EACX;AACJ;;;AC7vEA,IAAM,YAAN,MAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOZ,YAAYC,OAAM,iBAAiB,MAAM;AACrC,SAAK,OAAOA;AACZ,SAAK,kBAAkB;AACvB,SAAK,OAAO;AACZ,SAAK,oBAAoB;AAIzB,SAAK,eAAe,CAAC;AACrB,SAAK,oBAAoB;AACzB,SAAK,oBAAoB;AAAA,EAC7B;AAAA,EACA,qBAAqB,MAAM;AACvB,SAAK,oBAAoB;AACzB,WAAO;AAAA,EACX;AAAA,EACA,qBAAqB,mBAAmB;AACpC,SAAK,oBAAoB;AACzB,WAAO;AAAA,EACX;AAAA,EACA,gBAAgB,OAAO;AACnB,SAAK,eAAe;AACpB,WAAO;AAAA,EACX;AAAA,EACA,2BAA2B,UAAU;AACjC,SAAK,oBAAoB;AACzB,WAAO;AAAA,EACX;AACJ;AAkBA,IAAM,qBAAqB;AAsB3B,IAAM,WAAN,MAAe;AAAA,EACX,YAAYA,OAAM,WAAW;AACzB,SAAK,OAAOA;AACZ,SAAK,YAAY;AACjB,SAAK,YAAY;AACjB,SAAK,YAAY,oBAAI,IAAI;AACzB,SAAK,oBAAoB,oBAAI,IAAI;AACjC,SAAK,mBAAmB,oBAAI,IAAI;AAChC,SAAK,kBAAkB,oBAAI,IAAI;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,YAAY;AAEZ,UAAM,uBAAuB,KAAK,4BAA4B,UAAU;AACxE,QAAI,CAAC,KAAK,kBAAkB,IAAI,oBAAoB,GAAG;AACnD,YAAM,WAAW,IAAI,SAAS;AAC9B,WAAK,kBAAkB,IAAI,sBAAsB,QAAQ;AACzD,UAAI,KAAK,cAAc,oBAAoB,KACvC,KAAK,qBAAqB,GAAG;AAE7B,YAAI;AACA,gBAAM,WAAW,KAAK,uBAAuB;AAAA,YACzC,oBAAoB;AAAA,UACxB,CAAC;AACD,cAAI,UAAU;AACV,qBAAS,QAAQ,QAAQ;AAAA,UAC7B;AAAA,QACJ,SACO,GAAG;AAAA,QAGV;AAAA,MACJ;AAAA,IACJ;AACA,WAAO,KAAK,kBAAkB,IAAI,oBAAoB,EAAE;AAAA,EAC5D;AAAA,EACA,aAAa,SAAS;AAClB,QAAI;AAEJ,UAAM,uBAAuB,KAAK,4BAA4B,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,UAAU;AAClI,UAAM,YAAY,KAAK,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,cAAc,QAAQ,OAAO,SAAS,KAAK;AAC5H,QAAI,KAAK,cAAc,oBAAoB,KACvC,KAAK,qBAAqB,GAAG;AAC7B,UAAI;AACA,eAAO,KAAK,uBAAuB;AAAA,UAC/B,oBAAoB;AAAA,QACxB,CAAC;AAAA,MACL,SACO,GAAG;AACN,YAAI,UAAU;AACV,iBAAO;AAAA,QACX,OACK;AACD,gBAAM;AAAA,QACV;AAAA,MACJ;AAAA,IACJ,OACK;AAED,UAAI,UAAU;AACV,eAAO;AAAA,MACX,OACK;AACD,cAAM,MAAM,WAAW,KAAK,IAAI,mBAAmB;AAAA,MACvD;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,eAAe;AACX,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,aAAa,WAAW;AACpB,QAAI,UAAU,SAAS,KAAK,MAAM;AAC9B,YAAM,MAAM,yBAAyB,UAAU,IAAI,iBAAiB,KAAK,IAAI,GAAG;AAAA,IACpF;AACA,QAAI,KAAK,WAAW;AAChB,YAAM,MAAM,iBAAiB,KAAK,IAAI,4BAA4B;AAAA,IACtE;AACA,SAAK,YAAY;AAEjB,QAAI,CAAC,KAAK,qBAAqB,GAAG;AAC9B;AAAA,IACJ;AAEA,QAAI,iBAAiB,SAAS,GAAG;AAC7B,UAAI;AACA,aAAK,uBAAuB,EAAE,oBAAoB,mBAAmB,CAAC;AAAA,MAC1E,SACO,GAAG;AAAA,MAKV;AAAA,IACJ;AAIA,eAAW,CAAC,oBAAoB,gBAAgB,KAAK,KAAK,kBAAkB,QAAQ,GAAG;AACnF,YAAM,uBAAuB,KAAK,4BAA4B,kBAAkB;AAChF,UAAI;AAEA,cAAM,WAAW,KAAK,uBAAuB;AAAA,UACzC,oBAAoB;AAAA,QACxB,CAAC;AACD,yBAAiB,QAAQ,QAAQ;AAAA,MACrC,SACO,GAAG;AAAA,MAGV;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,cAAc,aAAa,oBAAoB;AAC3C,SAAK,kBAAkB,OAAO,UAAU;AACxC,SAAK,iBAAiB,OAAO,UAAU;AACvC,SAAK,UAAU,OAAO,UAAU;AAAA,EACpC;AAAA;AAAA;AAAA,EAGA,MAAM,SAAS;AACX,UAAM,WAAW,MAAM,KAAK,KAAK,UAAU,OAAO,CAAC;AACnD,UAAM,QAAQ,IAAI;AAAA,MACd,GAAG,SACE,OAAO,aAAW,cAAc,OAAO,EAEvC,IAAI,aAAW,QAAQ,SAAS,OAAO,CAAC;AAAA,MAC7C,GAAG,SACE,OAAO,aAAW,aAAa,OAAO,EAEtC,IAAI,aAAW,QAAQ,QAAQ,CAAC;AAAA,IACzC,CAAC;AAAA,EACL;AAAA,EACA,iBAAiB;AACb,WAAO,KAAK,aAAa;AAAA,EAC7B;AAAA,EACA,cAAc,aAAa,oBAAoB;AAC3C,WAAO,KAAK,UAAU,IAAI,UAAU;AAAA,EACxC;AAAA,EACA,WAAW,aAAa,oBAAoB;AACxC,WAAO,KAAK,iBAAiB,IAAI,UAAU,KAAK,CAAC;AAAA,EACrD;AAAA,EACA,WAAW,OAAO,CAAC,GAAG;AAClB,UAAM,EAAE,UAAU,CAAC,EAAE,IAAI;AACzB,UAAM,uBAAuB,KAAK,4BAA4B,KAAK,kBAAkB;AACrF,QAAI,KAAK,cAAc,oBAAoB,GAAG;AAC1C,YAAM,MAAM,GAAG,KAAK,IAAI,IAAI,oBAAoB,gCAAgC;AAAA,IACpF;AACA,QAAI,CAAC,KAAK,eAAe,GAAG;AACxB,YAAM,MAAM,aAAa,KAAK,IAAI,8BAA8B;AAAA,IACpE;AACA,UAAM,WAAW,KAAK,uBAAuB;AAAA,MACzC,oBAAoB;AAAA,MACpB;AAAA,IACJ,CAAC;AAED,eAAW,CAAC,oBAAoB,gBAAgB,KAAK,KAAK,kBAAkB,QAAQ,GAAG;AACnF,YAAM,+BAA+B,KAAK,4BAA4B,kBAAkB;AACxF,UAAI,yBAAyB,8BAA8B;AACvD,yBAAiB,QAAQ,QAAQ;AAAA,MACrC;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,OAAO,UAAU,YAAY;AACzB,QAAI;AACJ,UAAM,uBAAuB,KAAK,4BAA4B,UAAU;AACxE,UAAM,qBAAqB,KAAK,KAAK,gBAAgB,IAAI,oBAAoB,OAAO,QAAQ,OAAO,SAAS,KAAK,oBAAI,IAAI;AACzH,sBAAkB,IAAI,QAAQ;AAC9B,SAAK,gBAAgB,IAAI,sBAAsB,iBAAiB;AAChE,UAAM,mBAAmB,KAAK,UAAU,IAAI,oBAAoB;AAChE,QAAI,kBAAkB;AAClB,eAAS,kBAAkB,oBAAoB;AAAA,IACnD;AACA,WAAO,MAAM;AACT,wBAAkB,OAAO,QAAQ;AAAA,IACrC;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,sBAAsB,UAAU,YAAY;AACxC,UAAM,YAAY,KAAK,gBAAgB,IAAI,UAAU;AACrD,QAAI,CAAC,WAAW;AACZ;AAAA,IACJ;AACA,eAAW,YAAY,WAAW;AAC9B,UAAI;AACA,iBAAS,UAAU,UAAU;AAAA,MACjC,SACO,IAAI;AAAA,MAEX;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,uBAAuB,EAAE,oBAAoB,UAAU,CAAC,EAAE,GAAG;AACzD,QAAI,WAAW,KAAK,UAAU,IAAI,kBAAkB;AACpD,QAAI,CAAC,YAAY,KAAK,WAAW;AAC7B,iBAAW,KAAK,UAAU,gBAAgB,KAAK,WAAW;AAAA,QACtD,oBAAoB,8BAA8B,kBAAkB;AAAA,QACpE;AAAA,MACJ,CAAC;AACD,WAAK,UAAU,IAAI,oBAAoB,QAAQ;AAC/C,WAAK,iBAAiB,IAAI,oBAAoB,OAAO;AAMrD,WAAK,sBAAsB,UAAU,kBAAkB;AAMvD,UAAI,KAAK,UAAU,mBAAmB;AAClC,YAAI;AACA,eAAK,UAAU,kBAAkB,KAAK,WAAW,oBAAoB,QAAQ;AAAA,QACjF,SACO,IAAI;AAAA,QAEX;AAAA,MACJ;AAAA,IACJ;AACA,WAAO,YAAY;AAAA,EACvB;AAAA,EACA,4BAA4B,aAAa,oBAAoB;AACzD,QAAI,KAAK,WAAW;AAChB,aAAO,KAAK,UAAU,oBAAoB,aAAa;AAAA,IAC3D,OACK;AACD,aAAO;AAAA,IACX;AAAA,EACJ;AAAA,EACA,uBAAuB;AACnB,WAAQ,CAAC,CAAC,KAAK,aACX,KAAK,UAAU,sBAAsB;AAAA,EAC7C;AACJ;AAEA,SAAS,8BAA8B,YAAY;AAC/C,SAAO,eAAe,qBAAqB,SAAY;AAC3D;AACA,SAAS,iBAAiB,WAAW;AACjC,SAAO,UAAU,sBAAsB;AAC3C;AAqBA,IAAM,qBAAN,MAAyB;AAAA,EACrB,YAAYA,OAAM;AACd,SAAK,OAAOA;AACZ,SAAK,YAAY,oBAAI,IAAI;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,aAAa,WAAW;AACpB,UAAM,WAAW,KAAK,YAAY,UAAU,IAAI;AAChD,QAAI,SAAS,eAAe,GAAG;AAC3B,YAAM,IAAI,MAAM,aAAa,UAAU,IAAI,qCAAqC,KAAK,IAAI,EAAE;AAAA,IAC/F;AACA,aAAS,aAAa,SAAS;AAAA,EACnC;AAAA,EACA,wBAAwB,WAAW;AAC/B,UAAM,WAAW,KAAK,YAAY,UAAU,IAAI;AAChD,QAAI,SAAS,eAAe,GAAG;AAE3B,WAAK,UAAU,OAAO,UAAU,IAAI;AAAA,IACxC;AACA,SAAK,aAAa,SAAS;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,YAAYA,OAAM;AACd,QAAI,KAAK,UAAU,IAAIA,KAAI,GAAG;AAC1B,aAAO,KAAK,UAAU,IAAIA,KAAI;AAAA,IAClC;AAEA,UAAM,WAAW,IAAI,SAASA,OAAM,IAAI;AACxC,SAAK,UAAU,IAAIA,OAAM,QAAQ;AACjC,WAAO;AAAA,EACX;AAAA,EACA,eAAe;AACX,WAAO,MAAM,KAAK,KAAK,UAAU,OAAO,CAAC;AAAA,EAC7C;AACJ;;;AClYA,IAAM,YAAY,CAAC;AAYnB,IAAI;AAAA,CACH,SAAUC,WAAU;AACjB,EAAAA,UAASA,UAAS,OAAO,IAAI,CAAC,IAAI;AAClC,EAAAA,UAASA,UAAS,SAAS,IAAI,CAAC,IAAI;AACpC,EAAAA,UAASA,UAAS,MAAM,IAAI,CAAC,IAAI;AACjC,EAAAA,UAASA,UAAS,MAAM,IAAI,CAAC,IAAI;AACjC,EAAAA,UAASA,UAAS,OAAO,IAAI,CAAC,IAAI;AAClC,EAAAA,UAASA,UAAS,QAAQ,IAAI,CAAC,IAAI;AACvC,GAAG,aAAa,WAAW,CAAC,EAAE;AAC9B,IAAM,oBAAoB;AAAA,EACtB,SAAS,SAAS;AAAA,EAClB,WAAW,SAAS;AAAA,EACpB,QAAQ,SAAS;AAAA,EACjB,QAAQ,SAAS;AAAA,EACjB,SAAS,SAAS;AAAA,EAClB,UAAU,SAAS;AACvB;AAIA,IAAM,kBAAkB,SAAS;AAOjC,IAAM,gBAAgB;AAAA,EAClB,CAAC,SAAS,KAAK,GAAG;AAAA,EAClB,CAAC,SAAS,OAAO,GAAG;AAAA,EACpB,CAAC,SAAS,IAAI,GAAG;AAAA,EACjB,CAAC,SAAS,IAAI,GAAG;AAAA,EACjB,CAAC,SAAS,KAAK,GAAG;AACtB;AAMA,IAAM,oBAAoB,CAAC,UAAU,YAAY,SAAS;AACtD,MAAI,UAAU,SAAS,UAAU;AAC7B;AAAA,EACJ;AACA,QAAM,OAAM,oBAAI,KAAK,GAAE,YAAY;AACnC,QAAM,SAAS,cAAc,OAAO;AACpC,MAAI,QAAQ;AACR,YAAQ,MAAM,EAAE,IAAI,GAAG,MAAM,SAAS,IAAI,KAAK,GAAG,IAAI;AAAA,EAC1D,OACK;AACD,UAAM,IAAI,MAAM,8DAA8D,OAAO,GAAG;AAAA,EAC5F;AACJ;AACA,IAAM,SAAN,MAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOT,YAAYC,OAAM;AACd,SAAK,OAAOA;AAIZ,SAAK,YAAY;AAKjB,SAAK,cAAc;AAInB,SAAK,kBAAkB;AAIvB,cAAU,KAAK,IAAI;AAAA,EACvB;AAAA,EACA,IAAI,WAAW;AACX,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,SAAS,KAAK;AACd,QAAI,EAAE,OAAO,WAAW;AACpB,YAAM,IAAI,UAAU,kBAAkB,GAAG,4BAA4B;AAAA,IACzE;AACA,SAAK,YAAY;AAAA,EACrB;AAAA;AAAA,EAEA,YAAY,KAAK;AACb,SAAK,YAAY,OAAO,QAAQ,WAAW,kBAAkB,GAAG,IAAI;AAAA,EACxE;AAAA,EACA,IAAI,aAAa;AACb,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,WAAW,KAAK;AAChB,QAAI,OAAO,QAAQ,YAAY;AAC3B,YAAM,IAAI,UAAU,mDAAmD;AAAA,IAC3E;AACA,SAAK,cAAc;AAAA,EACvB;AAAA,EACA,IAAI,iBAAiB;AACjB,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,eAAe,KAAK;AACpB,SAAK,kBAAkB;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA,EAIA,SAAS,MAAM;AACX,SAAK,mBAAmB,KAAK,gBAAgB,MAAM,SAAS,OAAO,GAAG,IAAI;AAC1E,SAAK,YAAY,MAAM,SAAS,OAAO,GAAG,IAAI;AAAA,EAClD;AAAA,EACA,OAAO,MAAM;AACT,SAAK,mBACD,KAAK,gBAAgB,MAAM,SAAS,SAAS,GAAG,IAAI;AACxD,SAAK,YAAY,MAAM,SAAS,SAAS,GAAG,IAAI;AAAA,EACpD;AAAA,EACA,QAAQ,MAAM;AACV,SAAK,mBAAmB,KAAK,gBAAgB,MAAM,SAAS,MAAM,GAAG,IAAI;AACzE,SAAK,YAAY,MAAM,SAAS,MAAM,GAAG,IAAI;AAAA,EACjD;AAAA,EACA,QAAQ,MAAM;AACV,SAAK,mBAAmB,KAAK,gBAAgB,MAAM,SAAS,MAAM,GAAG,IAAI;AACzE,SAAK,YAAY,MAAM,SAAS,MAAM,GAAG,IAAI;AAAA,EACjD;AAAA,EACA,SAAS,MAAM;AACX,SAAK,mBAAmB,KAAK,gBAAgB,MAAM,SAAS,OAAO,GAAG,IAAI;AAC1E,SAAK,YAAY,MAAM,SAAS,OAAO,GAAG,IAAI;AAAA,EAClD;AACJ;AACA,SAAS,YAAY,OAAO;AACxB,YAAU,QAAQ,UAAQ;AACtB,SAAK,YAAY,KAAK;AAAA,EAC1B,CAAC;AACL;AACA,SAAS,kBAAkB,aAAa,SAAS;AAC7C,aAAW,YAAY,WAAW;AAC9B,QAAI,iBAAiB;AACrB,QAAI,WAAW,QAAQ,OAAO;AAC1B,uBAAiB,kBAAkB,QAAQ,KAAK;AAAA,IACpD;AACA,QAAI,gBAAgB,MAAM;AACtB,eAAS,iBAAiB;AAAA,IAC9B,OACK;AACD,eAAS,iBAAiB,CAACC,WAAU,UAAU,SAAS;AACpD,cAAM,UAAU,KACX,IAAI,SAAO;AACZ,cAAI,OAAO,MAAM;AACb,mBAAO;AAAA,UACX,WACS,OAAO,QAAQ,UAAU;AAC9B,mBAAO;AAAA,UACX,WACS,OAAO,QAAQ,YAAY,OAAO,QAAQ,WAAW;AAC1D,mBAAO,IAAI,SAAS;AAAA,UACxB,WACS,eAAe,OAAO;AAC3B,mBAAO,IAAI;AAAA,UACf,OACK;AACD,gBAAI;AACA,qBAAO,KAAK,UAAU,GAAG;AAAA,YAC7B,SACO,SAAS;AACZ,qBAAO;AAAA,YACX;AAAA,UACJ;AAAA,QACJ,CAAC,EACI,OAAO,SAAO,GAAG,EACjB,KAAK,GAAG;AACb,YAAI,UAAU,mBAAmB,QAAQ,mBAAmB,SAAS,iBAAiBA,UAAS,WAAW;AACtG,sBAAY;AAAA,YACR,OAAO,SAAS,KAAK,EAAE,YAAY;AAAA,YACnC;AAAA,YACA;AAAA,YACA,MAAMA,UAAS;AAAA,UACnB,CAAC;AAAA,QACL;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACJ;;;ACvNA,IAAM,gBAAgB,CAAC,QAAQ,iBAAiB,aAAa,KAAK,CAAC,MAAM,kBAAkB,CAAC;AAE5F,IAAI;AACJ,IAAI;AAEJ,SAAS,uBAAuB;AAC5B,SAAQ,sBACH,oBAAoB;AAAA,IACjB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACR;AAEA,SAAS,0BAA0B;AAC/B,SAAQ,yBACH,uBAAuB;AAAA,IACpB,UAAU,UAAU;AAAA,IACpB,UAAU,UAAU;AAAA,IACpB,UAAU,UAAU;AAAA,EACxB;AACR;AACA,IAAM,mBAAmB,oBAAI,QAAQ;AACrC,IAAM,qBAAqB,oBAAI,QAAQ;AACvC,IAAM,2BAA2B,oBAAI,QAAQ;AAC7C,IAAM,iBAAiB,oBAAI,QAAQ;AACnC,IAAM,wBAAwB,oBAAI,QAAQ;AAC1C,SAAS,iBAAiB,SAAS;AAC/B,QAAM,UAAU,IAAI,QAAQ,CAAC,SAAS,WAAW;AAC7C,UAAM,WAAW,MAAM;AACnB,cAAQ,oBAAoB,WAAW,OAAO;AAC9C,cAAQ,oBAAoB,SAAS,KAAK;AAAA,IAC9C;AACA,UAAM,UAAU,MAAM;AAClB,cAAQ,KAAK,QAAQ,MAAM,CAAC;AAC5B,eAAS;AAAA,IACb;AACA,UAAM,QAAQ,MAAM;AAChB,aAAO,QAAQ,KAAK;AACpB,eAAS;AAAA,IACb;AACA,YAAQ,iBAAiB,WAAW,OAAO;AAC3C,YAAQ,iBAAiB,SAAS,KAAK;AAAA,EAC3C,CAAC;AACD,UACK,KAAK,CAAC,UAAU;AAGjB,QAAI,iBAAiB,WAAW;AAC5B,uBAAiB,IAAI,OAAO,OAAO;AAAA,IACvC;AAAA,EAEJ,CAAC,EACI,MAAM,MAAM;AAAA,EAAE,CAAC;AAGpB,wBAAsB,IAAI,SAAS,OAAO;AAC1C,SAAO;AACX;AACA,SAAS,+BAA+B,IAAI;AAExC,MAAI,mBAAmB,IAAI,EAAE;AACzB;AACJ,QAAM,OAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AAC1C,UAAM,WAAW,MAAM;AACnB,SAAG,oBAAoB,YAAY,QAAQ;AAC3C,SAAG,oBAAoB,SAAS,KAAK;AACrC,SAAG,oBAAoB,SAAS,KAAK;AAAA,IACzC;AACA,UAAM,WAAW,MAAM;AACnB,cAAQ;AACR,eAAS;AAAA,IACb;AACA,UAAM,QAAQ,MAAM;AAChB,aAAO,GAAG,SAAS,IAAI,aAAa,cAAc,YAAY,CAAC;AAC/D,eAAS;AAAA,IACb;AACA,OAAG,iBAAiB,YAAY,QAAQ;AACxC,OAAG,iBAAiB,SAAS,KAAK;AAClC,OAAG,iBAAiB,SAAS,KAAK;AAAA,EACtC,CAAC;AAED,qBAAmB,IAAI,IAAI,IAAI;AACnC;AACA,IAAI,gBAAgB;AAAA,EAChB,IAAI,QAAQ,MAAM,UAAU;AACxB,QAAI,kBAAkB,gBAAgB;AAElC,UAAI,SAAS;AACT,eAAO,mBAAmB,IAAI,MAAM;AAExC,UAAI,SAAS,oBAAoB;AAC7B,eAAO,OAAO,oBAAoB,yBAAyB,IAAI,MAAM;AAAA,MACzE;AAEA,UAAI,SAAS,SAAS;AAClB,eAAO,SAAS,iBAAiB,CAAC,IAC5B,SACA,SAAS,YAAY,SAAS,iBAAiB,CAAC,CAAC;AAAA,MAC3D;AAAA,IACJ;AAEA,WAAO,KAAK,OAAO,IAAI,CAAC;AAAA,EAC5B;AAAA,EACA,IAAI,QAAQ,MAAM,OAAO;AACrB,WAAO,IAAI,IAAI;AACf,WAAO;AAAA,EACX;AAAA,EACA,IAAI,QAAQ,MAAM;AACd,QAAI,kBAAkB,mBACjB,SAAS,UAAU,SAAS,UAAU;AACvC,aAAO;AAAA,IACX;AACA,WAAO,QAAQ;AAAA,EACnB;AACJ;AACA,SAAS,aAAa,UAAU;AAC5B,kBAAgB,SAAS,aAAa;AAC1C;AACA,SAAS,aAAa,MAAM;AAIxB,MAAI,SAAS,YAAY,UAAU,eAC/B,EAAE,sBAAsB,eAAe,YAAY;AACnD,WAAO,SAAU,eAAe,MAAM;AAClC,YAAM,KAAK,KAAK,KAAK,OAAO,IAAI,GAAG,YAAY,GAAG,IAAI;AACtD,+BAAyB,IAAI,IAAI,WAAW,OAAO,WAAW,KAAK,IAAI,CAAC,UAAU,CAAC;AACnF,aAAO,KAAK,EAAE;AAAA,IAClB;AAAA,EACJ;AAMA,MAAI,wBAAwB,EAAE,SAAS,IAAI,GAAG;AAC1C,WAAO,YAAa,MAAM;AAGtB,WAAK,MAAM,OAAO,IAAI,GAAG,IAAI;AAC7B,aAAO,KAAK,iBAAiB,IAAI,IAAI,CAAC;AAAA,IAC1C;AAAA,EACJ;AACA,SAAO,YAAa,MAAM;AAGtB,WAAO,KAAK,KAAK,MAAM,OAAO,IAAI,GAAG,IAAI,CAAC;AAAA,EAC9C;AACJ;AACA,SAAS,uBAAuB,OAAO;AACnC,MAAI,OAAO,UAAU;AACjB,WAAO,aAAa,KAAK;AAG7B,MAAI,iBAAiB;AACjB,mCAA+B,KAAK;AACxC,MAAI,cAAc,OAAO,qBAAqB,CAAC;AAC3C,WAAO,IAAI,MAAM,OAAO,aAAa;AAEzC,SAAO;AACX;AACA,SAAS,KAAK,OAAO;AAGjB,MAAI,iBAAiB;AACjB,WAAO,iBAAiB,KAAK;AAGjC,MAAI,eAAe,IAAI,KAAK;AACxB,WAAO,eAAe,IAAI,KAAK;AACnC,QAAM,WAAW,uBAAuB,KAAK;AAG7C,MAAI,aAAa,OAAO;AACpB,mBAAe,IAAI,OAAO,QAAQ;AAClC,0BAAsB,IAAI,UAAU,KAAK;AAAA,EAC7C;AACA,SAAO;AACX;AACA,IAAM,SAAS,CAAC,UAAU,sBAAsB,IAAI,KAAK;;;AC5KzD,SAAS,OAAOC,OAAMC,UAAS,EAAE,SAAS,SAAS,UAAU,WAAW,IAAI,CAAC,GAAG;AAC5E,QAAM,UAAU,UAAU,KAAKD,OAAMC,QAAO;AAC5C,QAAM,cAAc,KAAK,OAAO;AAChC,MAAI,SAAS;AACT,YAAQ,iBAAiB,iBAAiB,CAAC,UAAU;AACjD,cAAQ,KAAK,QAAQ,MAAM,GAAG,MAAM,YAAY,MAAM,YAAY,KAAK,QAAQ,WAAW,GAAG,KAAK;AAAA,IACtG,CAAC;AAAA,EACL;AACA,MAAI,SAAS;AACT,YAAQ,iBAAiB,WAAW,CAAC,UAAU;AAAA;AAAA,MAE/C,MAAM;AAAA,MAAY,MAAM;AAAA,MAAY;AAAA,IAAK,CAAC;AAAA,EAC9C;AACA,cACK,KAAK,CAAC,OAAO;AACd,QAAI;AACA,SAAG,iBAAiB,SAAS,MAAM,WAAW,CAAC;AACnD,QAAI,UAAU;AACV,SAAG,iBAAiB,iBAAiB,CAAC,UAAU,SAAS,MAAM,YAAY,MAAM,YAAY,KAAK,CAAC;AAAA,IACvG;AAAA,EACJ,CAAC,EACI,MAAM,MAAM;AAAA,EAAE,CAAC;AACpB,SAAO;AACX;AAgBA,IAAM,cAAc,CAAC,OAAO,UAAU,UAAU,cAAc,OAAO;AACrE,IAAM,eAAe,CAAC,OAAO,OAAO,UAAU,OAAO;AACrD,IAAM,gBAAgB,oBAAI,IAAI;AAC9B,SAAS,UAAU,QAAQ,MAAM;AAC7B,MAAI,EAAE,kBAAkB,eACpB,EAAE,QAAQ,WACV,OAAO,SAAS,WAAW;AAC3B;AAAA,EACJ;AACA,MAAI,cAAc,IAAI,IAAI;AACtB,WAAO,cAAc,IAAI,IAAI;AACjC,QAAM,iBAAiB,KAAK,QAAQ,cAAc,EAAE;AACpD,QAAM,WAAW,SAAS;AAC1B,QAAM,UAAU,aAAa,SAAS,cAAc;AACpD;AAAA;AAAA,IAEA,EAAE,mBAAmB,WAAW,WAAW,gBAAgB,cACvD,EAAE,WAAW,YAAY,SAAS,cAAc;AAAA,IAAI;AACpD;AAAA,EACJ;AACA,QAAM,SAAS,eAAgB,cAAc,MAAM;AAE/C,UAAM,KAAK,KAAK,YAAY,WAAW,UAAU,cAAc,UAAU;AACzE,QAAIC,UAAS,GAAG;AAChB,QAAI;AACA,MAAAA,UAASA,QAAO,MAAM,KAAK,MAAM,CAAC;AAMtC,YAAQ,MAAM,QAAQ,IAAI;AAAA,MACtBA,QAAO,cAAc,EAAE,GAAG,IAAI;AAAA,MAC9B,WAAW,GAAG;AAAA,IAClB,CAAC,GAAG,CAAC;AAAA,EACT;AACA,gBAAc,IAAI,MAAM,MAAM;AAC9B,SAAO;AACX;AACA,aAAa,CAAC,aAAc,iCACrB,WADqB;AAAA,EAExB,KAAK,CAAC,QAAQ,MAAM,aAAa,UAAU,QAAQ,IAAI,KAAK,SAAS,IAAI,QAAQ,MAAM,QAAQ;AAAA,EAC/F,KAAK,CAAC,QAAQ,SAAS,CAAC,CAAC,UAAU,QAAQ,IAAI,KAAK,SAAS,IAAI,QAAQ,IAAI;AACjF,EAAE;;;ACtEF,IAAM,4BAAN,MAAgC;AAAA,EAC5B,YAAY,WAAW;AACnB,SAAK,YAAY;AAAA,EACrB;AAAA;AAAA;AAAA,EAGA,wBAAwB;AACpB,UAAM,YAAY,KAAK,UAAU,aAAa;AAG9C,WAAO,UACF,IAAI,cAAY;AACjB,UAAI,yBAAyB,QAAQ,GAAG;AACpC,cAAM,UAAU,SAAS,aAAa;AACtC,eAAO,GAAG,QAAQ,OAAO,IAAI,QAAQ,OAAO;AAAA,MAChD,OACK;AACD,eAAO;AAAA,MACX;AAAA,IACJ,CAAC,EACI,OAAO,eAAa,SAAS,EAC7B,KAAK,GAAG;AAAA,EACjB;AACJ;AASA,SAAS,yBAAyB,UAAU;AACxC,QAAM,YAAY,SAAS,aAAa;AACxC,UAAQ,cAAc,QAAQ,cAAc,SAAS,SAAS,UAAU,UAAU;AACtF;AAEA,IAAM,SAAS;AACf,IAAM,YAAY;AAkBlB,IAAM,SAAS,IAAI,OAAO,eAAe;AAEzC,IAAM,SAAS;AAEf,IAAM,SAAS;AAEf,IAAM,SAAS;AAEf,IAAM,SAAS;AAEf,IAAM,SAAS;AAEf,IAAM,SAAS;AAEf,IAAM,SAAS;AAEf,IAAM,SAAS;AAEf,IAAM,SAAS;AAEf,IAAM,SAAS;AAEf,IAAM,SAAS;AAEf,IAAM,SAAS;AAEf,IAAM,SAAS;AAEf,IAAM,SAAS;AAEf,IAAM,SAAS;AAEf,IAAM,SAAS;AAEf,IAAM,SAAS;AAEf,IAAM,SAAS;AAEf,IAAM,SAAS;AAEf,IAAM,SAAS;AAEf,IAAM,SAAS;AAEf,IAAM,SAAS;AAEf,IAAM,SAAS;AAEf,IAAM,SAAS;AAEf,IAAM,SAAS;AAEf,IAAM,OAAO;AACb,IAAM,UAAU;AAuBhB,IAAMC,sBAAqB;AAC3B,IAAM,sBAAsB;AAAA,EACxB,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,CAAC,MAAM,GAAG;AAAA,EACV,WAAW;AAAA;AAAA,EACX,CAAC,IAAI,GAAG;AACZ;AAqBA,IAAM,QAAQ,oBAAI,IAAI;AAItB,IAAM,cAAc,oBAAI,IAAI;AAO5B,IAAM,cAAc,oBAAI,IAAI;AAM5B,SAAS,cAAc,KAAK,WAAW;AACnC,MAAI;AACA,QAAI,UAAU,aAAa,SAAS;AAAA,EACxC,SACO,GAAG;AACN,WAAO,MAAM,aAAa,UAAU,IAAI,wCAAwC,IAAI,IAAI,IAAI,CAAC;AAAA,EACjG;AACJ;AAKA,SAAS,yBAAyB,KAAK,WAAW;AAC9C,MAAI,UAAU,wBAAwB,SAAS;AACnD;AAQA,SAAS,mBAAmB,WAAW;AACnC,QAAM,gBAAgB,UAAU;AAChC,MAAI,YAAY,IAAI,aAAa,GAAG;AAChC,WAAO,MAAM,sDAAsD,aAAa,GAAG;AACnF,WAAO;AAAA,EACX;AACA,cAAY,IAAI,eAAe,SAAS;AAExC,aAAW,OAAO,MAAM,OAAO,GAAG;AAC9B,kBAAc,KAAK,SAAS;AAAA,EAChC;AACA,aAAW,aAAa,YAAY,OAAO,GAAG;AAC1C,kBAAc,WAAW,SAAS;AAAA,EACtC;AACA,SAAO;AACX;AAUA,SAAS,aAAa,KAAKC,OAAM;AAC7B,QAAM,sBAAsB,IAAI,UAC3B,YAAY,WAAW,EACvB,aAAa,EAAE,UAAU,KAAK,CAAC;AACpC,MAAI,qBAAqB;AACrB,SAAK,oBAAoB,iBAAiB;AAAA,EAC9C;AACA,SAAO,IAAI,UAAU,YAAYA,KAAI;AACzC;AASA,SAAS,uBAAuB,KAAKA,OAAM,qBAAqBD,qBAAoB;AAChF,eAAa,KAAKC,KAAI,EAAE,cAAc,kBAAkB;AAC5D;AASA,SAAS,eAAe,KAAK;AACzB,SAAO,IAAI,YAAY;AAC3B;AASA,SAAS,qBAAqB,KAAK;AAC/B,MAAI,QAAQ,QAAQ,QAAQ,QAAW;AACnC,WAAO;AAAA,EACX;AACA,SAAO,IAAI,aAAa;AAC5B;AAMA,SAAS,mBAAmB;AACxB,cAAY,MAAM;AACtB;AAkBA,IAAM,SAAS;AAAA,EACX;AAAA,IAAC;AAAA;AAAA,EAA8B,GAAG;AAAA,EAElC;AAAA,IAAC;AAAA;AAAA,EAA0C,GAAG;AAAA,EAC9C;AAAA,IAAC;AAAA;AAAA,EAA4C,GAAG;AAAA,EAChD;AAAA,IAAC;AAAA;AAAA,EAAwC,GAAG;AAAA,EAC5C;AAAA,IAAC;AAAA;AAAA,EAAsD,GAAG;AAAA,EAC1D;AAAA,IAAC;AAAA;AAAA,EAAsC,GAAG;AAAA,EAC1C;AAAA,IAAC;AAAA;AAAA,EAA0D,GAAG;AAAA,EAE9D;AAAA,IAAC;AAAA;AAAA,EAA0D,GAAG;AAAA,EAC9D;AAAA,IAAC;AAAA;AAAA,EAAkC,GAAG;AAAA,EACtC;AAAA,IAAC;AAAA;AAAA,EAAgC,GAAG;AAAA,EACpC;AAAA,IAAC;AAAA;AAAA,EAAkC,GAAG;AAAA,EACtC;AAAA,IAAC;AAAA;AAAA,EAAsC,GAAG;AAAA,EAC1C;AAAA,IAAC;AAAA;AAAA,EAAwF,GAAG;AAAA,EAC5F;AAAA,IAAC;AAAA;AAAA,EAA8E,GAAG;AACtF;AACA,IAAM,gBAAgB,IAAI,aAAa,OAAO,YAAY,MAAM;AAkBhE,IAAM,kBAAN,MAAsB;AAAA,EAClB,YAAY,SAAS,QAAQ,WAAW;AACpC,SAAK,aAAa;AAClB,SAAK,WAAW,OAAO,OAAO,CAAC,GAAG,OAAO;AACzC,SAAK,UAAU,OAAO,OAAO,CAAC,GAAG,MAAM;AACvC,SAAK,QAAQ,OAAO;AACpB,SAAK,kCACD,OAAO;AACX,SAAK,aAAa;AAClB,SAAK,UAAU,aAAa,IAAI;AAAA,MAAU;AAAA,MAAO,MAAM;AAAA,MAAM;AAAA;AAAA,IAAmC,CAAC;AAAA,EACrG;AAAA,EACA,IAAI,iCAAiC;AACjC,SAAK,eAAe;AACpB,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,+BAA+B,KAAK;AACpC,SAAK,eAAe;AACpB,SAAK,kCAAkC;AAAA,EAC3C;AAAA,EACA,IAAI,OAAO;AACP,SAAK,eAAe;AACpB,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,UAAU;AACV,SAAK,eAAe;AACpB,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,SAAS;AACT,SAAK,eAAe;AACpB,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,YAAY;AACZ,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,YAAY;AACZ,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,UAAU,KAAK;AACf,SAAK,aAAa;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB;AACb,QAAI,KAAK,WAAW;AAChB,YAAM,cAAc,OAAO,eAA0C,EAAE,SAAS,KAAK,MAAM,CAAC;AAAA,IAChG;AAAA,EACJ;AACJ;AAqBA,SAAS,iBAAiB,aAAa,WAAW;AAC9C,QAAM,aAAa,aAAa,YAAY,MAAM,GAAG,EAAE,CAAC,CAAC;AACzD,MAAI,eAAe,MAAM;AACrB,YAAQ,MAAM,qBAAqB,SAAS,+CAA+C;AAC3F;AAAA,EACJ;AACA,QAAM,WAAW,KAAK,MAAM,UAAU,EAAE;AACxC,MAAI,aAAa,QAAW;AACxB,YAAQ,MAAM,qBAAqB,SAAS,mDAAmD;AAC/F;AAAA,EACJ;AACA,QAAM,MAAM,KAAK,MAAM,UAAU,EAAE,MAAM;AACzC,QAAM,OAAM,oBAAI,KAAK,GAAE,QAAQ;AAC/B,QAAM,OAAO,MAAM;AACnB,MAAI,QAAQ,GAAG;AACX,YAAQ,MAAM,qBAAqB,SAAS,qCAAqC;AAAA,EACrF;AACJ;AACA,IAAM,wBAAN,cAAoC,gBAAgB;AAAA,EAChD,YAAY,SAAS,cAAcA,OAAM,WAAW;AAEhD,UAAM,iCAAiC,aAAa,mCAAmC,SACjF,aAAa,iCACb;AAEN,UAAM,SAAS;AAAA,MACX,MAAAA;AAAA,MACA;AAAA,IACJ;AACA,QAAI,QAAQ,WAAW,QAAW;AAE9B,YAAM,SAAS,QAAQ,SAAS;AAAA,IACpC,OACK;AACD,YAAM,UAAU;AAChB,YAAM,QAAQ,SAAS,QAAQ,SAAS;AAAA,IAC5C;AAEA,SAAK,gBAAgB,OAAO,OAAO,EAAE,+BAA+B,GAAG,YAAY;AAEnF,QAAI,KAAK,cAAc,aAAa;AAChC,uBAAiB,KAAK,cAAc,aAAa,aAAa;AAAA,IAClE;AAEA,QAAI,KAAK,cAAc,eAAe;AAClC,uBAAiB,KAAK,cAAc,eAAe,eAAe;AAAA,IACtE;AACA,SAAK,wBAAwB;AAC7B,QAAI,OAAO,yBAAyB,aAAa;AAC7C,WAAK,wBAAwB,IAAI,qBAAqB,MAAM;AACxD,aAAK,iBAAiB;AAAA,MAC1B,CAAC;AAAA,IACL;AACA,SAAK,YAAY;AACjB,SAAK,YAAY,KAAK,cAAc,cAAc;AAGlD,SAAK,cAAc,iBAAiB;AACpC,iBAAa,iBAAiB;AAC9B,oBAAgB,QAAQ,WAAW,WAAW;AAAA,EAClD;AAAA,EACA,SAAS;AACL,WAAO;AAAA,EACX;AAAA,EACA,IAAI,WAAW;AACX,WAAO,KAAK;AAAA,EAChB;AAAA;AAAA;AAAA,EAGA,YAAY,KAAK;AACb,QAAI,KAAK,WAAW;AAChB;AAAA,IACJ;AACA,SAAK;AACL,QAAI,QAAQ,UAAa,KAAK,0BAA0B,MAAM;AAC1D,WAAK,sBAAsB,SAAS,KAAK,IAAI;AAAA,IACjD;AAAA,EACJ;AAAA;AAAA,EAEA,cAAc;AACV,QAAI,KAAK,WAAW;AAChB,aAAO;AAAA,IACX;AACA,WAAO,EAAE,KAAK;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA,EAIA,mBAAmB;AACf,SAAK,UAAU,IAAI;AAAA,EACvB;AAAA,EACA,IAAI,WAAW;AACX,SAAK,eAAe;AACpB,WAAO,KAAK;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB;AACb,QAAI,KAAK,WAAW;AAChB,YAAM,cAAc;AAAA,QAAO;AAAA;AAAA,MAAsD;AAAA,IACrF;AAAA,EACJ;AACJ;AAuBA,IAAM,cAAc;AACpB,SAAS,cAAc,UAAU,YAAY,CAAC,GAAG;AAC7C,MAAI,UAAU;AACd,MAAI,OAAO,cAAc,UAAU;AAC/B,UAAMA,QAAO;AACb,gBAAY,EAAE,MAAAA,MAAK;AAAA,EACvB;AACA,QAAM,SAAS,OAAO,OAAO,EAAE,MAAMD,qBAAoB,gCAAgC,KAAK,GAAG,SAAS;AAC1G,QAAMC,QAAO,OAAO;AACpB,MAAI,OAAOA,UAAS,YAAY,CAACA,OAAM;AACnC,UAAM,cAAc,OAAO,gBAA4C;AAAA,MACnE,SAAS,OAAOA,KAAI;AAAA,IACxB,CAAC;AAAA,EACL;AACA,cAAY,UAAU,oBAAoB;AAC1C,MAAI,CAAC,SAAS;AACV,UAAM,cAAc;AAAA,MAAO;AAAA;AAAA,IAAsC;AAAA,EACrE;AACA,QAAM,cAAc,MAAM,IAAIA,KAAI;AAClC,MAAI,aAAa;AAEb,QAAI,UAAU,SAAS,YAAY,OAAO,KACtC,UAAU,QAAQ,YAAY,MAAM,GAAG;AACvC,aAAO;AAAA,IACX,OACK;AACD,YAAM,cAAc,OAAO,iBAA8C,EAAE,SAASA,MAAK,CAAC;AAAA,IAC9F;AAAA,EACJ;AACA,QAAM,YAAY,IAAI,mBAAmBA,KAAI;AAC7C,aAAW,aAAa,YAAY,OAAO,GAAG;AAC1C,cAAU,aAAa,SAAS;AAAA,EACpC;AACA,QAAM,SAAS,IAAI,gBAAgB,SAAS,QAAQ,SAAS;AAC7D,QAAM,IAAIA,OAAM,MAAM;AACtB,SAAO;AACX;AACA,SAAS,oBAAoB,UAAU,kBAAkB;AACrD,MAAI,UAAU,KAAK,CAAC,YAAY,GAAG;AAE/B,UAAM,cAAc;AAAA,MAAO;AAAA;AAAA,IAA8E;AAAA,EAC7G;AACA,MAAI,iBAAiB,mCAAmC,QAAW;AAC/D,qBAAiB,iCAAiC;AAAA,EACtD;AACA,MAAI;AACJ,MAAI,eAAe,QAAQ,GAAG;AAC1B,iBAAa,SAAS;AAAA,EAC1B,OACK;AACD,iBAAa;AAAA,EACjB;AAEA,QAAM,UAAU,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,gBAAgB,GAAG,UAAU;AAG7E,MAAI,QAAQ,mBAAmB,QAAW;AACtC,WAAO,QAAQ;AAAA,EACnB;AACA,QAAM,WAAW,CAAC,MAAM;AACpB,WAAO,CAAC,GAAG,CAAC,EAAE,OAAO,CAAC,MAAM,MAAO,KAAK,KAAK,IAAI,IAAI,IAAI,EAAE,WAAW,CAAC,IAAK,GAAG,CAAC;AAAA,EACpF;AACA,MAAI,iBAAiB,mBAAmB,QAAW;AAC/C,QAAI,OAAO,yBAAyB,aAAa;AAC7C,YAAM,cAAc,OAAO,uCAA0F,CAAC,CAAC;AAAA,IAC3H;AAAA,EACJ;AACA,QAAM,aAAa,KAAK,SAAS,KAAK,UAAU,OAAO,CAAC;AACxD,QAAM,cAAc,YAAY,IAAI,UAAU;AAC9C,MAAI,aAAa;AACb,gBAAY,YAAY,iBAAiB,cAAc;AACvD,WAAO;AAAA,EACX;AACA,QAAM,YAAY,IAAI,mBAAmB,UAAU;AACnD,aAAW,aAAa,YAAY,OAAO,GAAG;AAC1C,cAAU,aAAa,SAAS;AAAA,EACpC;AACA,QAAM,SAAS,IAAI,sBAAsB,YAAY,kBAAkB,YAAY,SAAS;AAC5F,cAAY,IAAI,YAAY,MAAM;AAClC,SAAO;AACX;AA8BA,SAAS,OAAOA,QAAOD,qBAAoB;AACvC,QAAM,MAAM,MAAM,IAAIC,KAAI;AAC1B,MAAI,CAAC,OAAOA,UAASD,uBAAsB,oBAAoB,GAAG;AAC9D,WAAO,cAAc;AAAA,EACzB;AACA,MAAI,CAAC,KAAK;AACN,UAAM,cAAc,OAAO,UAAgC,EAAE,SAASC,MAAK,CAAC;AAAA,EAChF;AACA,SAAO;AACX;AAKA,SAAS,UAAU;AACf,SAAO,MAAM,KAAK,MAAM,OAAO,CAAC;AACpC;AAkBA,eAAe,UAAU,KAAK;AAC1B,MAAI,mBAAmB;AACvB,QAAMA,QAAO,IAAI;AACjB,MAAI,MAAM,IAAIA,KAAI,GAAG;AACjB,uBAAmB;AACnB,UAAM,OAAOA,KAAI;AAAA,EACrB,WACS,YAAY,IAAIA,KAAI,GAAG;AAC5B,UAAM,oBAAoB;AAC1B,QAAI,kBAAkB,YAAY,KAAK,GAAG;AACtC,kBAAY,OAAOA,KAAI;AACvB,yBAAmB;AAAA,IACvB;AAAA,EACJ;AACA,MAAI,kBAAkB;AAClB,UAAM,QAAQ,IAAI,IAAI,UACjB,aAAa,EACb,IAAI,cAAY,SAAS,OAAO,CAAC,CAAC;AACvC,QAAI,YAAY;AAAA,EACpB;AACJ;AASA,SAAS,gBAAgB,kBAAkBC,UAAS,SAAS;AACzD,MAAI;AAGJ,MAAI,WAAW,KAAK,oBAAoB,gBAAgB,OAAO,QAAQ,OAAO,SAAS,KAAK;AAC5F,MAAI,SAAS;AACT,eAAW,IAAI,OAAO;AAAA,EAC1B;AACA,QAAM,kBAAkB,QAAQ,MAAM,OAAO;AAC7C,QAAM,kBAAkBA,SAAQ,MAAM,OAAO;AAC7C,MAAI,mBAAmB,iBAAiB;AACpC,UAAM,UAAU;AAAA,MACZ,+BAA+B,OAAO,mBAAmBA,QAAO;AAAA,IACpE;AACA,QAAI,iBAAiB;AACjB,cAAQ,KAAK,iBAAiB,OAAO,mDAAmD;AAAA,IAC5F;AACA,QAAI,mBAAmB,iBAAiB;AACpC,cAAQ,KAAK,KAAK;AAAA,IACtB;AACA,QAAI,iBAAiB;AACjB,cAAQ,KAAK,iBAAiBA,QAAO,mDAAmD;AAAA,IAC5F;AACA,WAAO,KAAK,QAAQ,KAAK,GAAG,CAAC;AAC7B;AAAA,EACJ;AACA,qBAAmB,IAAI;AAAA,IAAU,GAAG,OAAO;AAAA,IAAY,OAAO,EAAE,SAAS,SAAAA,SAAQ;AAAA,IAAI;AAAA;AAAA,EAAqC,CAAC;AAC/H;AAQA,SAAS,MAAM,aAAa,SAAS;AACjC,MAAI,gBAAgB,QAAQ,OAAO,gBAAgB,YAAY;AAC3D,UAAM,cAAc;AAAA,MAAO;AAAA;AAAA,IAA0D;AAAA,EACzF;AACA,oBAAkB,aAAa,OAAO;AAC1C;AAUA,SAASC,aAAY,UAAU;AAC3B,cAAc,QAAQ;AAC1B;AAkBA,IAAM,UAAU;AAChB,IAAM,aAAa;AACnB,IAAM,aAAa;AACnB,IAAI,YAAY;AAChB,SAAS,eAAe;AACpB,MAAI,CAAC,WAAW;AACZ,gBAAY,OAAO,SAAS,YAAY;AAAA,MACpC,SAAS,CAAC,IAAI,eAAe;AAMzB,gBAAQ,YAAY;AAAA,UAChB,KAAK;AACD,gBAAI;AACA,iBAAG,kBAAkB,UAAU;AAAA,YACnC,SACO,GAAG;AAIN,sBAAQ,KAAK,CAAC;AAAA,YAClB;AAAA,QACR;AAAA,MACJ;AAAA,IACJ,CAAC,EAAE,MAAM,OAAK;AACV,YAAM,cAAc,OAAO,YAAoC;AAAA,QAC3D,sBAAsB,EAAE;AAAA,MAC5B,CAAC;AAAA,IACL,CAAC;AAAA,EACL;AACA,SAAO;AACX;AACA,eAAe,4BAA4B,KAAK;AAC5C,MAAI;AACA,UAAM,KAAK,MAAM,aAAa;AAC9B,UAAM,KAAK,GAAG,YAAY,UAAU;AACpC,UAAM,SAAS,MAAM,GAAG,YAAY,UAAU,EAAE,IAAI,WAAW,GAAG,CAAC;AAGnE,UAAM,GAAG;AACT,WAAO;AAAA,EACX,SACO,GAAG;AACN,QAAI,aAAa,eAAe;AAC5B,aAAO,KAAK,EAAE,OAAO;AAAA,IACzB,OACK;AACD,YAAM,cAAc,cAAc,OAAO,WAAkC;AAAA,QACvE,sBAAsB,MAAM,QAAQ,MAAM,SAAS,SAAS,EAAE;AAAA,MAClE,CAAC;AACD,aAAO,KAAK,YAAY,OAAO;AAAA,IACnC;AAAA,EACJ;AACJ;AACA,eAAe,2BAA2B,KAAK,iBAAiB;AAC5D,MAAI;AACA,UAAM,KAAK,MAAM,aAAa;AAC9B,UAAM,KAAK,GAAG,YAAY,YAAY,WAAW;AACjD,UAAM,cAAc,GAAG,YAAY,UAAU;AAC7C,UAAM,YAAY,IAAI,iBAAiB,WAAW,GAAG,CAAC;AACtD,UAAM,GAAG;AAAA,EACb,SACO,GAAG;AACN,QAAI,aAAa,eAAe;AAC5B,aAAO,KAAK,EAAE,OAAO;AAAA,IACzB,OACK;AACD,YAAM,cAAc,cAAc,OAAO,WAAoC;AAAA,QACzE,sBAAsB,MAAM,QAAQ,MAAM,SAAS,SAAS,EAAE;AAAA,MAClE,CAAC;AACD,aAAO,KAAK,YAAY,OAAO;AAAA,IACnC;AAAA,EACJ;AACJ;AACA,SAAS,WAAW,KAAK;AACrB,SAAO,GAAG,IAAI,IAAI,IAAI,IAAI,QAAQ,KAAK;AAC3C;AAkBA,IAAM,mBAAmB;AACzB,IAAM,4BAA4B;AAClC,IAAM,uBAAN,MAA2B;AAAA,EACvB,YAAY,WAAW;AACnB,SAAK,YAAY;AAUjB,SAAK,mBAAmB;AACxB,UAAM,MAAM,KAAK,UAAU,YAAY,KAAK,EAAE,aAAa;AAC3D,SAAK,WAAW,IAAI,qBAAqB,GAAG;AAC5C,SAAK,0BAA0B,KAAK,SAAS,KAAK,EAAE,KAAK,YAAU;AAC/D,WAAK,mBAAmB;AACxB,aAAO;AAAA,IACX,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,mBAAmB;AACrB,QAAI,IAAI;AACR,QAAI;AACA,YAAM,iBAAiB,KAAK,UACvB,YAAY,iBAAiB,EAC7B,aAAa;AAGlB,YAAM,QAAQ,eAAe,sBAAsB;AACnD,YAAM,OAAO,iBAAiB;AAC9B,YAAM,KAAK,KAAK,sBAAsB,QAAQ,OAAO,SAAS,SAAS,GAAG,eAAe,MAAM;AAC3F,aAAK,mBAAmB,MAAM,KAAK;AAEnC,cAAM,KAAK,KAAK,sBAAsB,QAAQ,OAAO,SAAS,SAAS,GAAG,eAAe,MAAM;AAC3F;AAAA,QACJ;AAAA,MACJ;AAGA,UAAI,KAAK,iBAAiB,0BAA0B,QAChD,KAAK,iBAAiB,WAAW,KAAK,yBAAuB,oBAAoB,SAAS,IAAI,GAAG;AACjG;AAAA,MACJ,OACK;AAED,aAAK,iBAAiB,WAAW,KAAK,EAAE,MAAM,MAAM,CAAC;AAGrD,YAAI,KAAK,iBAAiB,WAAW,SAAS,2BAA2B;AACrE,gBAAM,uBAAuB,wBAAwB,KAAK,iBAAiB,UAAU;AACrF,eAAK,iBAAiB,WAAW,OAAO,sBAAsB,CAAC;AAAA,QACnE;AAAA,MACJ;AACA,aAAO,KAAK,SAAS,UAAU,KAAK,gBAAgB;AAAA,IACxD,SACO,GAAG;AACN,aAAO,KAAK,CAAC;AAAA,IACjB;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,sBAAsB;AACxB,QAAI;AACJ,QAAI;AACA,UAAI,KAAK,qBAAqB,MAAM;AAChC,cAAM,KAAK;AAAA,MACf;AAEA,YAAM,KAAK,KAAK,sBAAsB,QAAQ,OAAO,SAAS,SAAS,GAAG,eAAe,QACrF,KAAK,iBAAiB,WAAW,WAAW,GAAG;AAC/C,eAAO;AAAA,MACX;AACA,YAAM,OAAO,iBAAiB;AAE9B,YAAM,EAAE,kBAAkB,cAAc,IAAI,2BAA2B,KAAK,iBAAiB,UAAU;AACvG,YAAM,eAAe,8BAA8B,KAAK,UAAU,EAAE,SAAS,GAAG,YAAY,iBAAiB,CAAC,CAAC;AAE/G,WAAK,iBAAiB,wBAAwB;AAC9C,UAAI,cAAc,SAAS,GAAG;AAE1B,aAAK,iBAAiB,aAAa;AAInC,cAAM,KAAK,SAAS,UAAU,KAAK,gBAAgB;AAAA,MACvD,OACK;AACD,aAAK,iBAAiB,aAAa,CAAC;AAEpC,aAAK,KAAK,SAAS,UAAU,KAAK,gBAAgB;AAAA,MACtD;AACA,aAAO;AAAA,IACX,SACO,GAAG;AACN,aAAO,KAAK,CAAC;AACb,aAAO;AAAA,IACX;AAAA,EACJ;AACJ;AACA,SAAS,mBAAmB;AACxB,QAAM,QAAQ,oBAAI,KAAK;AAEvB,SAAO,MAAM,YAAY,EAAE,UAAU,GAAG,EAAE;AAC9C;AACA,SAAS,2BAA2B,iBAAiB,UAAU,kBAAkB;AAG7E,QAAM,mBAAmB,CAAC;AAE1B,MAAI,gBAAgB,gBAAgB,MAAM;AAC1C,aAAW,uBAAuB,iBAAiB;AAE/C,UAAM,iBAAiB,iBAAiB,KAAK,QAAM,GAAG,UAAU,oBAAoB,KAAK;AACzF,QAAI,CAAC,gBAAgB;AAEjB,uBAAiB,KAAK;AAAA,QAClB,OAAO,oBAAoB;AAAA,QAC3B,OAAO,CAAC,oBAAoB,IAAI;AAAA,MACpC,CAAC;AACD,UAAI,WAAW,gBAAgB,IAAI,SAAS;AAGxC,yBAAiB,IAAI;AACrB;AAAA,MACJ;AAAA,IACJ,OACK;AACD,qBAAe,MAAM,KAAK,oBAAoB,IAAI;AAGlD,UAAI,WAAW,gBAAgB,IAAI,SAAS;AACxC,uBAAe,MAAM,IAAI;AACzB;AAAA,MACJ;AAAA,IACJ;AAGA,oBAAgB,cAAc,MAAM,CAAC;AAAA,EACzC;AACA,SAAO;AAAA,IACH;AAAA,IACA;AAAA,EACJ;AACJ;AACA,IAAM,uBAAN,MAA2B;AAAA,EACvB,YAAY,KAAK;AACb,SAAK,MAAM;AACX,SAAK,0BAA0B,KAAK,6BAA6B;AAAA,EACrE;AAAA,EACA,MAAM,+BAA+B;AACjC,QAAI,CAAC,qBAAqB,GAAG;AACzB,aAAO;AAAA,IACX,OACK;AACD,aAAO,0BAA0B,EAC5B,KAAK,MAAM,IAAI,EACf,MAAM,MAAM,KAAK;AAAA,IAC1B;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA,EAIA,MAAM,OAAO;AACT,UAAM,kBAAkB,MAAM,KAAK;AACnC,QAAI,CAAC,iBAAiB;AAClB,aAAO,EAAE,YAAY,CAAC,EAAE;AAAA,IAC5B,OACK;AACD,YAAM,qBAAqB,MAAM,4BAA4B,KAAK,GAAG;AACrE,UAAI,uBAAuB,QAAQ,uBAAuB,SAAS,SAAS,mBAAmB,YAAY;AACvG,eAAO;AAAA,MACX,OACK;AACD,eAAO,EAAE,YAAY,CAAC,EAAE;AAAA,MAC5B;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA,EAEA,MAAM,UAAU,kBAAkB;AAC9B,QAAI;AACJ,UAAM,kBAAkB,MAAM,KAAK;AACnC,QAAI,CAAC,iBAAiB;AAClB;AAAA,IACJ,OACK;AACD,YAAM,2BAA2B,MAAM,KAAK,KAAK;AACjD,aAAO,2BAA2B,KAAK,KAAK;AAAA,QACxC,wBAAwB,KAAK,iBAAiB,2BAA2B,QAAQ,OAAO,SAAS,KAAK,yBAAyB;AAAA,QAC/H,YAAY,iBAAiB;AAAA,MACjC,CAAC;AAAA,IACL;AAAA,EACJ;AAAA;AAAA,EAEA,MAAM,IAAI,kBAAkB;AACxB,QAAI;AACJ,UAAM,kBAAkB,MAAM,KAAK;AACnC,QAAI,CAAC,iBAAiB;AAClB;AAAA,IACJ,OACK;AACD,YAAM,2BAA2B,MAAM,KAAK,KAAK;AACjD,aAAO,2BAA2B,KAAK,KAAK;AAAA,QACxC,wBAAwB,KAAK,iBAAiB,2BAA2B,QAAQ,OAAO,SAAS,KAAK,yBAAyB;AAAA,QAC/H,YAAY;AAAA,UACR,GAAG,yBAAyB;AAAA,UAC5B,GAAG,iBAAiB;AAAA,QACxB;AAAA,MACJ,CAAC;AAAA,IACL;AAAA,EACJ;AACJ;AAMA,SAAS,WAAW,iBAAiB;AAEjC,SAAO;AAAA;AAAA,IAEP,KAAK,UAAU,EAAE,SAAS,GAAG,YAAY,gBAAgB,CAAC;AAAA,EAAC,EAAE;AACjE;AAKA,SAAS,wBAAwB,YAAY;AACzC,MAAI,WAAW,WAAW,GAAG;AACzB,WAAO;AAAA,EACX;AACA,MAAI,uBAAuB;AAC3B,MAAI,wBAAwB,WAAW,CAAC,EAAE;AAC1C,WAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AACxC,QAAI,WAAW,CAAC,EAAE,OAAO,uBAAuB;AAC5C,8BAAwB,WAAW,CAAC,EAAE;AACtC,6BAAuB;AAAA,IAC3B;AAAA,EACJ;AACA,SAAO;AACX;AAkBA,SAAS,uBAAuB,SAAS;AACrC,qBAAmB,IAAI;AAAA,IAAU;AAAA,IAAmB,eAAa,IAAI,0BAA0B,SAAS;AAAA,IAAG;AAAA;AAAA,EAAqC,CAAC;AACjJ,qBAAmB,IAAI;AAAA,IAAU;AAAA,IAAa,eAAa,IAAI,qBAAqB,SAAS;AAAA,IAAG;AAAA;AAAA,EAAqC,CAAC;AAEtI,kBAAgB,QAAQ,WAAW,OAAO;AAE1C,kBAAgB,QAAQ,WAAW,SAAS;AAE5C,kBAAgB,WAAW,EAAE;AACjC;AAQA,uBAAuB,EAAE;;;AC5qCzB,IAAIC,QAAO;AACX,IAAIC,WAAU;AAkBd,gBAAgBD,OAAMC,UAAS,KAAK;;;AC8OpC,SAAS,kBAAkB,UAAU;AACjC,MAAI,aAAa,QAAW;AACxB,iBAAa,yBAAyB,iBAAiB;AACvD,eAAW,OAAO,QAAQ;AAAA,EAC9B;AACA,QAAM,cAAc,SAAS,IAAI,YAAY;AAC7C,SAAO,CAAC,qBAAqB;AACzB,WAAO,IAAI,WAAW,CAAC,uBAAuB;AAE1C,YAAM,aAAa,YAAY,IAAI;AACnC,UAAI,YAAY;AAChB,eAAS,cAAc;AACnB,YAAI,WAAW;AACX;AAAA,QACJ;AACA,mBAAW;AACX,oBAAY;AAAA,MAChB;AACA,YAAM,oBAAoB,iBAAiB,UAAU;AAAA,QACjD,MAAM,CAAC,MAAM;AACT,6BAAmB,KAAK,CAAC;AACzB,sBAAY;AAAA,QAChB;AAAA,QACA,UAAU,MAAM;AACZ,6BAAmB,SAAS;AAC5B,sBAAY;AAAA,QAChB;AAAA,QACA,OAAO,CAAC,MAAM;AACV,6BAAmB,MAAM,CAAC;AAC1B,sBAAY;AAAA,QAChB;AAAA,MACJ,CAAC;AACD,wBAAkB,IAAI,MAAM;AACxB,2BAAmB,YAAY;AAC/B,oBAAY;AAAA,MAChB,CAAC;AACD,aAAO;AAAA,IACX,CAAC;AAAA,EACL;AACJ;;;ACrSA,IAAMC,WAAU,IAAI,QAAQ,sBAAsB;AAIlD,SAAS,sBAAsB,YAAY,UAAU,YAAY;AAC/D,MAAI,UAAU;AAEZ,QAAI,SAAS,WAAW,GAAG;AACzB,aAAO,SAAS,CAAC;AAAA,IACnB;AACA,UAAM,0BAA0B,SAAS,OAAO,QAAM,GAAG,QAAQ,UAAU;AAE3E,QAAI,wBAAwB,WAAW,GAAG;AACxC,aAAO,wBAAwB,CAAC;AAAA,IAClC;AAAA,EACF;AAEA,QAAM,0BAA0B;AAChC,QAAM,WAAW,wBAAwB,UAAU,YAAY,UAAU;AACzE,SAAO,SAAS,aAAa;AAAA,IAC3B,UAAU;AAAA,EACZ,CAAC;AACH;AACA,IAAM,qBAAqB,CAAC,YAAY,QAAQ;AAC9C,QAAM,OAAO,MAAM,CAAC,GAAG,IAAI,QAAQ;AACnC,QAAMC,aAAY,CAAC;AACnB,OAAK,QAAQ,CAAAC,SAAO;AAClB,UAAM,WAAWA,KAAI,UAAU,YAAY,UAAU;AACrD,aAAS,UAAU,QAAQ,cAAY;AACrC,UAAI,CAACD,WAAU,SAAS,QAAQ,GAAG;AACjC,QAAAA,WAAU,KAAK,QAAQ;AAAA,MACzB;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACD,SAAOA;AACT;AAGA,IAAIE;AAAA,CACH,SAAUA,WAAU;AACnB,EAAAA,UAASA,UAAS,QAAQ,IAAI,CAAC,IAAI;AACnC,EAAAA,UAASA,UAAS,MAAM,IAAI,CAAC,IAAI;AACjC,EAAAA,UAASA,UAAS,SAAS,IAAI,CAAC,IAAI;AACtC,GAAGA,cAAaA,YAAW,CAAC,EAAE;AAC9B,IAAI,kBAAkB,UAAU,KAAK,OAAO,SAAS,cAAcA,UAAS,OAAOA,UAAS;AAK5F,IAAM,iBAAN,MAAqB;AAAA,EACnB;AAAA,EACA;AAAA,EACA,YAAY,MAAM,WAAW,gBAAgB;AAC3C,SAAK,OAAO;AACZ,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,MAAM;AACJ,WAAO,KAAK,SAAS,IAAI;AAAA,EAC3B;AAAA,EACA,SAAS,MAAM,OAAO,OAAO;AAC3B,UAAM,aAAa,KAAK;AAGxB,UAAM,aAAa,SAAUC,QAAO;AAClC,UAAI,YAAY;AACd,mBAAW,WAAW,MAAM;AAC1B,eAAK,MAAM,MAAM,CAACA,MAAK,CAAC;AAAA,QAC1B,CAAC;AAAA,MACH,OAAO;AACL,aAAK,MAAM,MAAM,CAACA,MAAK,CAAC;AAAA,MAC1B;AAAA,IACF;AAIA,WAAO,KAAK,SAAS,SAAS,YAAY,OAAO,KAAK;AAAA,EACxD;AACF;AACA,IAAM,yBAAN,MAAM,wBAAuB;AAAA,EAC3B;AAAA,EACA;AAAA,EACA,cAAc;AACZ,UAAM,SAAS,OAAO,MAAM;AAC5B,SAAK,iBAAiB,OAAO,kBAAkB,MAAM,IAAI,eAAe,OAAO,SAAS,cAAc,SAAY,KAAK,OAAO,CAAC;AAC/H,SAAK,gBAAgB,OAAO,IAAI,MAAM,IAAI,eAAe,OAAO,SAAS,cAAc,SAAY,KAAK,SAAS,cAAc,CAAC;AAAA,EAClI;AAAA,EACA,OAAO,OAAO,SAAS,+BAA+B,mBAAmB;AACvE,WAAO,KAAK,qBAAqB,yBAAwB;AAAA,EAC3D;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,wBAAuB;AAAA,IAChC,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AACH,IAAI,gBAAgB;AACpB,SAAS,4BAA4B,UAAU,UAAU;AACvD,MAAI,CAAC,kBAAkB,kBAAkBC,UAAS,UAAU,UAAU,IAAI;AACxE,oBAAgB;AAChB,YAAQ,KAAK,2NAA2N;AAAA,EAC1O;AACA,MAAI,mBAAmB,UAAU;AAC/B,YAAQ,KAAK,kDAAkD,SAAS,IAAI,EAAE;AAAA,EAChF;AACF;AACA,SAAS,kBAAkB,IAAI;AAC7B,QAAM,SAAS,OAAO,QAAQ;AAAA,IAC5B,UAAU;AAAA,EACZ,CAAC;AACD,MAAI,CAAC,QAAQ;AACX,WAAO,GAAG;AAAA,EACZ;AACA,SAAO,OAAO,kBAAkB,MAAM,GAAG,CAAC;AAC5C;AACA,SAAS,IAAI,IAAI;AACf,QAAM,SAAS,OAAO,QAAQ;AAAA,IAC5B,UAAU;AAAA,EACZ,CAAC;AACD,MAAI,CAAC,QAAQ;AACX,WAAO,GAAG;AAAA,EACZ;AACA,SAAO,OAAO,IAAI,MAAM,GAAG,CAAC;AAC9B;AACA,IAAM,aAAa,CAAC,IAAI,UAAU,aAAa;AAC7C,SAAO,IAAI,SAAS;AAClB,QAAI,UAAU;AACZ,iBAAW,UAAU,CAAC;AAAA,IACxB;AACA,WAAO,sBAAsB,UAAU,MAAM,IAAI,MAAM,GAAG,MAAM,QAAM,IAAI,CAAC,CAAC;AAAA,EAC9E;AACF;AACA,IAAM,YAAY,CAAC,IAAI,iBAAiB,aAAa;AACnD,eAAa,kBAAkBA,UAAS,OAAOA,UAAS;AAExD,SAAO,WAAY;AACjB,QAAI;AACJ,UAAM,aAAa;AACnB,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACF,mBAAa,OAAO,sBAAsB;AAC1C,qBAAe,OAAO,YAAY;AAClC,iBAAW,OAAO,mBAAmB;AAAA,IACvC,SAAS,GAAG;AACV,kCAA4B,IAAI,QAAQ;AACxC,aAAO,GAAG,MAAM,MAAM,UAAU;AAAA,IAClC;AAGA,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,UAAI,OAAO,WAAW,CAAC,MAAM,YAAY;AACvC,YAAI,iBAAiB;AACnB,uBAAa,IAAI,MAAM,aAAa,IAAI,CAAC;AAAA,QAC3C;AAEA,mBAAW,CAAC,IAAI,WAAW,WAAW,CAAC,GAAG,UAAU,QAAQ;AAAA,MAC9D;AAAA,IACF;AACA,UAAM,MAAM,kBAAkB,MAAM,GAAG,MAAM,MAAM,UAAU,CAAC;AAC9D,QAAI,CAAC,iBAAiB;AACpB,UAAI,eAAe,YAAY;AAC7B,eAAO,IAAI,KAAK,YAAY,WAAW,cAAc,GAAG,UAAU,WAAW,aAAa,CAAC;AAAA,MAC7F,OAAO;AACL,eAAO,IAAI,MAAM,GAAG;AAAA,MACtB;AAAA,IACF;AACA,QAAI,eAAe,YAAY;AAC7B,aAAO,IAAI,KAAK,YAAY,WAAW,cAAc,GAAG,UAAU,WAAW,aAAa,GAAG,kBAAkB,QAAQ,CAAC;AAAA,IAC1H,WAAW,eAAe,SAAS;AAEjC,aAAO,IAAI,MAAM;AACf,cAAM,aAAa,aAAa,IAAI;AACpC,eAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,cAAI,KAAK,CAAAC,QAAM,sBAAsB,UAAU,MAAM,IAAI,MAAM,QAAQA,GAAE,CAAC,CAAC,GAAG,YAAU,sBAAsB,UAAU,MAAM,IAAI,MAAM,OAAO,MAAM,CAAC,CAAC,CAAC,EAAE,QAAQ,UAAU;AAAA,QAC9K,CAAC;AAAA,MACH,CAAC;AAAA,IACH,WAAW,OAAO,QAAQ,cAAc,UAAU;AAGhD,aAAO,WAAY;AACjB,mBAAW,UAAU,CAAC;AACtB,eAAO,IAAI,MAAM,MAAM,SAAS;AAAA,MAClC;AAAA,IACF,OAAO;AAEL,aAAO,IAAI,MAAM,GAAG;AAAA,IACtB;AAAA,EACF;AACF;;;ACpMA,IAAM,cAAN,MAAkB;AAAA,EAChB,YAAY,KAAK;AACf,WAAO;AAAA,EACT;AACF;AACA,IAAM,eAAN,MAAmB;AAAA,EACjB,cAAc;AACZ,WAAO,QAAU;AAAA,EACnB;AACF;AACA,IAAM,eAAe,MAAM,GAAG,GAAG,EAAE,KAAK,UAAU,MAAM,KAAK,QAAU,CAAC,CAAC,GAAG,SAAS,CAAC;AACtF,SAAS,0BAA0B,UAAU;AAE3C,MAAI,YAAY,SAAS,WAAW,GAAG;AACrC,WAAO,SAAS,CAAC;AAAA,EACnB;AACA,SAAO,IAAI,YAAY,OAAS,CAAC;AACnC;AAIA,IAAM,yBAAyB,IAAI,eAAe,oBAAoB;AAItE,IAAM,gCAAgC;AAAA,EACpC,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,MAAM,CAAC,CAAC,IAAI,SAAS,GAAG,sBAAsB,CAAC;AACjD;AACA,IAAM,yBAAyB;AAAA,EAC7B,SAAS;AAAA,EACT,MAAM,CAAC,CAAC,IAAI,SAAS,GAAG,sBAAsB,CAAC;AACjD;AACA,SAAS,mBAAmB,IAAI;AAC9B,SAAO,CAAC,MAAM,aAAa;AACzB,UAAM,aAAa,SAAS,IAAI,WAAW;AAC3C,oBAAkB,eAAeC,SAAQ,MAAM,MAAM;AACrD,oBAAkB,eAAeA,SAAQ,MAAM,KAAK;AAEpD,oBAAkB,WAAW,QAAU,MAAM,WAAW,SAAS,CAAC;AAClE,UAAM,MAAM,KAAK,kBAAkB,MAAM,GAAG,QAAQ,CAAC;AACrD,WAAO,IAAI,YAAY,GAAG;AAAA,EAC5B;AACF;AACA,IAAM,oBAAN,MAAM,mBAAkB;AAAA;AAAA,EAEtB,YAAY,YAAY;AACtB,oBAAkB,eAAeA,SAAQ,MAAM,MAAM;AACrD,oBAAkB,eAAeA,SAAQ,MAAM,KAAK;AAEpD,oBAAkB,WAAW,QAAU,MAAM,WAAW,SAAS,CAAC;AAAA,EACpE;AAAA,EACA,OAAO,OAAO,SAAS,0BAA0B,mBAAmB;AAClE,WAAO,KAAK,qBAAqB,oBAAsB,SAAS,WAAW,CAAC;AAAA,EAC9E;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,EACR,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,WAAW,CAAC,+BAA+B,sBAAsB;AAAA,EACnE,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,WAAW,CAAC,+BAA+B,sBAAsB;AAAA,IACnE,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,IACpB,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AAIH,SAAS,mBAAmB,OAAO,MAAM;AACvC,SAAO,yBAAyB,CAAC,+BAA+B,wBAAwB;AAAA,IACtF,SAAS;AAAA,IACT,YAAY,mBAAmB,EAAE;AAAA,IACjC,OAAO;AAAA,IACP,MAAM,CAAC,QAAQ,UAAU,wBAAwB,GAAG,IAAI;AAAA,EAC1D,CAAC,CAAC;AACJ;AAGA,IAAMC,aAAY,UAAU,WAAa,IAAI;AAC7C,IAAMC,UAAS,UAAU,QAAU,IAAI;AACvC,IAAMC,WAAU,UAAU,SAAW,IAAI;AACzC,IAAMC,iBAAgB,UAAU,eAAiB,IAAI;AACrD,IAAMC,uBAAsB,UAAU,qBAAuB,IAAI;AACjE,IAAMC,SAAQ,UAAU,OAAS,IAAI;AACrC,IAAMC,mBAAkB,UAAU,iBAAmB,IAAI;AACzD,IAAMC,eAAc,UAAUA,cAAe,IAAI;", "names": ["name", "name", "querystring", "name", "LogLevel", "name", "instance", "name", "version", "target", "DEFAULT_ENTRY_NAME", "name", "version", "setLogLevel", "name", "version", "VERSION", "instances", "app", "LogLevel", "state", "LogLevel", "it", "VERSION", "deleteApp", "getApp", "getApps", "initializeApp", "initializeServerApp", "onLog", "registerVersion", "setLogLevel"]}