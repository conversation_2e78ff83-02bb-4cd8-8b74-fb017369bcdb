{"version": 3, "sources": ["src/app/components/loading.component.ts"], "sourcesContent": ["\n    .loading-container {\n      position: fixed;\n      top: 0;\n      left: 0;\n      width: 100%;\n      height: 100%;\n      background-color: rgba(255, 255, 255, 0.9);\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      z-index: 9999;\n    }\n    \n    .loading-content {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      gap: 1rem;\n    }\n    \n    .loading-text {\n      margin: 0;\n      font-size: 1rem;\n      color: #525252;\n    }\n  "], "mappings": ";AACI,CAAA;AACE,YAAA;AACA,OAAA;AACA,QAAA;AACA,SAAA;AACA,UAAA;AACA,oBAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,WAAA;AACA,mBAAA;AACA,eAAA;AACA,WAAA;;AAGF,CAAA;AACE,WAAA;AACA,kBAAA;AACA,eAAA;AACA,OAAA;;AAGF,CAAA;AACE,UAAA;AACA,aAAA;AACA,SAAA;;", "names": []}